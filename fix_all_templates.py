#!/usr/bin/env python3
"""
<PERSON>ript to fix all template files with render_video_with_hls
"""

import os
import re

def fix_template_file(file_path):
    """Fix template syntax in a single file"""
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # Pattern to match {% with object|render_video_with_hls:params as variable %}
    pattern = r'{%\s*with\s+([^|]+)\|render_video_with_hls:([^%]+)\s+as\s+([^%]+)\s*%}'
    
    def replace_match(match):
        object_name = match.group(1).strip()
        params = match.group(2).strip()
        variable_name = match.group(3).strip()
        
        # Convert comma-separated params to space-separated
        # Remove quotes around parameter names but keep quotes around values
        params_parts = []
        for param in params.split(','):
            param = param.strip()
            if '=' in param:
                key, value = param.split('=', 1)
                key = key.strip()
                value = value.strip()
                params_parts.append(f'{key}={value}')
            else:
                params_parts.append(param)
        
        params_fixed = ' '.join(params_parts)
        
        return f'{{% render_video_with_hls {object_name} {params_fixed} as {variable_name} %}}'
    
    # Replace the pattern
    content = re.sub(pattern, replace_match, content)
    
    # Remove corresponding {% endwith %} tags that are now orphaned
    # This is a simple approach - remove {% endwith %} that appear after our replacements
    lines = content.split('\n')
    new_lines = []
    skip_endwith = False
    
    for line in lines:
        if 'render_video_with_hls' in line and not line.strip().startswith('{%'):
            # This is the new syntax line
            skip_endwith = True
            new_lines.append(line)
        elif skip_endwith and line.strip() == '{% endwith %}':
            # Skip this endwith
            skip_endwith = False
            continue
        else:
            new_lines.append(line)
    
    content = '\n'.join(new_lines)
    
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"Fixed: {file_path}")
        return True
    else:
        print(f"No changes needed: {file_path}")
        return False

def main():
    """Fix all template files"""
    # Get all template files that contain render_video_with_hls
    import subprocess
    
    try:
        result = subprocess.run(['grep', '-r', '-l', 'render_video_with_hls', 'app/templates/', '--include=*.html'], 
                              capture_output=True, text=True, cwd='/home/<USER>/amela/soremo/soremo-github/soremo-soundcheck')
        
        if result.returncode == 0:
            files = result.stdout.strip().split('\n')
            files = [f for f in files if f.strip()]  # Remove empty lines
            
            print(f"Found {len(files)} files to fix:")
            for file_path in files:
                print(f"  {file_path}")
            
            print("\nFixing files...")
            fixed_count = 0
            for file_path in files:
                if fix_template_file(file_path):
                    fixed_count += 1
            
            print(f"\nFixed {fixed_count} files")
        else:
            print("No files found with render_video_with_hls")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == '__main__':
    main()
