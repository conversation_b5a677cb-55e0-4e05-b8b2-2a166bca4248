# HLS Video Streaming Integration

## Tổng quan

Dự án này đã được cập nhật để hỗ trợ HLS (HTTP Live Streaming) cho tất cả các video files. Khi có file chuyển đổi HLS từ AWS MediaConvert, hệ thống sẽ tự động sử dụng HLS thay vì video gốc.

**Trạng thái hiện tại**: ✅ Code integration hoàn thành, sẵn sàng sử dụng khi có MediaConvert jobs.

## Các thay đổi chính

### 1. Model MediaConvertJob
- Đã có sẵn model để lưu trữ thông tin chuyển đổi
- Các trường quan trọng:
  - `original_object_key`: Key file gốc
  - `converted_media_key`: Key file HLS đã chuyển đổi
  - `status`: Trạng thái chuyển đổi

### 2. Template Tag mới
- **`render_video_with_hls`**: Template tag để render video với HLS support
- Tự động kiểm tra MediaConvert job và sử dụng HLS nếu có
- Fallback về video gốc nếu không có HLS

### 3. API Updates
- **`add_mediaconvert_data_to_files()`**: Thêm HLS data vào file lists
- Tất cả API endpoints trả về file data đã được cập nhật

### 4. JavaScript Enhancements
- **`initializeHLSVideo()`**: Function để khởi tạo HLS player
- **`previewFile()`**: Cập nhật để hỗ trợ HLS trong modal popup
- Auto-initialize HLS videos khi DOM ready và sau AJAX calls

### 5. Template Updates
Các template đã được cập nhật để sử dụng HLS:
- `app/templates/top/_video_item_component.html`
- `app/templates/top/_video-modal__videos.html`
- `app/templates/top/_process_bottom_video.html`
- `app/templates/top/_process_top_video.html`
- `app/templates/top/_list_update_video.html`
- `app/templates/scene/detail.html`
- `app/templates/top/_item_send_2.html`
- `app/templates/top/_item_received_2.html`
- `app/templates/top/_item_send.html`
- `app/templates/top/_file_infor.html`
- `app/templates/creator/topic_detail.html`
- `app/templates/creator/_detail_topic_in_modal.html`
- `app/templates/product/order_file_delete.html`

### 6. View Updates
Các view đã được cập nhật để xử lý HLS data:
- `TopicDetailView` - Thêm HLS data cho TopicGallery objects
- `get_topic_detail` - Thêm HLS data cho modal popup
- `load_more_list_topics` - Thêm HLS data cho topic lists

## Cách sử dụng

### 1. Trong Template
```django
{% load util %}

<!-- Sử dụng template tag mới -->
{% with scene|render_video_with_hls:width="100%",height="auto",poster=scene|get_thumbnail,preload="none",id="video_"|add:scene.pk as video_html %}
  {% if video_html %}
    {{ video_html }}
  {% else %}
    <!-- Fallback video cũ -->
    <video width="100%" height="auto" poster="{{ scene|get_thumbnail }}" preload="none">
      <source src="{{ scene.movie.url }}" type="video/mp4">
    </video>
  {% endif %}
{% endwith %}
```

### 2. Trong JavaScript
```javascript
// Auto-initialize tất cả HLS videos
initializeAllHLSVideos();

// Initialize một video cụ thể
const video = document.getElementById('my-video');
initializeHLSVideo(video);
```

### 3. Trong API Response
```python
# API sẽ tự động thêm HLS data
comment_files = add_mediaconvert_data_to_files(comment_files)

# Kết quả sẽ có thêm:
# - has_hls: boolean
# - hls_url: string (nếu có)
# - conversion_status: string
```

## Luồng hoạt động

1. **Upload video**: User upload video file
2. **MediaConvert job**: AWS MediaConvert xử lý và tạo HLS
3. **Database update**: MediaConvertJob được cập nhật với status 'completed'
4. **Frontend display**: 
   - Template tag kiểm tra MediaConvert job
   - Nếu có HLS → sử dụng HLS player
   - Nếu không → fallback về video gốc

## Testing

Chạy test script để kiểm tra integration:

```bash
python test_hls_integration.py
```

## Cấu hình cần thiết

### 1. Settings
Đảm bảo có cấu hình AWS S3:
```python
AWS_S3_CUSTOM_DOMAIN = 'your-cloudfront-domain.com'
```

### 2. Frontend Dependencies
Đảm bảo HLS.js được load:
```html
<script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
```

## Troubleshooting

### 1. Video không play HLS
- Kiểm tra MediaConvertJob status = 'completed'
- Kiểm tra converted_media_key có giá trị
- Kiểm tra HLS.js được load đúng

### 2. Fallback không hoạt động
- Kiểm tra data-original-src attribute
- Kiểm tra error handling trong JavaScript

### 3. Performance issues
- HLS có thể cần thời gian để load manifest
- Sử dụng preload="metadata" thay vì "auto"

## Lưu ý quan trọng

1. **Backward compatibility**: Tất cả video cũ vẫn hoạt động bình thường
2. **Progressive enhancement**: HLS chỉ được sử dụng khi có sẵn
3. **Error handling**: Có fallback về video gốc khi HLS lỗi
4. **Performance**: HLS được optimize với buffering và quality adaptation

## Monitoring

Để monitor HLS performance:
1. Kiểm tra MediaConvertJob status trong admin
2. Monitor HLS errors trong browser console
3. Theo dõi video loading times

## Future Enhancements

1. **Quality selection**: Cho phép user chọn quality
2. **Analytics**: Track HLS usage và performance
3. **Caching**: Implement HLS manifest caching
4. **Mobile optimization**: Optimize cho mobile devices
