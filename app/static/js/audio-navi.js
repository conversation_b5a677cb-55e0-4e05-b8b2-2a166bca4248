var wavesurfer;
var last_interact = 0;
$(document).ready(function () {
    wavesurfer = WaveSurfer.create({
        container: '.audio-navi-wave',
        waveColor: '#b1b1b1',
        progressColor: '#000',
        cursorColor: 'rgba(0, 0, 0,0.29)',
        barWidth: 2,
        barHeight: 6,
        barRadius: 2,
        cursorWidth: 2,
        barGap: 4,
        mediaControls: false,
        height: 26,
        responsive: true,
        hideScrollbar: true,
        backend: 'MediaElement',
    });

    wavesurfer.on('audioprocess', function(time) {
        $('.audio-navi .audio-navi-time').text(msToTime(wavesurfer.getCurrentTime()));
    })

    wavesurfer.on('finish', function(time) {
        playPauseInNavigation('', 'play')
    })

    $(document).on('click', '.audio-navi-playpause', function() {
        playPauseInNavigation('click', '')
    })

    // action click icon bookmark
    $(document).on('click', '.audio-navi-titlebar-bookmark', function(e) {
        let saleId = $('.audio-navi').attr('data-id');
        if (is_logged_in != 'True') {
            window.location.href = '/accounts/signup?next_url=/collection/bookmark_sale_content?sale_id=' + saleId
        } else if ((['admin', 'master_client']).includes(user_role)) {
            let icon_bookmark = $(this).find('span.fa-bookmark')
            let type_bookmark = icon_bookmark.is('.icon--sicon-bookmark-o') ? 'bookmark' : 'unbookmark'
            // call ajax bookmark sale content

            $.ajax({
                type: "POST",
                datatype: "json",
                url: "/scene/bookmark_scene_title",
                data: {
                    'sale_id': saleId,
                    'type_bookmark': 'sale',
                    'type_action': type_bookmark,
                },
                success: function (data) {
                    let bookmark_state = data.type_bookmark === 'bookmark' ? 'True' : 'False'
                    if(saleId) {
                        let item = $('.list-new-works__item-container[data-sale-id=' + saleId + '] audio')
                        if(item.length) {
                            item.parents('.list-new-works__item-container').attr('data-bookmark-state', bookmark_state)
                        } else {
                            item = $('.sample-audio-item[data-sale-id=' + saleId + ']').attr('data-bookmark-state', bookmark_state)
                        }
                    }

                    if (data.type_bookmark === 'bookmark') {
                        icon_bookmark.removeClass("icon--sicon-bookmark-o").addClass("icon--sicon-bookmark")
                        var style = document.querySelector(':root');
                        if(saleId) {
                            let item = $('.list-new-works__item-container[data-sale-id=' + saleId + '] audio')
                            let img;
                            if(item.length) {
                                img = item.parents('.list-new-works__media.gallery__item').css('background')
                            } else {
                                img = $('.sample-audio-item[data-sale-id=' + saleId + '] .sample-audio-thumbnail').css('background')
                            }

                            style.style.setProperty('--bg-album', img);
                        }
                        $('.icon-bookmark-navbar').addClass('showing')
                        setTimeout(() => {
                            $('.icon-bookmark-navbar').addClass('add')
                        }, 600)

                        setTimeout(() => {
                            $('.icon-bookmark-navbar').removeClass('add showing')
                            style.style.setProperty('--bg-album', '#a7a8a9');
                        }, 1000)
                    } else {
                        icon_bookmark.removeClass("icon--sicon-bookmark").addClass("icon--sicon-bookmark-o")
                    }
                },
                error: function () {
                    toastr.error('エラーが発生しました');
                }
            })
            return
        }
    })

    $(document).on('click', '.audio-navi-titlebar-copylink', function(e) {
        let id = $('.audio-navi').attr('data-id')
        let item = $('.list-new-works__item-container[data-sale-id=' + id + '] audio')
        let url = window.location.href
        if(item.length) {
            id = item.parents('.list-new-works__item-container').attr('data-sale-id')
            url = window.location.origin + item.parents('.list-new-works__item-container').find('.list-new-works__sub-title').attr('data-url') + '?playing_id=' + id
        } else {
            let cur_url = new URL(url);
            cur_url.searchParams.set('playing_id', id)
            url = cur_url.href
        }

        if(navigator.clipboard) {
            navigator.clipboard.writeText(url)
            showMessageCopied()
        } else {
            fallbackCopy(url)
        }
    })
})

function playPauseInNavigation(from, type, audio=false) {
    if (is_logged_in === "True" && ['master_admin', 'curator'].includes(user_role)) {
        $('.audio-navi-titlebar-bookmark span').remove()
    }
    let id = $('.audio-navi').attr('data-id')
    last_interact++
    if(type == '') {
        type = wavesurfer.isPlaying() ? 'pause' : 'play'
    }

    if(type=='play') {
        if(!$('.audio-navi').is('.showing')) {
            $('.audio-navi').addClass('showing')
            $('footer').addClass('add-margin-to-footer')
        }

        $('.playing audio, video').each(function(i, e) {
            e.pause()
            $(e).parents('.sample-audio-thumbnail, .exhibition-works__component-content, .list-circle__component, .list-new-works__media.gallery__item').removeClass('playing loading')
            if($(e).parents('.list-topics__content-bottom__list-circle, .section-content__list-media').length && $(this).parents('.list-circle__component').attr('process-status') === 'play') {
                $(this).parents('.list-circle__component').attr('process-status', 'pause');
                $(this).parents('.list-circle__component').css({'border': '1px solid #F0F0F0'});
                $(this).parents('.list-circle__component').attr('process-data', parseInt($(e).get(0).currentTime / $(e).get(0).duration * 100));
            }
        })

        if(audio) {
            if(audio.parents('.list-new-works__media').length) {
                let audio_id = audio.parents('.list-new-works__item-container').attr('data-sale-id')
                let item = $('.list-new-works__item-container[data-sale-id=' + audio_id + '] audio')
                if(item.length) {
                    item.parents('.list-new-works__media.gallery__item').addClass('playing-navi')
                }
                if(id != audio_id) {
                    let name = audio.attr('data-name')
                    let title = audio.parents('.list-new-works__media.gallery__item').attr('data-artist')
                    let bookmark_state = audio.parents('.list-new-works__item-container').attr('data-bookmark-state')

                    $('.audio-navi').attr('data-id', audio_id)
                    $('.audio-navi-titlebar-info-title').text(name)
                    $('.audio-navi-titlebar-info-name').text(title)

                    if(bookmark_state == 'False') {
                        $('.audio-navi-titlebar-bookmark span').addClass('icon--sicon-bookmark-o').removeClass('icon--sicon-bookmark')
                    } else {
                        $('.audio-navi-titlebar-bookmark span').addClass('icon--sicon-bookmark').removeClass('icon--sicon-bookmark-o')
                    }

                    let src = audio[0].src
                    audio.attr('crossorigin', 'anonymous')
                    if(!src.includes('&v=1')) {
                        audio.attr('src', src + '&v=1')
                    }

                    audio.off('canplay').on('canplay', function() {
                        audio.off('canplay')
                        if($('audio-navi').is('.showing') && !audio.parents('.playing-navi').length) {
                            audio[0].pause()
                        }
                    })

                    wavesurfer.load(audio[0]);
                }
                wavesurfer.play();
                $('.audio-navi-playpause .audio-navi-play').addClass('hide');
                $('.audio-navi-playpause .audio-navi-pause').removeClass('hide');
            } else {
                let audio_id = audio.parents('.sample-audio-item').attr('data-sale-id')
                $('.sample-audio-item[data-sale-id=' + audio_id + ']').addClass('playing-navi')
                if(id != audio_id) {
                    let info = audio.parents('.sample-audio-item').find('.sample-audio-info .sample-audio-title')
                    $('.audio-navi').attr('data-id', audio_id)
                    $('.audio-navi-titlebar-info-title').text(info.attr('data-title'))
                    $('.audio-navi-titlebar-info-name').text($('.header-fullscreen').attr('data-artist-name'))
                    let bookmark_state = audio.parents('.sample-audio-item').attr('data-bookmark-state')

                    if(bookmark_state == 'False') {
                        $('.audio-navi-titlebar-bookmark span').addClass('icon--sicon-bookmark-o').removeClass('icon--sicon-bookmark')
                    } else {
                        $('.audio-navi-titlebar-bookmark span').addClass('icon--sicon-bookmark').removeClass('icon--sicon-bookmark-o')
                    }

                    let src = audio[0].src
                    if(src.includes('?')) {
                        audio.attr('crossorigin', 'anonymous')
                        if(!src.includes('&v=1')) {
                            audio.attr('src', src + '&v=1')
                        }
                    }

                    audio.off('canplay').on('canplay', function() {
                        audio.off('canplay')
                        if($('audio-navi').is('.showing') && !audio.parents('.playing-navi').length) {
                            audio[0].pause()
                        }
                    })

                    wavesurfer.load(audio[0]);
                }
                wavesurfer.play();
                $('.audio-navi-playpause .audio-navi-play').addClass('hide');
                $('.audio-navi-playpause .audio-navi-pause').removeClass('hide');
            }
        } else {
            wavesurfer.play();
            if(id) {
                let item = $('.list-new-works__item-container[data-sale-id=' + id + '] audio')
                if(item.length) {
                    item.parents('.list-new-works__media.gallery__item').addClass('playing-navi')
                } else {
                    item = $('.sample-audio-item[data-sale-id=' + id + ']').addClass('playing-navi')
                }
            }

            $('.audio-navi-playpause .audio-navi-play').addClass('hide');
            $('.audio-navi-playpause .audio-navi-pause').removeClass('hide');
        }
    } else {
        if(audio) {
            let audio_id = audio.parents('.sample-audio-item').attr('data-sale-id')
            if(audio.parents('.list-new-works__media').length) {
                audio_id = audio.parents('.list-new-works__item-container').attr('data-sale-id')
            }
            if(id != audio_id) {
                audio[0].pause()
            } else {
                $('.audio-navi-playpause .audio-navi-play').removeClass('hide');
                $('.audio-navi-playpause .audio-navi-pause').addClass('hide');
                wavesurfer.pause();
            }

            $('.playing-navi[data-sale-id=' + audio_id + ']').removeClass('playing-navi')
            let item = $('.list-new-works__item-container[data-sale-id=' + audio_id + ']' + ' .playing-navi audio')
            if(item.length) {
                item.parents('.playing-navi').removeClass('playing-navi')
                item.each(function(i,e) {
                    e.pause()
                })
            }
        } else {
            if(id) {
                $('.playing-navi[data-sale-id=' + id + ']').removeClass('playing-navi')
                let item = $('.list-new-works__item-container[data-sale-id=' + id + ']' + ' .playing-navi audio')
                if(item.length) {
                    item.parents('.playing-navi').removeClass('playing-navi')
                    item.each(function(i,e) {
                        e.pause()
                    })
                }
            }
            $('.audio-navi-playpause .audio-navi-play').removeClass('hide');
            $('.audio-navi-playpause .audio-navi-pause').addClass('hide');
            wavesurfer.pause();
        }
    }

    if(!wavesurfer.isPlaying()) {
        checkNoInteract();
    }
}

function checkNoInteract () {
    let current = last_interact
    setTimeout(() => {
        if(current == last_interact && !wavesurfer.isPlaying()) {
            $('.audio-navi').removeClass('showing')
            $('footer').removeClass('add-margin-to-footer')
            $('.audio-navi').attr('data-id', '')
        }
    }, 10000)
}

function playFromURL() {
    let cur_url = new URL(window.location.href);
    let playing_id = cur_url.searchParams.get('playing_id')
    if(playing_id) {
        let item = $('.list-new-works__item-container[data-sale-id=' + playing_id + '] .list-search__item-playpause')
        if(item.length) {
            item.parents('.list-new-works__media.gallery__item').first().trigger('click')
        } else {
            item = $('.sample-audio-item[data-sale-id=' + playing_id + '] .sample-audio-playpause-button').first()
            item.trigger('click')
            setTimeout(() => {
                item.trigger('click')
            }, 100)

            cur_url.searchParams.delete('autoplay')
            cur_url.searchParams.delete('playing_id')
            window.history.replaceState(null, null, cur_url);
        }
    }
}

function showMessageCopied() {
    $('.audio-navi-titlebar-copylink').addClass('copied')
    setTimeout(() => {
        $('.audio-navi-titlebar-copylink').removeClass('copied')
    }, 3000)
}

function fallbackCopy(url) {
    const temp = document.createElement("div");
    temp.style.position = 'fixed';
    temp.style.bottom= 0;
    temp.style.left= 0;
    temp.setAttribute("contentEditable", true);
    temp.innerHTML = url;
    temp.setAttribute("onfocus", "document.execCommand('selectAll',false,null)");
    document.body.appendChild(temp);
    temp.focus();
    document.execCommand("copy");
    document.body.removeChild(temp);
    showMessageCopied()
}
