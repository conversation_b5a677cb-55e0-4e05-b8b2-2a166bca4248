var $dropzoneUploadNDA = [];
var real_name_nda = '';
let nda_file = '';
let is_uploading_nda = false;
let is_delete_file = false;

Dropzone.autoDiscover = false;

// Tab Skills
function activeTab() {
    // $('.tabs-skill').tab('show');
}

// Count skill selected
function countSkills(tabId, countSelected) {
    tabId = $('.tab-pane.active').attr('id');
    countSelected = $(`#${tabId}`).find('.skills-item.selected').length;
    if (countSelected > 0) {
        $('.nav-item.active').find('.skill-selected').removeClass('hide')
    } else {
        $('.nav-item.active').find('.skill-selected').addClass('hide')
    }
    $('.nav-item.active').find('.skill-selected').attr('value', countSelected).text(countSelected)
}

// Selected Skill
function selectSkill() {
    isSelected = $(this).hasClass('selected');
    let dataId = $(this).attr('data-id');
    let checkbox = $(`#skill_${dataId}`);
    let tabId = $(this).parents().find('.tab-pane.active').attr('id');
    let count;

    if (isSelected) {
        $(this).removeClass('selected');
        checkbox.removeAttr('checked');
        count--;
    } else {
        $(this).addClass('selected');
        checkbox.attr('checked', 'checked');
        count++;
    }
    countSkills(tabId, count);
}

function inviteArtist() {
    $(document).on('click', '.account-management__add-artist', function () {
        $('#modal-invite-artist').modal('show');
    });
}

function uploadNDA() {
    dragDropNDA();

    $(document).on('click', '.account-info__right__manage__NDA', function () {
        $('#modal-upload-NDA').modal('show');
        let user_id = $(this).parents('.tr-artist-account-info').attr('data-user-id');
        $('#modal-upload-NDA').attr('data-user', user_id);
        getCuratorFileForArtist($(this));
        $('#modal-upload-NDA').css({'display': 'flex', 'align-items': 'center'});
    });


    //ajax upload curator file
    $(document).on('click', '#modal-upload-NDA .btn-upload-NDA', function () {
        let data = new FormData();
        let user_id = $('#modal-upload-NDA').attr('data-user');
        data.append('file', nda_file);
        data.append('file_name', real_name_nda);
        data.append('delete', is_delete_file);
        data.append('user_id', user_id);
        $.ajax({
            type: "POST",
            data: data,
            contentType: false,
            processData: false,
            url: '/accounts/curator/curator_upload_file',
            beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
            },
            success: function (data) {
                $('#modal-upload-NDA').modal('hide');
                let labelNDA = $('.tr-artist-account-info[data-user-id=' + user_id + ']').find('.account-info__right__manage__NDA');
                labelNDA.toggleClass('NDA-not-uploaded', !data.curator_file_name);
                labelNDA.attr('data-curator-file-name', data.curator_file_name);
                labelNDA.attr('data-curator-file-link', data.curator_file_link);
            },
        });
    });

    // reset dropzone
    $('#modal-upload-NDA').on('hidden.bs.modal', function () {
        $dropzoneInformation.removeAllFiles();
        $(this).attr('data-user', '');
        $('#modal-upload-NDA .mattach-info-file .file-name').text('');
        $('#modal-upload-NDA .mattach-info-file').attr('data-link', '');
        $('#modal-upload-NDA .mattach-info-file').addClass('hide');
        is_delete_file = false;
        nda_file = '';
        real_name_nda = '';
    });

    $(document).on('click', '#modal-upload-NDA .mattach-info-file-information',function () {
        let linkFile = $(this).attr('data-link');
        window.open(linkFile)
    })

}

function getCuratorFileForArtist(labelNDA) {
    let fileName = labelNDA.attr('data-curator-file-name');
    if (fileName) {
        $('#modal-upload-NDA .mattach-info-file .file-name').text(fileName);
        $('#modal-upload-NDA .mattach-info-file').removeClass('hide');
        $('#modal-upload-NDA .mattach-info-file').attr('data-link', labelNDA.attr('data-curator-file-link'))
    }
}

function dragDropNDA() {
    let thisModal = $('#modal-upload-NDA');
    if (thisModal.find('#dropzoneUpLoadNDA').length) {
        var previewNode = thisModal.find('.mattach-template-form');
        var previewTemplate = previewNode.parent().html();
        previewNode.parent().empty();

        $dropzoneInformation = new Dropzone('#dropzoneUpLoadNDA', {
            maxFilesize: 4500,
            timeout: 900000,
            autoDiscover: false,
            previewsContainer: '#modal-upload-NDA .mattach-previews-form',
            previewTemplate: previewTemplate,
            url: "/",
            autoProcessQueue: false,
            autoQueue: false,
            clickable: '#dropzoneUpLoadNDA',
            maxFiles: 1,
            dictDefaultMessage: '<i class="icon icon--sicon-add-cirlce"></i>\n' + '<p>ファイルを選択</p>'
        });

        $dropzoneInformation.on("maxfilesexceeded", function (file) {
            $dropzoneInformation.removeAllFiles();
            $dropzoneInformation.addFile(file);
        });

        $dropzoneInformation.on('removedfile', function (file) {
            real_name_nda = '';
        });

        $dropzoneInformation.on('addedfile', function (file, e) {
            let file_dom = $(file.previewElement);
            real_name_nda = file.name;
            let file_preview = thisModal.find('.mattach-preview-container-form').find(".mcommment-file__name-form");
            for (let i = 0; i < file_preview.length; i++) {
                $(file_preview[i]).text('');
                $(file_preview[i]).append('<i class="icon icon--sicon-clip"></i>' + real_name_nda);
                break;
            }
            thisModal.find(".mattach-info-file").addClass("hide");
            if (thisModal.find(".mattach-template").length == 2) {
                $(thisModal.find(".mattach-template")[0]).remove();
            }
            file_name = this.files[0].name;
            if (thisModal.find('.account__file').length) {
                thisModal.find('.account__file').hide();
                thisModal.find('.account__file').next().hide();
            }
            // $('.btn-upload-NDA').removeClass('disabled');
            curatorUploadFileS3(file, file_dom);
        });

        thisModal.find('.mattach-info-file .icon--sicon-close').on('click', function (e) {
            e.stopPropagation();
            thisModal.find('.mattach-info-file').addClass('hide');
            is_delete_file = true;
        })
    }
}

function curatorUploadFileS3(file, file_dom) {
    file_dom.find('.determinate').css('width', '0%');
    $.ajax({
        type: "GET",
        datatype: "json",
        url: "/get_presigned_url",
        data: {
            'file_name': "file/" + file.name,
            'file_type': file.type,
        },
        success: function (data) {
            let url = data.presigned_post.url;
            nda_file = data.presigned_post.fields["key"];
            let postData = new FormData();
            for (key in data.presigned_post.fields) {
                postData.append(key, data.presigned_post.fields[key]);
            }
            postData.append('file', file);
            xhr = new XMLHttpRequest();
            xhr.open('POST', url);
            xhr.upload.addEventListener("progress", function (evt) {
                if (evt.lengthComputable) {
                    let percentComplete = (evt.loaded / evt.total) * 70 + '%';
                    file_dom.find('.determinate').css('transition', '0');
                    file_dom.find('.determinate').css('transition', '1s');
                    file_dom.find('.determinate').css('width', percentComplete);
                    if ($('.upload-button-wrapper') && $('.upload-button-wrapper').hasClass("clicked")){
                        $('.upload-button-wrapper').css('display', 'flex');
                        $('.upload-button-wrapper').addClass('clicked');
                        $('.upload-button-wrapper .fill .process').css('width', percentComplete + '%');
                    }
                }
            }, false);
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200 || xhr.status === 204) {
                        file_dom.find('.determinate').css('width', "100%");
                        is_uploading_nda = false;
                    }
                }
            };
            is_uploading_nda = true;
            xhr.send(postData);
        }
    })
}

let is_check_file = false;

function approvalOfIndentity() {
    $(document).on('click', '.account-info__right__manage__identification', function () {
        $('#modal-approval-of-indentity').modal('show');
        let userDom = $(this).parents('.tr-artist-account-info');
        let user_id = userDom.attr('data-user-id');
        $('#modal-approval-of-indentity').attr('data-user', user_id);
        getArtistFile(user_id)
    });

    function getArtistFile(user_id) {
        $.ajax({
            type: 'POST',
            url: '/accounts/curator/get_artist_files',
            data: {
                'user_id': user_id,
            },
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                $('#modal-approval-of-indentity .indentity-container').empty();
                $('#modal-approval-of-indentity .indentity-container').append(response.html);
                if ($('#modal-approval-of-indentity .indentity-container .indentity-component-container').length) {
                    $('#modal-approval-of-indentity .indentity-container .indentity-component-container').first().trigger('click')
                }
            },
            error: function () {
            },
            complete: function () {
            }
        });
    }

    previewFile();

    curatorCheckCreatorFile();


    function previewFile() {
        $(document).on('click', '.indentity-container .indentity-component-container', function () {
            let dataType = $(this).attr('data-type');
            let dataSource = $(this).attr('data-source');

            if (!$(this).hasClass('active')) {
                $(this).parents('.indentity-container').find('.active').removeClass('active');
                $(this).addClass('active');

                $(this).parents('.popup-body').find('.indentity-content-view-container').remove();
                if (dataType === 'image') {
                    $(`<div class="indentity-content-view-container mscrollbar"><img src="${dataSource}" alt="image-preview"></div>`).insertAfter($(`.indentity-container`))
                } else if (dataType === 'document') {
                    $(`<div class="indentity-content-view-container mscrollbar"><iframe src="/static/pdfjs/web/viewer.html?file=${encodeURIComponent(dataSource) + '#zoom=page-width'}" frameborder="0" style="min-height: 450px"></iframe></div>`).insertAfter($('.indentity-container'))
                }
            }
        })
    }

    function curatorCheckCreatorFile() {
        $(document).on('click', '#modal-approval-of-indentity .btn-popup-send', function () {
            let buttonDom = $(this);
            let user_id = buttonDom.parents('#modal-approval-of-indentity').attr('data-user');
            let status = buttonDom.hasClass('btn-approval-of-indentity') ? 'accept' : 'reject';
            let userDom = $('.tr-artist-account-info[data-user-id=' + user_id + ']');
            if (is_check_file || !user_id) {
                return
            }
            if (!is_check_file) {
                is_check_file = true;
                $.ajax({
                    type: 'POST',
                    url: '/accounts/curator/curator_approve_reject_file',
                    datatype: "json",
                    data: {
                        'user_id': user_id,
                        'status': status,
                    },
                    beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                        $('#modal-approval-of-indentity').modal('hide');
                        // update label
                        let labelDom = userDom.find('.account-info__right__manage__identification');
                        labelDom.removeClass('idetity-uploaded')
                    },
                    error: function () {
                    },
                    complete: function () {
                        is_check_file = false
                    }
                });
            }
        })
    }
}

// function editMaterialsUsed() {
//     $(document).on('click', '.materials-used__content', function () {
//         let userDom = $(this).parents('.tr-artist-account-info');
//         $('#modal-edit-materials-used').attr('data-user', userDom.attr('data-user-id'));
//         $('#modal-edit-materials-used').modal('show');
//         if(window.innerWidth > 992){
//             $('#modal-edit-materials-used').css({'display': 'flex', 'align-items': 'center'});
//         }
//         let currentValue = userDom.find('.materials-used__content span').text();
//         $('input[name=materials-used-input]').val(currentValue);
//     });
//
//     const numberRegex = new RegExp("^[0-9]+(\.[0-9][0-9]?)?$");
//
//     function checkMaterialUsedNumber() {
//         let materialsUsed = $('input[name=materials-used-input]').val().trim();
//         const buttonSubmit = $('#modal-edit-materials-used .btn-edit-materials-used')
//
//         $('input[name=materials-used-input]').on('input', function (e) {
//             materialsUsed = e.target.value.trim();
//             if (!numberRegex.test(materialsUsed) && materialsUsed !== '' || materialsUsed > 100) {
//                 if (!$('.error-number').length) {
//                     $('<div class="errorlist error-number">' +
//                         ' 利用料のフォーマットが正しくありません。' +
//                         '</div>').insertAfter($('input[name=materials-used-input]'));
//                 }
//             } else {
//                 $('.errorlist').remove();
//             }
//             disableButton();
//         });
//
//         function disableButton() {
//             buttonSubmit.toggleClass('disabled', materialsUsed === '' || !numberRegex.test(materialsUsed) || materialsUsed > 100);
//         }
//
//         disableButton();
//     }
//
//     checkMaterialUsedNumber();
// }

function editEvaluationPoint() {
    $(document).on('click', '.materials-used__content', function () {
        $('.btn-edit-materials-used').addClass('disabled');
        let userDom = $(this).parents('.tr-artist-account-info');
        $('#modal-edit-materials-used').attr('data-user', userDom.attr('data-user-id'));
        $('#modal-edit-materials-used').modal('show');
        if(window.innerWidth > 992){
            $('#modal-edit-materials-used').css({'display': 'flex', 'align-items': 'center'});
        }
        let currentValue = userDom.find('.materials-used__content span').text();
        $('input[name=materials-used-input]').val(currentValue);
        $('input[name=materials-used-input]').attr('data-value', currentValue);
    });

    $(document).on('input', 'input[name=materials-used-input]', function (e) {
        let intValue = this.value.replaceAll(',', '');
        if (intValue.length > this.maxLength) {
            this.value = intValue.slice(0, this.maxLength - 4);
        }
        this.value = intValue.replace(/\D/g, '').replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        disableButton();
    });

    function disableButton() {
        let materialsUsed = $('input[name=materials-used-input]').val().trim();
        $('.btn-edit-materials-used').toggleClass('disabled', materialsUsed === '' || materialsUsed == $('input[name=materials-used-input]').attr('data-value'));
    }

    $(document).on('click', '.btn-edit-materials-used', function () {
        let buttonDom = $(this);
        let userId = $('#modal-edit-materials-used').attr('data-user');
        let newValue = $('input[name=materials-used-input]').val();
        if (newValue.trim() === '') {
            newValue = '0'
        }
        if (!buttonDom.hasClass('disabled') && userId) {
            buttonDom.addClass('disabled');
            $.ajax({
                type: 'POST',
                url: '/accounts/curator/edit_evaluation_point_artist',
                datatype: "json",
                data: {
                    'user_id': userId,
                    'evaluation_point': newValue
                },
                beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                    $('#modal-edit-materials-used').modal('hide');
                    let userDom = $('.tr-artist-account-info[data-user-id=' + response.user_id + ']');
                    userDom.find('.materials-used__content span').html(newValue)
                },
                error: function () {

                }
            });
        }

    })
}

function deleteUser() {
    $(document).on('click', '.delete-user-button', function () {
        let button = $(this);
        bootbox.confirm({
            message: "本当に削除しますか？",
            buttons: {
                cancel: {
                    label: 'Cancel',
                    className: 'btn btn-info btn--primary btn-cancel-message'
                },
                confirm: {
                    label: 'OK',
                    className: 'btn btn-danger btn--tertiary btn-delete-message'
                },
            },
            callback: function (result) {
                if (result) {
                    button.parents('.account-info__action').find('.delete-user-btn').trigger('click');
                }
            }
        });
    });
}

function checkFormInvite() {
    let emailVal = $('#modal-invite-artist input[name=email]').val().trim();
    const emailDom = $('#modal-invite-artist input[name=email]');
    const buttonSubmit = $('#modal-invite-artist .btn-invite-artist');
    const emailRegex = /^\b[A-Z0-9._%-]+@[A-Z0-9.-]+\.[A-Z]{2,4}\b$/i


    emailDom.on('input', function (e) {
        emailVal = e.target.value;
        disabledButtonSubmit();
    })

    function disabledButtonSubmit() {
        buttonSubmit.toggleClass('disabled', emailVal === '' || !emailRegex.test(emailVal));
    }

    disabledButtonSubmit();
}

let total_page_search;
let list_skill_ids = [];
let ket_word = '';
let is_loading_artist_search = false;
let current_load_artist_search = 0;

function resetLoadMoreSearch() {
    total_page_search;
    list_skill_ids = [];
    ket_word = '';
    is_loading_artist_search = false;
    current_load_artist_search = 0;
}

function searchArtist() {
    $(document).on('input', '#account-management__search-artist-input', function (e) {
        if (e.target.value.trim() !== '') {
            $(this).parents('.sform-group__append-before').find('.search-delete').css({"display": "block"});
        }
    });

    $('.search-delete').on('click', function () {
        $('#account-management__search-artist-input').val('');
        $(this).css({"display": "none"});
        $('.skills-item').removeClass('selected');
        $('.nav-item .notification--blue').addClass('hide').attr('value', 0);
        $('.nav-item .notification--blue').html(0);
        $('.list-search-artist').empty();
        resetLoadMoreSearch();
        $('.list-all-artist').removeClass('hide');
    });

    $(document).on('keyup', '#account-management__search-artist-input', function (e) {
        if ((e.key === 'Enter' || e.keyCode === 13) && $('#account-management__search-artist-input').is(':focus')) {
            total_page_search = 0;
            let stage_name = $('#account-management__search-artist-input').val();
            if (stage_name.trim() === '' && !$('.skills-item.selected').length) {
                $('.list-search-artist').empty();
                $('.list-all-artist').removeClass('hide');
                resetLoadMoreSearch();
                return
            }
            let skill_ids = [];
            $('.skills-item.selected').map(function () {
                skill_ids.push($(this).attr('data-id'))
            });
            list_skill_ids = skill_ids;
            key_word = stage_name;
            $.ajax({
                type: 'GET',
                datatype: 'json',
                url: '/accounts/curator/curator_search_artist',
                data: {
                    'stage_name': stage_name,
                    'skill_ids': skill_ids
                },
                beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                    $('.list-search-artist').empty();
                    $('.list-all-artist').addClass('hide');
                    $('.list-search-artist').append(response.html);
                    total_page_search = response.total_page_search;

                },
                error: function () {
                    $('.list-search-artist').empty();
                    resetLoadMoreSearch();
                }
            })
        }
    });
}

function checkEmailExistInviteArtist() {
    $(document).on('keyup', '#id_email', function () {
        let emailDom = $('#id_email');
        let emailValue = emailDom.val().trim();
        emailDom.removeClass('error-border');
        $('.errorlist').remove();
        if (!validateEmail()) {
            return false
        }

        $.ajax({
            type: 'POST',
            url: '/accounts/curator/check_email_exist_invite_artist',
            data: {
                'email_user': emailValue,
            },
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                $('.btn-invite-artist').removeClass('disabled');
            },
            error: function () {
                $('.btn-invite-artist').addClass('disabled');
                $('<ul class="errorlist">' +
                    '<li>このメールアドレスはすでに存在しています。</li>' +
                    '</ul>').insertAfter(emailDom);
            }
        });
    })
}

function ajaxSubmitFormInvite() {
    $(document).on('click', '.btn-invite-artist', function () {
        $('.error-border').removeClass('error-border');
        $('.errorlist').remove();
        let buttonDom = $(this);
        if (!buttonDom.hasClass('disabled')) {
            checkValidateFormInvite();
            buttonDom.addClass('disabled');
            let form = $('#invite-artist-form');
            var objectJson = new FormData(form[0]);
            $.ajax({
                type: 'POST',
                url: '/accounts/curator/invite_artist',
                datatype: "json",
                processData: false,
                contentType: false,
                data: objectJson,
                beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                    $('#modal-invite-artist').modal('hide');
                    $('.account_management__list-invited').removeClass('hide');
                    $('.list-invited__list').append(response.html);
                },
                error: function () {
                    $('.btn-invite-artist').removeClass('disabled');
                    $('<ul class="errorlist">' +
                        '<li>エラーが発生しました</li>' +
                        '</ul>').insertAfter($('#id_email'));
                }
            });
        }
    })
}

function resetFormInvite() {
    let formDom = $('#invite-artist-form');
    formDom.find('input').each(function () {
        $(this).val('')
    });
    $('.error-border').removeClass('error-border');
    $('.errorlist').remove();
    $('.btn-invite-artist').addClass('disabled');
}


const max_length_stage_name = 60;
const max_length_position = 255;

function checkValidateFormInvite() {
    let stageNameDom = $('input[name="stage_name"]');
    let stageNameEnDom = $('input[name="stage_name_en"]');
    let positionDom = $('input[name="position"]');
    let typeDom = $('input[name="type"]');
    let enterpriseDom = $('input[name="enterprise"]');
    if (stageNameDom.val() && stageNameDom.val().trim().length > max_length_stage_name) {
        stageNameDom.val(stageNameDom.val().slice(0, max_length_stage_name))
    }

    if (stageNameEnDom.val() && stageNameEnDom.val().trim().length > max_length_stage_name) {
        stageNameEnDom.val(stageNameEnDom.val().slice(0, max_length_stage_name))
    }
    validateLengthInput(stageNameDom, max_length_stage_name);
    validateLengthInput(stageNameEnDom, max_length_stage_name);
    validateLengthInput(positionDom, max_length_position);
    validateLengthInput(typeDom, max_length_stage_name);
    validateLengthInput(enterpriseDom, max_length_position)
}

function validateLengthInput(elementDom, max_length) {
    if (elementDom.val() && elementDom.val().trim().length > max_length_position) {
        elementDom.val(elementDom.val().slice(0, max_length))
    }
}

let is_resend_mail = false;
let is_delete_artist = false;
function inviteAccountAction() {
    $(document).on('click', '.btn-remove-invite', function () {
        //TODO: handle Delete here
        let buttonDom = $(this);
        let artistDom = buttonDom.parents('.list-invited__component_container');
        let user_id = artistDom.attr('data-user');

        bootbox.confirm({
            message: "本当に招待を取り消しますか？",
            buttons: {
                confirm: {
                    label: 'はい',
                    className: 'btn btn-success btn--tertiary btn-delete-message'
                },
                cancel: {
                    label: 'いいえ',
                    className: 'btn btn-danger btn--primary btn-cancel-message'
                }
            },
            callback: function (result) {
                if (result === true) {
                    if (!is_delete_artist) {
                        is_delete_artist = true;
                        let jwt_token = artistDom.attr('data-jwt');
                        $.ajax({
                            type: 'DELETE',
                            url: '/accounts/curator/invite_artist?jwt=' + jwt_token,
                            success: function () {
                                artistDom.remove();
                                $('.text-danger[data-user=' + user_id + ']').remove();
                                $('.errorlist[data-user=' + user_id + ']').remove();
                                if (!$('.account_management__list-invited .list-invited__component_container').length) {
                                    $('.account_management__list-invited').addClass('hide');
                                }
                            },
                            error: function () {
                                toastr.error("このアクションは実行できません！");
                            },
                            complete: function () {
                                is_delete_artist = false
                            }
                        })
                    }
                }
            }
        })
    });

    $(document).on('click', '.btn-resend-invite', function () {
        //TODO: Handle resend here
        let buttonDom = $(this);
        let artistDom = buttonDom.parents('.list-invited__component_container');
        let user_id = artistDom.attr('data-user');
        if (!is_resend_mail) {
            is_resend_mail = true;
            let jwt_token = artistDom.attr('data-jwt');
            $.ajax({
                type: 'PUT',
                url: '/accounts/curator/invite_artist?jwt=' + jwt_token,
                success: function () {
                    artistDom.after(`<small class="text-danger" data-user="${user_id}" style="font-size: 8px;list-style: none;padding: 8px 0;">招待メールを送りました。</small><br>`);
                },
                error: function () {
                    $(`<ul class="errorlist" data-user="${user_id}"><li>エラーが発生しました</li></ul>`).insertAfter(artistDom);
                },
                complete: function () {
                    is_resend_mail = false
                }
            });
        }

    });
}

// LOAD MORE ARTIST
let is_loading_artist = false;
let current_load_artist = 0;
let total_artist;

function scrollToLoadMoreArtist() {
    $(document).scroll(function () {
            if ($('.account-management__list-acount.list-all-artist:not(.hide)').length && $(this).scrollTop()  + $(window).height() > $(document).height() - 100 && !is_loading_artist) {
                ajaxLoadMoreArtist()
            } else if ($('.account-management__list-acount.list-search-artist:not(.hide)').length && $(this).scrollTop() + $(window).height() > $(document).height() - 100 && !is_loading_artist_search) {
                ajaxLoadMoreArtistSearch()
            }
        }
    )
}

function ajaxLoadMoreArtist() {
    if (current_load_artist < total_artist && !is_loading_artist) {
        current_load_artist++;
        is_loading_artist = true;
        $.ajax({
            type: "GET",
            datatype: "json",
            url: "/accounts/curator/get_load_more_artist",
            data: {
                'offset': current_load_artist,
            },
            beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                $('.account-management__list-acount.list-all-artist').append('<div class="load-more-loading"></div>')
            },
            success: function (data) {
                is_loading_artist = false;
                $('.account-management__list-acount.list-all-artist table tbody').append(data.html);
                $('.load-more-loading').remove();
            }
        });
    }
}

function ajaxLoadMoreArtistSearch() {
    if (current_load_artist_search < total_page_search && !is_loading_artist_search) {
        current_load_artist_search++;
        is_loading_artist_search = true;
        $.ajax({
            type: "GET",
            datatype: "json",
            url: "/accounts/curator/get_load_more_artist",
            data: {
                'offset': current_load_artist_search,
                'stage_name': key_word,
                'skill_ids': list_skill_ids
            },
            beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                $('.account-management__list-acount.list-search-artist').append('<div class="load-more-loading"></div>')
            },
            success: function (data) {
                is_loading_artist_search = false;
                $('.account-management__list-acount.list-search-artist:not(.hide) table tbody').append(data.html);
                $('.load-more-loading').remove();
            }
        });
    }
}

$(document).ready(function () {
    total_artist = $('.account-management__list-acount.list-all-artist').attr('data-total-page');
    activeTab();
    uploadNDA();
    approvalOfIndentity();
    inviteArtist();
    deleteUser();
    checkFormInvite();
    searchArtist();
    editEvaluationPoint();
    checkEmailExistInviteArtist();
    ajaxSubmitFormInvite();
    // editIsDirect();
    inviteAccountAction();
    scrollToLoadMoreArtist();

    $(document).on("click", '.skills-item', selectSkill);

    $(document).on('hidden.bs.modal', '#modal-invite-artist', function (e) {
        resetFormInvite()
    });

    $(document).on('hidden.bs.modal', '#modal-edit-materials-used', function (e) {
        let modal = $(this);
        modal.attr('data-user', '');
        modal.find('.errorlist').remove();
        modal.find('.btn-edit-materials-used').addClass('disabled');
    });
});
