import { PROJECT_DEFAULT_FILE_HOST } from "../../../constant/index.js";
import UserDownloaded from "../file-info/UserDownloaded.js";

const DocumentMinorBlock = ({
  fileInfo,
  file,
  version,
  is_pc_device
}) => {
  let dataFileInfo = fileInfo;
  let parsed = false;

  if (dataFileInfo && Object?.keys(dataFileInfo)?.length && Object?.keys(dataFileInfo)?.length > 0){
    parsed = true;
  }

  if (version == 2){
    return `
      <div class="block-pdf-image">
        ${parsed ? `<img src="${file?.presigned_url}" class="pdf-image" alt="pdf image">` : ''}
      </div>
      <div class="comment-file-content ">
        <p style="word-break: break-all;" class="file-name">${file?.real_name}</p>
        <div class="btn-download-file-cmt">
          <a href="javascript:void(0);" data-scene-title-id="${ file?.scene?.scene_id }" data-file-id="${ file?.file_id }" 
          class="btn-download-file ${file?.scene?.production_file ? `tfile-producttion-file` : ''} block-download-file">
            <span class="material-symbols-rounded scene-file-download">
						download
					</span>
          </a>
        </div>
      </div>
      <div class="info-message-file">
        <div class="size-file-message">
          ${parsed ? `<span>${ dataFileInfo?.width } x ${ dataFileInfo?.height }px </span>` : ''}
        </div>
        <div class="user-download-file">
          <div class="has_user_downloaded">
            ${UserDownloaded({
              file,
              type_comment: 'messenger',
              is_pc_device
            })}
          </div>
        </div>
      </div>`
  }else{
    return `
      <div class="size-file-message">
        ${parsed ? `
        <span>${ dataFileInfo?.width } x ${ dataFileInfo?.height }px </span>` : ''}
      </div>`;
  }

};

export default DocumentMinorBlock;
