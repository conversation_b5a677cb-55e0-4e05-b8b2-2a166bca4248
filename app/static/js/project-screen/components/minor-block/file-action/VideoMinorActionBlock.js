const VideoMinorActionBlock = ({
  file
}) => {
  let isAudioFile = ['audio','video']?.includes(file?.type_file_name);
  let isReadyState = ['1','2','3']?.includes(file?.acr_status);

  return `
  <div class="action-right-video">
    ${isAudioFile && isReadyState ? file.acr_status == '3' ? `
      <a href="javascript:void(0);" class="acr-result-icon btn-finger-print active" data-file-id="${ file?.file_id }">
        <img src="/static/images/scene-detail/icon-finger-print-active.svg" class="" alt="finger print">
      </a>
    ` : `
      <a href="javascript:void(0);" class="acr-result-icon btn-finger-print">
        <img src="/static/images/scene-detail/icon-finger-print.svg" class="" alt="finger print">
      </a>
    ` : ``}
    <a href="javascript:void(0);" class="btn-download-file">
      <span class="material-symbols-rounded scene-file-download">
						download
					</span>
    </a>
  </div>`;
};

export default VideoMinorActionBlock;
