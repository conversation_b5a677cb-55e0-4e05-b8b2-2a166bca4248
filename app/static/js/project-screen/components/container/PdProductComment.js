import CommentBlock from "../block/CommentBlock.js";
import NavigationTopBar from "../block/NavigationTopBar.js";

const PdProductComment = ({comments, projectPk, currentUserId, is_pc_device, current_user_role, offer_status, is_seen}) => {
	return `<div class="pd-section__content main-talk-room">
				<div class="pd-comment refactor">
					<div id="footerCommentBlock" class="footer-comment-block refactor">
						<div class="action-panel-head">
							<div class="offer-block-right maction maction-new">
								<div class="mcommment mcomment-new" id="comment-input-99">
									<div class="mcomment-message">
										<div class="mcomment-top comment-top-area">
											<div class="mcomment-attached">
												<div class="mattach-preview-container">
													<div class="mattach-previews collection">
														<div class="mattach-template collection-item item-template">
															<div class="mattach-info" data-dz-thumbnail="">
																<div class="mcommment-file">
																	<div class="determinate" style="width:0"
																		data-dz-uploadprogress="">
																	</div>
																	<div class="mcommment-file__name"
																		data-dz-name="">
																	</div>
																	<div class="mcommment-file__delete"
																		href="#!"
																		data-dz-remove=""><i
																			class="icon icon--sicon-close"></i>
																	</div>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
											<div class="mcomment-input mcomment-input-new">
												<div class="mcomment-input-title"></div>
												<div class="mcomment-input-close"><i class="icon icon--sicon-close"></i>
												</div>
											</div>
											<div class="block-remove-msg-editing d-none">
												<div class="mcomment-input-close btn-remove-msg">
													<i class="icon icon--sicon-close"></i>
												</div>
											</div>
											<div class="mcomment-bottom mcomment-bottom-new">
												<div class="mcomment-action mcomment-action-new">
													<div class="mattach-label mattach-label-new dz-clickable">
														<span class="material-symbols-rounded c-icon-attach">add_circle</span>
													</div>
												</div>
												<textarea class="mcomment-input-text mcomment-autoExpand" name="mcomment-input" id="text-input-comment" rows="1" placeholder="メッセージを送信"></textarea>
												<div class="mcomment-icon">
												</div>
												<a href="#" class="mcomment-send disabled">
													<span class="material-symbols-rounded c-icon-send is-icon-inactive">send</span>
												</a>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="pd-comment__heading">メッセージ</div>
					<div class="pd-comment__main">
						<div class="pd-comment__content">
							<div class="mmessage-component refactor mmessage-component-refactor">
								<div class="mattach" style="color: black;" id="comment-input-99-mcomment-attach" data-maxfile="2" data-maxsize="32">
									<div class="mattach-overlay" style="display: none;"></div>
									<form class="mattach-file" action="upload.php" id="comment-input-99-mcomment-attach-form">
										<div class="mattach-drop" style="display: none;">
											<div class="mattach-text"></div>
											<div class="border"></div>
										</div>
									<input type="hidden" name="csrfmiddlewaretoken" value="Us8iUtyCAVoTKTXiXMOD0sXpn19GvvFdUd8tyuZ3dWkeXzN7ges1ndKwPbTyQDnK"></form>
								</div>
								<div class='mmessage-list mscrollbar mscrollbar--vertical refactor mscrollbar--bottom ${is_seen ? "not-seen" : ""}'>
									<div class="mmessage-list-container refactor">
										${comments
                                            ?.map((comment, index) => {
												return CommentBlock({
													comment: {
														...comment,
														type: comments.type,
													},
													currentUserId,
													isLast:
														index ===
														comments.length - 1,
													is_pc_device,
												});

                                            })
                                            .join("")}
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
	`;
};

export default PdProductComment;
