import DefaultMinorBlock from "../minor-block/file-comment-detail/DefaultMinorBlock.js";
import DocumentMinorBlock from "../minor-block/file-comment-detail/DocumentMinorBlock.js";
import ImageMinorBlock from "../minor-block/file-comment-detail/ImageMinorBlock.js";
import VideoMinorBlock from "../minor-block/file-comment-detail/VideoMinorBlock.js";
import { PROJECT_DEFAULT_FILE_HOST, PROJECT_DEFAULT_CONVERTED_FILE_HOST } from "../../constant/index.js";
import FolderMinorBlock from "../minor-block/file-comment-detail/FolderMinorBlock.js";
import UserDownloaded from "../minor-block/file-info/UserDownloaded.js";

const ItemSendBlock = ({
    comment,
    type_file,
    file,
	indexFile,
	typeComment,
	is_pc_device
}) => {
	let isProduct = ['product','messenger_owner', 'messenger'].includes(comment?.type);
	let isAudioVideo = ['audio', 'video'].includes(file?.type_file_name);
	let isReadyStatus = ["1","2","3"].includes(file?.acr_status);
	let fileInfo = {};
	let parsed = false;
	try {
		fileInfo = JSON.parse(file?.file_info);
		parsed = true;
	}catch(e){
		//data is not JSON
	}
	//TODO: clear this part in file item_send_2 <div class="info-item-audio-comment {% if scene.production_file %}tfile-producttion-file {% endif %}block-download-file"
	//TODO: check scene.pk => what logic related to scene here
	//TODO: need to check the logic get list user download file: peoples-downloaded-audio-msg

	switch (type_file){
		case 'audio':
			return `
			<div class="comment-audio-content comments-audio-block ">
				<div class="mmessenger mmessenger--audio-wave mmessenger--black audio-message" data-file-id="${file?.file_id}">
					<div class="messenger-content">
						<div class="s-audio s-audio--audio-wave s-audio--black" ${!isProduct ? `data-scene-id="${ comment?.scene_id }"` : ''}>
							<div class="s-audio-control ${!isProduct && comment?.pin_video ? 'video-pin-time' : '' }">
								<span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                  play_circle
                </span>
                <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
							</div>
							<div class="video-pin-start hide">${isProduct ? comment?.pin_time : ''}</div>
							<div class="s-audio-source" data-link="${file?.presigned_url}" title="${file?.real_name}" data-waveColor="#d3d3d3" data-progressColor="#53565a" data-peaks-loaded="${ file?.peaks }"></div>
							<div class="s-audio-waveform"></div>
							<div class="s-audio-time"></div>
						</div>
					</div>
				</div>
				<div class="info-item-audio-comment ${false ? 'tfile-producttion-file' : ''} block-download-file" data-scene-title-id="${'scene.pk'}" data-file-id="${ file?.file_id }">
					<div class="info-audio-message">
						<div class="block-name-action-audio">
							<p class="file-name-message file-name-cmt">${file?.real_name}</p>
							<div class="block-btn-action-audio-msg">
								${isAudioVideo && isReadyStatus ? file.acr_status == '3' ? `
								<a href="javascript:void(0);" class="acr-result-icon btn-finger-print active" data-file-id="${ file?.file_id }">
									<img src="/static/images/scene-detail/icon-finger-print-active.svg" class="" alt="finger print">
								</a>`: `
								<a href="javascript:void(0);" class="acr-result-icon btn-finger-print">
									<img src="/static/images/scene-detail/icon-finger-print.svg" class="" alt="finger print">
								</a>` : ''}
								<a href="javascript:void(0);" class="btn-download-file">
									<span class="material-symbols-rounded scene-file-download">
						download
					</span>
								</a>
							</div>
						</div>
						<div class="audio-type-info-msg info-message-file">
							${parsed && false 
								? `<div class="file-info-message">
											<span>${ fileInfo?.sample_rate } </span>
											<span>${ fileInfo?.bit_depth } </span>
											<span>${ fileInfo?.channel_type } </span>
											<span>${ fileInfo?.loudness }</span>
									</div>`: ''}
							<div class="peoples-downloaded-audio-msg">	
								<div class="has_user_downloaded">
									${UserDownloaded({file, type_comment: 'messenger', is_pc_device})}
								</div>
							</div>
						</div>
					</div>	
				</div>
			</div>`;
		default:
			if (!isProduct && comment?.pin_video){
				return `
				<div class="s-audio s-audio--audio s-audio--black" data-scene-id="${ comment?.scene_id }">
					<div style="display: flex">
						<div class="s-audio-control video-pin-time">
							<span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                play_circle
              </span>
              <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
						</div>
            			<div class="s-audio-time video-pin-start">${ comment?.pin_time }</div>
          			</div>
					<div class="s-audio-text s-audio-file">
						<div class="mmessenger mmessenger--file mmessenger--black ${folder ? 'messager-folder' : ''}" 
									data-toggle="modal" data-target="#modal-${ file?.type_file_name }-popup" data-link="${PROJECT_DEFAULT_FILE_HOST}/${file?.file}"
						data-name="${file?.real_name}" data-type="${file?.type_file_name}" data-file-id="${ file?.file_id }">
							<div class="messenger-content messenger-content-file">
								<div class="s-file s-file--file s-file--black">
									<i class="${folder ? 'icon icon--sicon-storage' : 'icon icon--sicon-clip'}"></i>${file?.real_name ? file.real_name : file.name}
								</div>
							</div>
						</div>
					</div>
				</div>`;
			} else {
				let extraClassName = '';
				let extraClassName2 = 'other-file';
				let extraClassName3 = '';
				let childRender = DefaultMinorBlock({ fileInfo, file, version: 2, is_pc_device});
				switch (type_file){
					case 'image':
						if (comment?.fileList?.length == 1 && (!comment?.comment || comment?.comment == '')){
							extraClassName = 'single-file-image';
						}
						extraClassName2 = 'messenger-image-preview-content';
						childRender = ImageMinorBlock({ fileInfo, file, version: 2, is_pc_device});
						break;
					case 'video':
						extraClassName2 = 'message-video-content';
						childRender = VideoMinorBlock({ fileInfo, file, version: 2, is_pc_device});
						break;
					case 'document':
						extraClassName2 = 'message-document-content';
						extraClassName3 = 'document-file';
						childRender = DocumentMinorBlock({ fileInfo, file, version: 2, is_pc_device});
						break;
					case 'folder':
						extraClassName = 'messager-folder';
						extraClassName2 = 'message-folder-content';
						childRender = FolderMinorBlock({ fileInfo, file, version: 2});
						break;
					default:
						extraClassName = '';
						extraClassName2 = 'other-file';
						extraClassName3 = '';
						childRender = DefaultMinorBlock({ fileInfo, file, version: 2, is_pc_device});
				}

				return `
				<div class="mmessenger mmessenger--file block-download-file ${typeComment ? "mmessenger--gray" : ""} mmessenger--black message-file-other ${extraClassName}" 
					data-toggle="modal" data-target="#modal-${type_file}-popup" data-link="${PROJECT_DEFAULT_FILE_HOST}/${file?.file}" 
					${file?.converted_file ? `data-converted-link="${PROJECT_DEFAULT_CONVERTED_FILE_HOST}/${file?.converted_file}" data-converted-path="${file?.converted_file}"` : ''}
        			data-name="${file?.real_name ? file.real_name: file.name}" data-type="${type_file}" data-file-id="${file?.file_id ? file.file_id : file.folder_id}">
					<div class="messenger-content">
						<div class="s-file s-file--file s-file--black ${extraClassName2}">
							${childRender}
						</div>
						${type_file == 'folder' ? `<p class="txt-total-file">${comment?.fileList?.length} ${comment?.fileList?.length > 2 ? `files` : `file`}</p>`: ''}  
					</div>
        		</div>`;
			}
	}
}
  
export default ItemSendBlock;
