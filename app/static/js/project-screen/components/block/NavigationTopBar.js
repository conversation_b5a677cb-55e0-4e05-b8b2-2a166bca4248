/**
 * Renders the navigation top bar component.
 *
 * @param {Object} props - The component props.
 * @param {string} props.user_role - The user role.
 * @param {string} props.offer_status - The offer status.
 * @param {string} props.current_user_role - The current user role.
 * @returns {string} The HTML string representing the navigation top bar component.
 */
const NavigationTopBar = ({
	user_role,
	offer_status,
	current_user_role
}) => {
	return `<div class="navigation-top-app-bar refactor" id="navigation-top-app-after">
		<span id="left-sidebar-open" class="material-symbols-rounded hidden-el">
			view_sidebar
		</span>
		<div class="form-check custom-switch custom-switch-new switch-talkroom" style="display: inline-block;transition: all 0.3s ease 0s;">
			<label class="form-check-label label-new">
				<div class="form-check-group form-group-new">
					<span class="text-item switch-talk-room bodytext text-left navbar-active">進行中のみ</span>
					<input class="form-check-input switch-checkbox switch-checkbox-tr" type="checkbox" name="switch-checkbox-comment" id="order-monthy"/>
					<span class="text-item switch-talk-room bodytext text-right">全メッセージ</span>
				</div>
			</label>
		</div>
		<div class="form-check custom-switch custom-switch-new switch-dm ${user_role == 'master_admin' ? 'show' : 'hide'}">
			<label class="form-check-label label-new">
				<div class="form-check-group form-group-new">
					<span class="text-item bodytext switch-dm-txt text-left">進行中</span>
					<input class="form-check-input switch-checkbox switch-checkbox-dm" ${offer_status == 'processing' ? 'checked' : ''} type="checkbox" name=""
						id="offer-filter" data-offers-status="${ offer_status }">
					<span class="text-item bodytext switch-dm-txt text-right">全てのスレッド</span>
				</div>
			</label>
		</div>
		<div class="wallet-pl" style="display: inline-block;float: right">
			${current_user_role != 'master_client' ? `
			<span class="material-symbols-rounded u-pr8 balance-wallet-icon wallet-icon open-close-nav nav-close ${current_user_role == 'master_admin' ? 'show' : ''}"
				onclick="openCloseNav(this)">
				account_balance_wallet
			</span>` : ''}
			<span class="material-symbols-rounded show-file-icon pd-file-heading show close-file">
				home_storage
			</span>
		</div>
</div>`
}

export default NavigationTopBar;
