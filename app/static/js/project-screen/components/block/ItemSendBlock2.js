import VideoMinorActionBlock from "../minor-block/file-action/VideoMinorActionBlock.js";
import DefaultMinorActionBlock from "../minor-block/file-action/DefaultMinorActionBlock.js";
import DefaultMinorBlock from "../minor-block/file-comment-detail/DefaultMinorBlock.js";
import DocumentMinorBlock from "../minor-block/file-comment-detail/DocumentMinorBlock.js";
import ImageMinorBlock from "../minor-block/file-comment-detail/ImageMinorBlock.js";
import VideoMinorBlock from "../minor-block/file-comment-detail/VideoMinorBlock.js";
import FileDownloadBlock from "../minor-block/file-download/FileDownloadBlock.js";
import { PROJECT_DEFAULT_FILE_HOST, PROJECT_DEFAULT_CONVERTED_FILE_HOST } from "../../constant/index.js";
import UserDownloaded from "../minor-block/file-info/UserDownloaded.js";

const ItemSendBlock2 = ({
    comment,
    type_file,
    folder,
    file,
		indexFile
}) => {
	let isProduct = ['product','messenger_owner', 'messenger'].includes(comment?.type);
	let isAudioVideo = ['audio', 'video'].includes(file?.type_file_name);
	let isReadyStatus = ["1","2","3"].includes(file?.acr_status);
	let fileInfo = {};
	let parsed = false;
	
	try {
		fileInfo = JSON.parse(file?.file_info);
		parsed = true;
	}catch(e){
		//data is not JSON
	}
	//TODO: clear this part in file item_send_2 <div class="info-item-audio-comment {% if scene.production_file %}tfile-producttion-file {% endif %}block-download-file"
	//TODO: check scene.pk => what logic related to scene here
	//TODO: need to check the logic get list user download file: peoples-downloaded-audio-msg

	switch (type_file){
		case 'audio':
			return `
				<div class="comment-audio-content comments-audio-block ${indexFile > 0 && comment.fileList?.length > 1 ? 'border-audio-message' : ''}">
					<div class="mmessenger mmessenger--audio-wave mmessenger--black message-audio-block" data-file-id="${file?.file_id}">
						<div class="messenger-content messenger-content-file">
							<div class="s-audio s-audio--audio-wave s-audio--black wave-file-cmt" style="border: none" ${isProduct ? `data-scene-id="${ comment.scene_id }"` : ''}>
                <div class="s-audio-control ${isProduct && comment?.pin_video ? 'video-pin-time' : ''}">
                  <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                    play_circle
                  </span>
                  <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                </div>
                <div class="video-pin-start hide">${isProduct ? comment?.pin_time : ''}</div>
                <div class="s-audio-source" data-link="${ PROJECT_DEFAULT_FILE_HOST }/${file?.file}" title="${file?.real_name}" data-waveColor="#d3d3d3" data-progressColor="#53565a" data-peaks-loaded="${ file?.peaks }"></div>
                <div class="s-audio-waveform"></div>
                <div class="s-audio-time"></div>
              </div>
						</div>
          </div>
					<div class="info-item-audio-comment ${false ? 'tfile-producttion-file' : ''} block-download-file" data-scene-title-id="${'scene.pk'}" data-file-id="${ file?.file_id }">
            <div class="block-name-action-audio">
              <p class="file-name-message file-name-cmt">${file?.real_name}</p>
              <div class="block-btn-action-audio-msg">
								${isAudioVideo && isReadyStatus ? file.acr_status == '3' ? `
								<a href="javascript:void(0);" class="acr-result-icon btn-finger-print active" data-file-id="${ file?.file_id }">
                  <img src="/static/images/scene-detail/icon-finger-print-active.svg" class="" alt="finger print">
                </a>`: `
								<a href="javascript:void(0);" class="acr-result-icon btn-finger-print">
                  <img src="/static/images/scene-detail/icon-finger-print.svg" class="" alt="finger print">
                </a>` : ''}
                <a href="javascript:void(0);"
                  class="btn-download-file"
                >
                  <span class="material-symbols-rounded scene-file-download">
						download
					</span>
                </a>
              </div>
            </div>
            <div class="audio-type-info-msg">
							${parsed ? `
							<div class="file-info-message">
                <span>{{ result.sample_rate }} </span>
                <span>{{ result.bit_depth }} </span>
              	<span>{{ result.channel_type }} </span>
                <span>{{ result.loudness }}</span>
              </div>`: ''}
              <div class="peoples-downloaded-audio-msg">
                <div class="has_user_downloaded">
									${UserDownloaded({
										file,
										type_comment: 'messenger'
									})}
                </div>
              </div>
            </div>
          </div>	
				</div>	  
			`;
		default:
			if (!isProduct && comment?.pin_video){
				return `
				<div class="s-audio s-audio--audio s-audio--black" data-scene-id="${ comment?.scene_id }">
          <div style="display: flex">
            <div class="s-audio-control video-pin-time">
							<span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                play_circle
              </span>
              <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
            </div>
            <div class="s-audio-time video-pin-start">${ comment?.pin_time }</div>
          </div>
          <div class="s-audio-text s-audio-file">
            <div class="mmessenger mmessenger--file mmessenger--black ${folder ? 'messager-folder' : ''}" 
						  data-toggle="modal" data-target="#modal-${ file?.type_file_name }-popup" data-link="${PROJECT_DEFAULT_FILE_HOST}/${file?.file}"
              ${file?.converted_file ? `data-converted-link="${PROJECT_DEFAULT_CONVERTED_FILE_HOST}/${file?.converted_file}" data-converted-path="${file?.converted_file}"` : ''}
              data-name="${file?.real_name}" data-type="${file?.type_file_name}" data-file-id="${ file?.file_id }">
              <div class="messenger-content messenger-content-file">
                <div class="s-file s-file--file s-file--black">
                  <i class="${folder ? 'icon icon--sicon-storage' : 'icon icon--sicon-clip'}"></i>${file?.real_name}
                </div>
              </div>
            </div>
          </div>
        </div>
				`;
			}else{
				let extraClassName = '';
				let extraClassName2 = '';
				let extraClassName3 = '';
				let childRender = DefaultMinorBlock({ fileInfo, file});
				let childRender2 = DefaultMinorActionBlock({file});

				switch (type_file){
					case 'image':
						if (comment?.fileList?.length == 1 && (!comment?.comment || comment?.comment == '')){
							extraClassName = 'single-file-image';
						}
						extraClassName2 = 'messenger-image-preview-content';
						childRender = ImageMinorBlock({ fileInfo, file });
						break;
					case 'video':
						extraClassName2 = 'message-video-file';
						childRender = VideoMinorBlock({ fileInfo, file});
						childRender2 = VideoMinorActionBlock({file});
						break;
					case 'document':
						extraClassName3 = 'document-file';
						childRender = DocumentMinorBlock({ fileInfo, file});
						break;
					case 'folder':
						extraClassName = 'messager-folder';
						break;
					default:
						extraClassName = '';
						extraClassName2 = '';
						extraClassName3 = '';
						childRender = DefaultMinorBlock({ fileInfo, file});
						childRender2 = DefaultMinorActionBlock({file});
				}

				return `
				<div class="mmessenger mmessenger--file block-download-file mmessenger--black message-file-other ${extraClassName}"
          data-toggle="modal" data-target="#modal-${type_file}-popup" data-link="${PROJECT_DEFAULT_FILE_HOST}/${file?.file}"
          ${file?.converted_file ? `data-converted-link="${PROJECT_DEFAULT_CONVERTED_FILE_HOST}/${file?.converted_file}" data-converted-path="${file?.converted_file}"` : ''}
        	data-name="${file?.real_name}" data-type="${type_file}" data-file-id="${ file?.file_id }">
          <div class="messenger-content messenger-content-file">
            <div class="s-file s-file--file s-file--black ${extraClassName2} file-comment-action">
							${type_file != 'folder' ? `
								<div class="info-message-2">
                  ${childRender}
                  <div class="users-downloaded-file">
										<div class="has_user_downloaded">
											${UserDownloaded({
												file,
												type_comment: 'messenger'
											})}
										</div>
									</div>
								</div>
								<div class="comment-file-content ${extraClassName3}">
                  <p style="word-break: break-all;" class="file-name-cmt">${file?.real_name}</p>
									${childRender2}
                </div>
								${FileDownloadBlock({file})}
							` : `
								<div class="left-folder-message">
                  <img src="/static/images/scene-detail/icon-folder.svg" class="icon-folder-left" alt="folder icon">
                  <p class="file-name-cmt" style="word-break: break-all;">{{ file_name }}</p>
                </div>
              	<img src="/static/images/scene-detail/icon-navigate-next.svg" alt="navigation next">
							`}
						</div>
            ${type_file == 'folder' ? `<p class="txt-total-file">${comment?.fileList?.length} ${comment?.fileList?.length > 2 ? `files` : `file`}</p>`: ''}  
          </div>
        </div>`;
			}
	}
}
  
export default ItemSendBlock2;
