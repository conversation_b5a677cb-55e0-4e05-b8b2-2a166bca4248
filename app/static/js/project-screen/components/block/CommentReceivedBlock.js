import { parseRequestUrl } from "../../common/utils.js";
import ItemSendBlock from "./ItemSendBlock.js";
import MessageInfoBlock from "./MessageInfoBlock.js";
// import CommentInfoTopBlock from "./CommentInfoTopBlock.js";
// import ItemReceivedBlock from "./ItemReceivedBlock.js";

const CommentReceivedContainer = ({comment, currentUserId, isLast, is_pc_device}) => {
  return `<div class="mmessage-container refactor">
            <div class="mmessage mmessage--received clicked ${comment?.is_near ? 'mmessage-near' : ''} ${comment?.resolved ? 'resolved' : ''}"
              data-message-id="${ comment?.comment_id }" ${comment?.parentComment ? `data-parent-id="${comment?.parentComment?.comment_id }"` : ''}>
                ${CommentReceivedBlock({comment, currentUserId, isLast, is_pc_device})}
            </div>
          </div>`;
}

export const CommentReceivedBlock = ({ comment, currentUserId, isLast, is_pc_device }) => {
  let isProduct = ['product','messenger_owner', 'messenger'].includes(comment?.type);
  let BaseMessageContent = '';
  if (comment?.parent_id || comment?.parent){
    let HaveParentRender = `
      ${comment?.comment && comment?.comment != '' ? `
        <div class="s-filedisable-wrap">
          <a class="s-filedisable s-filedisable--filedisable s-filedisable--gray" href="javascript:void(0)">${
            comment?.parentComment?.comment && comment?.parentComment?.comment !== "" ? comment?.parentComment?.comment : comment?.parentComment?.name
          }</a>
          ${!isProduct && comment?.pin_time ? `
            <div class="s-audio s-audio--audio s-audio--black" data-scene-id="${comment?.scene?.scene_id}">
              <div class="s-audio-icon">
                <div class="s-audio-control video-pin-time">
                  <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                    play_circle
                  </span>
                  <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                </div>
                <div class="s-audio-time video-pin-start">${comment?.pin_time }</div>
              </div>

              <div class="s-filetext">${ comment?.comment }</div>
            </div>
          ` : `<div class="s-filetext">${ comment?.comment }</div>`}` : ''}
          </div>
      ${comment.fileList.length > 0 ?
        comment.fileList.map((file,indexFile) => {
          if (!file.folder_id) {
            if (indexFile == 0 && !comment.comment && comment.comment == "") {
              return `
              <div class="comments-audio-block">
                <div class="s-filedisable-wrap">
                  <a class="s-filedisable s-filedisable--filedisable s-filedisable--black" href="javascript:void(0) ">${
                    comment?.parent_comment?.comment
                  }</a>
                  ${ItemSendBlock({ comment, type_file: file?.type_file_name, file, indexFile, typeComment: "received", is_pc_device })}
                </div>
              </div>`;
            } else {
              return `${ItemSendBlock({comment, file, type_file: file?.type_file_name, indexFile, typeComment: "received", is_pc_device})}`;
            }
          }
        }).join('') : ''}

      ${comment.folders?.length > 0 ?
        comment.folders?.map((folder, indexFolder) => {
          if (!folder.parent) {
            return ItemSendBlock({comment, type_file: folder?.type_file_name, file: folder, indexFile: indexFolder, typeComment: "received", is_pc_device});
          }
        }).join("") : ''}
    `;

    BaseMessageContent = HaveParentRender;
  }else{
    let HaveNoParentRender = `
    ${
      !comment.parent_id &&
      comment.comment &&
      comment.comment != "" ?
      `<div class="mmessenger mmessenger--text mmessenger--gray">
        <div class="messenger-content">
          <div class="messenger-content">
            ${!isProduct && comment?.pin_time ? `
              <div class="s-audio s-audio--audio s-audio--gray" data-scene-id="${ comment?.scene?.scene_id }">
                  <div class="s-audio-icon">
                    <div class="s-audio-control video-pin-time">
                      <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                        play_circle
                      </span>
                      <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                    </div>
                    <div class="s-audio-time video-pin-start">${ comment?.pin_time }</div>
                  </div>

                  <div class="s-audio-text">${ comment?.comment }</div>
                </div>
            ` : `<div class="s-text s-text--gray ${comment?.fileList?.length == 0 ? `single-text` : ''}">${comment?.comment}</div>`}
          </div>
        </div>
      </div>` : ''
    }
    ${
      comment.fileList.length > 0 ?
      comment.fileList.map((file, indexFile) => {
        if (!file.folder_id) {
          return ItemSendBlock({ comment, type_file: file?.type_file_name, file, indexFile, typeComment: "received", is_pc_device });
        }
      }).join("") : ''
    }
    ${
      comment?.folders ?
      comment?.folders.map((folder, indexFolder) => {
        if (folder.folder_id) {
          return ItemSendBlock({ comment, type_file: "folder", file: folder, indexFile: indexFolder, is_pc_device });
        }
      }).join("") : ''
    }
    `;

    BaseMessageContent = HaveNoParentRender;
  }

  return `
    <div class="mmessage-main">
      ${!comment?.is_near ? `
      <div class="c-avatar32"  title="${comment?.display_name }" style="background-image: url(${comment?.user_avatar})"></div>
      ` : ''}
      <div class="mmessage-content">
        ${BaseMessageContent}
      </div>
    </div>
    <div class="mmessage-info ${comment?.has_audio ? "message-info-audio" : "" }" >
    ${
      parseRequestUrl() !== "scene_show"
        ? `<div class="dropdown dropdown-comment-new dropdown-comment">
            <div class="dropdown-toggle show-more-comment show-more-action-message" id="dropdownMenu1" data-toggle="dropdown"
                 aria-expanded="false" aria-haspopup="true">
                <img src="/static/images/scene-detail/icon-more-horiz.svg" class="more-action-hoz" alt="more horiz comment">
            </div>
            <ul class="dropdown-menu dropdown-menu-comment" aria-labelledby="dropdownMenu1">
                <li class="li-resolve-message">
                    <a class="dropdown-item mmessage-resolve {% if message.resolved %}mmessage-resolved{% endif %}"
                       href="javascript:void(0);">
                        <span class="txt-item-comment">${
                          comment?.resolved ? "進行中に戻す" : "解決済みにする"
                        }</span>
                        <span class="material-symbols-rounded img-resolve-comment">
                          check_circle
                        </span>
                    </a>
                </li>
                <li class="li-reply-message">
                    <a class="dropdown-item mmessage-reply" href="javascript:void(0);">
                        <span class="txt-item-comment txt-reply-comment">返信</span>
                        <span class="material-symbols-rounded img-reply-comment">
                          reply
                        </span>
                    </a>
                </li>
                ${
                  comment.is_same_role && comment.user_id == currentUserId
                    ? `<li class="li-edit-message">
                        <a class="dropdown-item mmessage-edit" href="javascript:void(0);">
                            <span class="txt-item-comment txt-edit-comment">コメントを編集</span>
                            <span class="material-symbols-rounded img-edit-comment">
                              edit
                            </span>
                        </a>
                    </li>
                    <li class="last-action-comment li-reply-message">
                        <a class="dropdown-item mmessage-delete" href="javascript:void(0);">
                            <span class="txt-item-comment txt-green">削除</span>
                            <span class="material-symbols-rounded">
                              delete
                            </span>
                        </a>
                    </li>`
                    : ``
                }
            </ul>
        </div>`
        : ""}
      ${MessageInfoBlock({ comment, typeInfo: "received", is_pc_device, currentUserId })}
    </div>`;
};

export default CommentReceivedContainer;
