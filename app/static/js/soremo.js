//=require malihu-custom-scrollbar-plugin/jquery.mCustomScrollbar.concat.min.js
//01EG89H6SS2141VNGDDEHBMV4Q
let maudio_arr = [];
let list_file_id = {};
let list_files_folders = {};
let list_folder_id = {};
let mzdrop = '';
let list_file_remove = [];
let list_temp_folder_id = {};
let is_exceeded_length = false;
let first_load_page = true;
let messenger_page = '';
var list_scenes = [];
let regexXss = /^<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/;
var id_time = new Date().getTime().toString();
let valInput = ''
let currentOffer = ''
let should_scroll = true;
const paddingHeightActivePreview = 10;
const paddingWidthActivePreview = 10;
const widthSP = 767;
const widthIpad = 992;
const max_width_sp_device = 695;

$(document).ready(function () {
    messenger_page = $('.p-martist, .project-item').attr('data-page');
    filterOffer();
    styleGuide();
    appHeight();
    initmCustomScrollbar();
    modalInModal();
    //createThread();
    actionThread();
    actionMessage();
    searchOffer();
    previewFile();
    getDownloadAudio();
    getDownloadFileScene();
    projectFolder();
    actionOffers();
    removeOverLayModal();
    initializeAllHLSVideos();

    window.addEventListener('beforeunload onunload onbeforeunload', async function(e) {
        e.preventDefault();
        e.stopPropagation();
        if($(document).find('textarea.mcomment-input-text').length){
            await doneTyping($(document).find('textarea.mcomment-input-text').val())
        }
    })
});

function actionOffers() {
    $(document).on('mouseenter mouseleave', '.accordion-header', function(e) {
        $(this).find('.scene-title__action').toggleClass('active-action-offers', e.type === 'mouseenter')
    })
}

function styleGuide() {
    // Toggle Password
    $('body').on('click', '.sform-group__append i.toggle-password', function () {
        var input_group = $(this).closest('.sform-group__input-group');
        var input_icon = $(this);
        var input = input_group.find('input');

        if (input.attr('type') === 'password') {
            input.attr('type', 'text');
            input_icon.removeClass('icon--sicon-eye-close').addClass('icon--sicon-eye-open');
        } else {
            input.attr('type', 'password');
            input_icon.addClass('icon--sicon-eye-close').removeClass('icon--sicon-eye-open');
        }
    });

    // Step number
    $(document.body).on('click', '.step .step-button', function () {
        var t = $(this),
            o = t.closest('.step').find('.step-number').val(),
            i = '' === o ? 0 : parseInt(o, 10);
        t.is('.step-button--plus') ? i++ : i > 0 && t.is('.step-button--minus') && i--,
            t
                .closest('.step').find('.step-number')
                .val(i)
                .trigger('change');
    });

    // Tooltip
    // $('.stooltip').tooltip({
    //     html: true
    // });

    // Input Search
    $('.sform-control[type="search"]').on('keyup', function () {
        var $this = $(this);

        if ($this.val()) {
            $this.closest('.sform-group__input-group').find('.search-delete').show();
        } else {
            $this.closest('.sform-group__input-group').find('.search-delete').hide();
        }
    });

    $('.search-delete').on('click', function () {
        $(this).closest('.sform-group__input-group').find('.sform-control[type="search"]').val('');
        if ($(this).closest('.sform-group__input-group').find('.sform-control[type="search"]').is('#pm-search')) {
            $('.tab--video-progress').removeClass('hide');
            $('.project-chapter-item-search').remove();
        }else {
            $('.skills-item').removeClass('selected');
            let allItem =  $('.nav-item .notification.notification--blue');
            allItem.addClass('hide');
            allItem.attr('value', '0');
            allItem.html('0');
            $('.psearch-section.psearch-artist-wrap').remove();
        }
        $(this).hide();
    });

    Date.prototype.addDays = function (days) {
        let date = new Date(this.valueOf());
        date.setDate(date.getDate() + days);
        return date;
    };
    let startDate = new Date();
    let endDate = new Date().addDays(5);
    // $('#deadline-date').daterangepicker({
    //     startDate: startDate,
    //     endDate: endDate,
    // });
    // $('#deadline-date').datepicker({
    //     format: 'yyyy/m/d',
    //     locale: 'ja',
    //     forceParse: false,
    // });
    $('#deadline-date').val(moment(endDate).format('YYYY/M/D'))
    // $('#deadline-date').datepicker('setDate',  moment(endDate).format('YYYY/M/D'));
    $('.select-deadline_time').val('10:00');

};

function initmCustomScrollbar() {
    $('.custom-scrollbar.custom-scrollbar--vertical').mCustomScrollbar({
        theme: 'minimal-dark',
        axis: 'y'
    });

    $('.custom-scrollbar.custom-scrollbar--horizontal').mCustomScrollbar({
        theme: 'minimal-dark',
        axis: 'x'
    });

    setTimeout(function () {
        if ($('.custom-scrollbar--bottom').length) {
            $('.custom-scrollbar--bottom').mCustomScrollbar('scrollTo', 'bottom', {
                scrollEasing: 'easeOut'
            });
        }
    }, 500);
};

function sScrollbarBottom() {
    $('.mscrollbar--bottom').each(function () {
        let $this = $(this);

        setTimeout(function () {
            $this.scrollTop($this[0].scrollHeight);
        }, 500);
    });
};

function messengerAudio() {
    newWavesurferInit()
};

function updatePeakMessage(wavesurfer, file_id) {
    wavesurfer.on('waveform-ready', function () {
        let peaks = wavesurfer.backend.mergedPeaks;
        let peaks_string = peaks.join(" ");
        // for (let i = 0; i < peaks.length; i++) {
        //     peaks_string += String(Math.round(peaks[i] * Math.pow(10, 8)) / Math.pow(10, 8)) + " ";
        // }
        let values = {
            "file_id": file_id,
            "peaks": peaks_string,
        };
        if (file_id && peaks !== '') {
            $.ajax({
                type: "POST",
                url: "/messenger/update_peaks_file",
                data: values,
                dataType: 'json',
                success: function (data) {
                    console.log("success");
                    let message_dom;
                    if (data.file_id) {
                        message_dom = $('.mmessenger--audio-wave[data-file-id^=' + data.file_id + ']');
                        if (message_dom.length) {
                            message_dom.find('.s-audio-source').attr('data-peaks-loaded', data.peaks_loaded)
                        }
                    }
                },
                error: function (e) {
                    console.log(e);
                }
            });
        }
    });
}

function convertSecondsToTime(seconds) {
    // https://stackoverflow.com/questions/1322732/convert-seconds-to-hh-mm-ss-with-javascript
    if (!isNaN(seconds)) {
        var time;
        time = new Date(seconds * 1000).toISOString().substr(11, 8);

        var time_arr = time.split(':');

        if (time_arr.length > 2 && time_arr[0] == '00') {
            time = time_arr[1] + ':' + time_arr[2];
        }

        return time;
    }
};

let list_file_name = [];
let list_folder_name = [];
let list_temp_folder_name = [];

var typingTimer;                //timer identifier
var doneTypingInterval = 2000;
async function doneTyping (content, offerID=null) {
    if($('.mcomment-input-text').attr('type_input') !== 'input') {
        if(!offerID){
            return;
        }
    }
    let offer_id = !!offerID ? offerID : $(document).find('.mitem.mactive').attr('data-offer');
    if(window.location.href.toString().includes("tab=messenger") || window.location.href.toString().includes(`offer=${offer_id}`)){
        let data_form = new FormData();
        let content_value = content;
        let project_id = $(document).find('.project-item.active').attr('data-project-id');
        
        data_form.append('project_id', project_id);
        data_form.append('content', content_value);
        data_form.append('offer_id', offer_id);
        data_form.append('file_id', JSON.stringify(list_file_id));
        data_form.append('folder_id', JSON.stringify(list_folder_id));
        data_form.append('type_comment', '1');
        data_form.append('id_time', id_time);

        // await $.ajax({
        //     type: "POST",
        //     contentType: false,
        //     processData: false,
        //     cache: false,
        //     url: "/message/save_draft_message",
        //     data: data_form,
        //     success: function (data) {
        //        console.log("success");
        //     },
        //     fail: function (data) {
        //         // toastr.error(gettext('Something went wrong!'));
        //     },
        //     error: function(xhr, status, error) {
        //         // var err = eval("(" + xhr.responseText + ")")
        //         // alert(err.message);
        //     },
        //     complete: function () {
        //     }
        // });
    }
}

function commentInput() {
    if ($('.mcommment').length > 0) {
        $('.mcommment').each(function () {
            var $comment_input = $(this);
            var comment_id = $comment_input.attr('id');
            var mattach =  $('.mattach:not(.mattach-form)');
            var mattach_id = mattach.attr('id');

            Dropzone.autoDiscover = false;

            var previewNode = $comment_input.find('.mattach-template');

            var previewTemplate = previewNode.parent().html();
            previewNode.parent().empty();

            window.addEventListener('dragover', function (e) {
                e = e || event;
                e.preventDefault();
            }, false);
            window.addEventListener('drop', function (e) {
                e = e || event;
                e.preventDefault();
            }, false);

            mattach.find('.mattach-drop').hide();
            mattach.find('.mattach-overlay').hide();

            var comment_count = $('.mcommment').length;

            if (comment_count > 1) {
                mattach.closest('.maction').on('dragover', function (e) {
                    var dt = e.originalEvent.dataTransfer;
                    if (dt.types && (dt.types.indexOf ? dt.types.indexOf('Files') != -1 : dt.types.contains('Files'))) {
                        mattach.find('.mattach-overlay').show();
                        mattach.find('.mattach-drop').show();
                    }
                });

                mattach.closest('.maction').on('drop', function (e) {
                    mattach.find('.mattach-overlay').hide();
                    mattach.find('.mattach-drop').hide();
                    console.log('drop');
                });

                mattach.closest('.maction').on('dragleave', function (e) {
                    // mattach.find('.mattach-overlay').hide();
                    // mattach.find('.mattach-drop').hide();
                    console.log('drag leave');
                });
            } else {
                $(window).on('dragover', function (e) {
                    if (!$('#modal-edit-offer').hasClass('in')) {
                        var dt = e.originalEvent.dataTransfer;
                        if (dt.types && (dt.types.indexOf ? dt.types.indexOf('Files') != -1 : dt.types.contains('Files'))) {
                            if (!$('.modal.in').length) {
                                mattach.show();
                                mattach.find('.mattach-overlay').show();
                                mattach.find('.mattach-drop').show();
                            }
                        }
                    }
                });

                $(window).on('drop', function (e) {
                    mattach.hide();
                    mattach.find('.mattach-overlay').hide();
                    mattach.find('.mattach-drop').hide();
                });

                $(window).on('dragleave', function (e) {
                    // mattach.find('.mattach-overlay').hide();
                    // mattach.find('.mattach-drop').hide();
                });
            }
            mattach.find('.mattach-overlay').on('click', function (e) {
                mattach.hide();
                mattach.find('.mattach-overlay').hide();
                mattach.find('.mattach-drop').hide();
            });
            $('#' + mattach_id + '-form').append(csrf);
            mzdrop = new Dropzone('#' + mattach_id + '-form', {
                autoDiscover: false,
                previewTemplate: previewTemplate,
                maxFiles: 10,
                maxFilesize: 4500,
                timeout: 900000,
                params:{'list_id': list_file_id},
                previewsContainer: '#' + comment_id + ' .mattach-previews',
                clickable: '#' + comment_id + ' .mattach-label',
                autoProcessQueue: false,
                autoQueue: false,
            });

            mzdrop.on('addedfile', function (file, e) {
                $('.mcomment-send').addClass('active');
                file_names = [];
                mzdrop["sended"] = false;
                let key_file = "";
                let file_dom = $(file.previewElement);
                let path = "";
                if(is_exceeded_length){
                    if(!mzdrop.printed_err){
                        toastr.error("Folder'name is too long.");
                    }
                    let page = getPage(file_dom);
                    let folder_ids = Object.values(list_temp_folder_id);
                    for (let folder_id of folder_ids){
                        delete_folder(folder_id, page);
                    }
                    file.not_created = true;
                    mzdrop.removeFile(file);
                    list_temp_folder_id = {};
                    list_files_folders = {};
                    list_temp_folder_name = [];
                }else{
                    list_folder_id = {...list_folder_id, ...list_temp_folder_id};
                    list_folder_name.concat(list_temp_folder_name);
                    if(!jQuery.isEmptyObject(list_files_folders)){
                        for(const key in list_files_folders){
                            if (list_files_folders[key].includes(file.name)){
                                path = key;
                                let index = list_files_folders[key].indexOf(file.name);
                                list_files_folders[key].splice(index, 1);
                                if(! list_files_folders[key].length){
                                    delete list_files_folders[key];
                                }
                                break;
                            }
                        }
                        if(jQuery.isEmptyObject(list_files_folders)){
                            list_files_folders = {};
                            list_temp_folder_name = [];
                            list_temp_folder_id = {};
                        }
                    }
                    let file_preview = $('.mattach-preview-container').find(".mcommment-file__name");
                    for (let i = 0; i< file_preview.length; i++){
                        if($(file_preview[i]).text() == file.name){
                            let real_path = path.substring(0, path.indexOf("---")) + path.slice(path.indexOf("/"));
                            $(file_preview[i]).text(real_path + file.name);
                            break;
                        }
                    }
                    if (path === '' ) {
                        list_file_name.push(file.name)
                    }
                    uploadFileS3(file, file_dom, path);
                }
            });

            mzdrop.on('dragenter', function () {
                mattach.find('.mattach-file').addClass('active');
            });

            mzdrop.on('dragover', function () {
                mattach.find('.mattach-file').addClass('active');
            });

            mzdrop.on('dragleave', function () {
                mattach.find('.mattach-file').removeClass('active');
            });

            mzdrop.on('drop', function (e) {
                is_exceeded_length = false;
                mzdrop.printed_err = false;
                $('.mattach-file').removeClass('active');
                mattach.hide();
                mattach.find('.mattach-overlay').hide();
                mattach.find('.mattach-drop').hide();
                $('.mcomment-bottom').trigger('click');
                let items = e.dataTransfer.items;
                let today = new Date();
                let epoch = Math.floor(today/1000);
                let dateTime = "---"+ epoch;
                let page = getPage(mattach);

                for (let i=0; i<items.length; i++) {
                    var item = items[i].webkitGetAsEntry();
                    if(item.isFile){
                        //list_file_name.push(item.name);
                        traverseFileTree(item, "", 0, "", page);
                    } else {
                        list_temp_folder_name.push(item.name);
                        traverseFileTree(item, item.name+dateTime+"/", 0, "", page);
                    }
                }
            });

            mzdrop.on('error', function (file) {
                console.log('error');
            });

            mzdrop.on('removedfile', function (file) {
                mzdrop.printed_err = true;
                if(file.not_create){
                    return
                }
                if (mzdrop.files.length == 0 && !$comment_input.find('.mcomment-input-text').val()) {
                    mattach.parents('.mcommment').find('.mcomment-send').removeClass('active');
                }
                if(!file["not_created"] && !mzdrop["sended"]){
                    file_id = Object.keys(list_file_id).find(key => list_file_id[key] === file.name);
                    if (file.name.indexOf(list_file_name) >= 0) {
                        list_file_name.pop(file.name.indexOf(list_file_name));
                    }
                    if(file_id){
                        delete list_file_id[file_id];
                        $.ajax({
                            type:"POST",
                            data: {
                                'file_id': file_id,
                                'message_type': getPage(mattach)
                            },
                            url: '/upload/remove_file',
                            success: function(data){
                                let list_folder_removed = data.removed_folder_id.split(",");
                                for(let key in list_folder_id){
                                    if(list_folder_removed.includes(list_folder_id[key])){
                                        delete list_folder_id[key]
                                    }
                                }
                                $('.mcomment-input-text').attr('type_input', 'input');
                                setTimeout(async () => {
                                    if($(document).find('textarea.mcomment-input-text').length){
                                        await doneTyping($(document).find('textarea.mcomment-input-text').val());
                                    }
                                }, 2000);
                            },
                        });
                    }
                }
            });

            $(document).on('click', '.mcommment-file__delete', async function () {
                let offer_id = $(document).find('.mitem.mactive').attr('data-offer');
                currentOffer = offer_id;
                if(window.location.href.toString().includes("tab=messenger") || window.location.href.toString().includes(`offer=${offer_id}`)){
                    let file_id = $(this).parents('.mattach-template').attr('data-file-id');
                    list_file_remove.push(file_id);
                    let file_name = $(this).parent().find('.mcommment-file__name').html();
                    file_name = file_name.split('/')[file_name.split('/').length - 1];
                    file_id = Object.keys(list_file_id).find(key => list_file_id[key] === file_name);
                    $(this).parents('.mattach-template').remove();
                        if(file_id){
                            delete list_file_id[file_id];
                            $.ajax({
                                type:"POST",
                                data: {
                                    'file_id': file_id,
                                    'message_type': getPage(mattach)
                                },
                                url: '/upload/remove_file',
                                success: function(data){
                                    let list_folder_removed = data.removed_folder_id.split(",");
                                    for(let key in list_folder_id){
                                        if(list_folder_removed.includes(list_folder_id[key])){
                                            delete list_folder_id[key]
                                        }
                                    }
                                },
                            });
                            $(document).find('.mcomment-input-text').attr('type_input', 'input');
                            if($(document).find('textarea.mcomment-input-text').length){
                                await doneTyping($(document).find('textarea.mcomment-input-text').val());
                            }
                        }
                }
            });

            mattach.parents('.mcommment').find('.mcomment-send').on('click', function (e) {
                e.preventDefault();
            });

            // https://codepen.io/vsync/pen/frudD
            $(document).on('input.mcomment-input-text', 'textarea.mcomment-input-text', function () {
                calculateHeightCommentInput(this)
                $(this).attr('type_input', 'input');
                clearTimeout(typingTimer);
                typingTimer = setTimeout(async () => {
                    if($(document).find('textarea.mcomment-input-text').length){
                        await doneTyping($('textarea.mcomment-input-text').val())
                    }
                }, doneTypingInterval);
            });
        });
    }
};

function calculateHeightCommentInput(inputElement ) {
    let editing = false;
    if ($('.mcomment-send.input-editing').length > 0){
        editing = true;
    }
            $(inputElement).css('height', 'auto');
            var height = parseInt(inputElement.scrollHeight)
                + parseInt($(inputElement).css('border-top-width'))
                - parseInt($(inputElement).css('padding-top'))
                + parseInt($(inputElement).css('border-bottom-width'))
                - parseInt($(inputElement).css('padding-bottom'));

            if ($(inputElement).val()) {
                $(inputElement).parents('.mcommment').find('.mcomment-send').addClass('active');
            } else {
                if (!$(inputElement).parents('.mcomment-top').find(".mattach-template").length && !$('.mcommment .mcomment-send').hasClass('input-editing')) {
                    $(inputElement).parents('.mcommment').find('.mcomment-send').removeClass('active');
                }
            }

        valInput = $(inputElement).val();
        var maxHeight = $(window).height() - 64 - 75 - 40 - 80 - 48;
        if ($('.prdt').length > 0) {
            //  $(inputElement).css('overflow', 'hidden')
            if (height > maxHeight) {
                const inputComment = $('.mcomment-input-text');
                inputComment.scrollTop(inputComment[0].scrollHeight)
                $(inputElement).height(maxHeight + 'px');
                $(inputElement).css('overflow', 'auto')
            } else if (height <= maxHeight) {
                $(inputElement).height(height + 'px');
            } else {
                if (editing) {
                    $(inputElement).height(height + 'px');
                } else {
                    $(inputElement).height(height + 'px');
                }
            }
        } else {
            $(inputElement).height(height + 'px');
            if (height > maxHeight) {
                $(inputElement).height(maxHeight + 'px');
            }
        }
    
}



function delete_folder(folder_id, page){
    $.ajax({
        type: "POST",
        datatype: "json",
        url: "/upload/delete_folder",
        data: {
            'folder_id': folder_id,
            'page': page
        },
        success: function () {
            console.log('deleted folder');
        }
    })
}

function modalInModal() {
    $(document).on('hidden.bs.modal', function (event) {
        if ($('.modal:visible').length) {
            $('body').addClass('modal-open');
        }
    });
};

function createThread() {
    $('#create-thread-policy').on('change', function () {
        var $this = $(this);
        var policyChecked = $('#create-thread-policy:checked').length > 0;

        if (policyChecked) {
            $this.parents('.create-thread').find('.create-thread__action .btn').attr('disabled', false);
            $this.parents('.create-thread').find('.create-thread__action .btn').removeClass('btn--disabled');
        } else {
            $this.parents('.create-thread').find('.create-thread__action .btn').attr('disabled', true);
            $this.parents('.create-thread').find('.create-thread__action .btn').addClass('btn--disabled');
        }
    });

    $('.create-thread-submit').on('click', function () {
        $('#modal-create-thread, #modal-create-thread-2').modal('hide');
    });
};

function seenMessage(offer_id) {
    $.ajax({
        type: "POST",
        datatype: "json",
        url: "/offer_message/update_seen",
        data: {
            'offer_id': offer_id,
        },
        success: function () {
            console.log('update seen successful');
        }
    })
}

function actionThread() {
    $(document).on('click', '.mlist .mitem', async function () {
        let offer_id = $(this).data('offer');
        if ($(this).hasClass('mactive')) {
            if ($('.mcontent').find('.mmessage-list').hasClass('not-seen')) {
                seenMessage(offer_id);
            }
        } else {
            const preOfferElement = $(this).parent().find('.mscene.mitem.mactive')
            if(preOfferElement.length){
                if($(document).find('textarea.mcomment-input-text').length){
                    await doneTyping($(document).find('textarea.mcomment-input-text').val(), preOfferElement.data('offer'))
                }
                preOfferElement.removeClass('mactive');
            }
            $(this).addClass('mactive');
            if (!$('.madd-new #offer-filter').is(':checked') && !$(this).parents('.list--offers-search').length) {
                let item_dones = $('.mitem.mchecked[data-offer!=' + offer_id + ']');
                item_dones.each(function () {
                    if ($(this).find('.notification.notification--blue.hide').length) {
                        $(this).hide('slow', function () {
                            $(this).remove();
                        })
                    }
                })
            }

            is_loading_offer_message = false;
            current_load_offer_message = 0;
            loadMessage(offer_id);
            if ($('.mcontent').find('.mmessage-list').hasClass('not-seen')) {
                seenMessage(offer_id);
            }
            first_load_page = false;
            $(this).siblings().removeClass('mactive');
            // getDraftMessageDM(offer_id);
            let url = new URL(window.location);
            url.searchParams.set("offer", offer_id);
            let refresh = url.toString();
            setTimeout(function () {
                window.history.pushState({path: refresh}, '', refresh);
            }, 500);
        }

        if ($(window).width() < max_width_sp_device) {
            $('.mcolumn--main').addClass('mshow');
            $('.mcolumn--left').css('display', 'none');
            $(this).parents('.martist').addClass('hide-banner');
            $('.pbanner__image').find('.pbanner__icon-expand-container').addClass('hide-button');
            if($('.project-item.active').find('.block-navigation-bar').hasClass('hide') && !$('.block-navigation-bar').hasClass('hide')) {
                $(this).parents('.martist').css('margin-top', '-160px');
            } else {
                // $(this).parents('.martist').css('margin-top', '-280px');
            }
            $('.btn-tutorial-sp').addClass('hide-button-tutorial');
            calcTopListMessage();
            $('.prdt').addClass('open_sp_modal');
            if ($(window).width() < max_width_sp_device && $('.project-tab-messenger.active').length > 0) {
                resetPositionCommentBox();
            }
        }
        let navigationBar = $('.block-navigation-bar');
        let footerCommentBlockOffer = $('#footerCommentBlockOffer');
        let bottomString = navigationBar.css('bottom');
        let bottomValue = parseInt(bottomString.replace('px', ''));
        if (bottomValue < 0 && footerCommentBlockOffer.length > 0) {
            footerCommentBlockOffer.css({'bottom': 0, 'transition': 'bottom 0.3s ease'})
        } else {
            footerCommentBlockOffer.css('bottom', footerCommentBlockOffer.css('bottom'))
        }
    });

    $(document).on('click', '.mcolumn.mcolumn--main .mcolumn-next', function () {
        $('.mcolumn--right').addClass('mshow');
        $('.mcolumn--right').css('display', 'inherit');
        $('.mcolumn--main').removeClass('mshow');
        $('.mcolumn--main').css('display', 'none');
    });

    $(document).on('click', '.mcolumn-back', function () {
        if ($(this).closest('.mcolumn').hasClass('mcolumn--main') && $(this).parents('.martist').hasClass('hide-banner')) {
            if ($(window).width() < max_width_sp_device) {
                calcTopListMessage();
                $('.prdt').removeClass('open_sp_modal');
            }
            $('.pbanner__image').find('.pbanner__icon-expand-container').removeClass('hide-button');
            $(this).parents('.martist').css('margin-top', 'auto');
            $('.btn-tutorial-sp').removeClass('hide-button-tutorial');
            appHeight();
            $('.mcolumn--left').css('display', 'inherit');
        } else if ($(this).closest('.mcolumn').hasClass('mcolumn--right')) {
            $('.mcolumn--main').addClass('mshow');
            $('.mcolumn--main').css('display', 'inherit');
            $('.mcolumn--right').css('display', 'none');
        }
        $(this).closest('.mcolumn').removeClass('mshow');
    });
};

function calcTopListMessage() {
    let header_global = $('.sheader');
    let banner_project = $('.new-banner-project');
    let height_header_global = header_global.outerHeight();
    let height_banner_project = banner_project.outerHeight();
    let total_space_top = height_header_global + height_banner_project;
    let top_header_global = parseInt(header_global.css('top').replace('px', ''));
    let message_tab = $('.project-tab.project-tab-messenger.active');
    if (message_tab.length > 0) {
        if (top_header_global < 0) {
            message_tab.css('top', `${height_banner_project}px`)
        } else {
            message_tab.css('top', `${total_space_top}px`)
        }
    }
}

function actionMessage() {
    messengerAction();
}

function messengerAction() {
    let $main_target = $(document);

    $main_target.on('click', '.mcomment-bottom', function () {
        $(this).find('.mcomment-input-placeholder').hide();
        $(this).closest('.mcommment').find('.mcomment-top').show(200);
        $(this).closest('.mcommment').find('.mcomment-input-text').focus();
        $('.prdt .DM-box-container .mmessage-list').css('padding-bottom', '120px')
    });

    showInforMessage();

    // edit
    $main_target.on('click', '.mmessage-edit', function (e) {
        e.stopPropagation();
        e.preventDefault();
        list_file_remove = [];
        mzdrop["sended"] = true;
        mzdrop.removeAllFiles();
        list_file_id = {};
        list_file_name = [];
        list_folder_name = [];
        let $message_target = $(this).parents('.mmessage');
        let message_id = $message_target.attr('data-message-id');
        $message_target.toggleClass('editing');
        $('.mcommment-file').remove();
        $main_target.find('.mcomment-pin').removeClass('active hide');
        $main_target.find('.mmessage.reply').removeClass('active reply');
        $main_target.find('.mcomment-input').removeClass('is-reply is-pin');
        $('.cscene-vertical').removeClass('active');
        $message_target.removeClass('reply');
        $('.mmessage[data-message-id!=' + message_id + ']').removeClass('editing');
        if ($(document).width() > maxWidthIpadDevice) {
            $('.prdt .mmessage-list').addClass('pd-main-message')
        }
        $(this).toggleClass('active');
        if ($(this).hasClass('active')) {
            $message_target.find('.mmessage-edit').removeClass('active');
            $(this).addClass('active');
        }

        if ($(this).parents('.mmessage').hasClass('editing')) {
            let pin_dom = $message_target.find('.video-pin-time');
            if (pin_dom.length > 0) {
                $main_target.find('.mcomment-pin').trigger('click');
                let current_time = $message_target.find('.video-pin-start').text();
                $main_target.find('.mcomment-input-title span').text(current_time);
                let scene_id = pin_dom.parents('.s-audio').attr('data-scene-id');
                goToSceneActive(scene_id);
            }

            $main_target.find('.mcommment').addClass('border-editing');
            $main_target.find('.btn-remove-msg').addClass('d-block')
            $main_target.find('.block-remove-msg-editing').removeClass('d-none')
            $main_target.find('.mcomment-send').addClass('input-editing');
            let content = '';
            let content_dom = $(this).parents('.mmessage').find('.s-text, .s-audio-text, .s-filetext');
                if (content_dom.length > 0) {
                    content = content_dom.text().replace(/<(.|\n)*?>/g, '');
                }
            $main_target.find('.mcommment .mcomment-bottom').click();
            $main_target.find('.mcommment .mcomment-input-text').val(content);
            calculateHeightCommentInput($main_target.find('.mcommment .mcomment-input-text')[0])
            let files_dom = $(this).parents('.mmessage').find('.s-file, .s-audio-source');
            let files_uploaded_html = '';
            if (files_dom) {
                $(this).parents('.mmessage').find('.s-file, .s-audio-source').each(function () {
                    let file_name = '';
                    if ($(this).hasClass('s-file')) {
                        if ($('.owner-top').hasClass('scene-detail-page')) {
                             file_name = $(this).find('.file-name-cmt').html();
                        } else {
                            file_name = $(this).html();
                        }
                    } else {
                        file_name = $(this).attr('title');
                    }
                    let file_id = $(this).parents('.mmessenger--file').attr('data-file-id');
                    if(!file_id){
                        file_id = $(this).parents('.mmessenger--audio-wave').attr('data-file-id');
                    }
                    if (file_name) {
                        if (file_id === file_contract || file_id === file_bill) {
                            files_uploaded_html += '<div class="mattach-template file-item-deleted collection-item item-template" data-file-id="' + file_id + '">' +
                                '<div class="mattach-info" data-dz-thumbnail="">' +
                                '<div class="mcommment-file">' +
                                '<div class="determinate" style="width:0" data-dz-uploadprogress=""></div>' +
                                '<div class="mcommment-file__name" data-dz-name="">' + file_name + '</div>' +
                                '<div class="mcommment-file__delete" data-dz-remove="">' +
                                '</div>' +
                                '</div>' +
                                '</div>' +
                                '</div>';
                        } else {
                            files_uploaded_html += '<div class="mattach-template file-item-deleted collection-item item-template" data-file-id="' + file_id + '">' +
                                '<div class="mattach-info" data-dz-thumbnail="">' +
                                '<div class="mcommment-file">' +
                                '<div class="determinate" style="width:0" data-dz-uploadprogress=""></div>' +
                                '<div class="mcommment-file__name" data-dz-name="">' + file_name + '</div>' +
                                '<div class="mcommment-file__delete" data-dz-remove="">' +
                                '<i class="icon icon--sicon-close"></i>' +
                                '</div>' +
                                '</div>' +
                                '</div>' +
                                '</div>';
                        }

                    }
                })
            }
            $('.maction .mattach-previews.collection').empty().append(files_uploaded_html);

            $main_target.find('.mcommment-file__delete').on('click', function () {
                let file_id = $(this).parents('.mattach-template').attr('data-file-id');
                list_file_remove.push(file_id);
                $(this).parents('.mattach-template').remove();
            });

            $main_target.find('.mcomment-send').addClass('active');
        } else {
            resetInputMessage($main_target);
        }
        let activeAudio = parseInt($(this).parents('.mmessage--sent').find('.s-audio').attr('data-wavesurfer'));
        let activeAudioSentSceneDetailComment = $(this).parents('.mmessage--sent').find('.comments-audio-block .s-audio');
        for (let i = 0; i < activeAudioSentSceneDetailComment.length; i++) {
            checkActive($(this), parseInt(activeAudioSentSceneDetailComment[i].getAttribute('data-wavesurfer')));
        }
        checkActive($(this), activeAudio);
    });

    $main_target.on('click', '.mmessage-delete', function (e) {
        let $message_target = $(this).parents('.mmessage');
        let message_id = $message_target.attr('data-message-id');
        $message_target.removeClass('editing');
        resetInputMessage($main_target);
        $('.mcomment-message .mcommment-file').remove();
        e.stopPropagation();
        e.preventDefault();
        let url_page;
        let data = new FormData();
        data.append('message_id', message_id);

        if (messenger_page === 'messenger_artist' || (messenger_page === 'top_page' && $('.pbanner-tab.active[data-show=messenger]').length)) {
            url_page = '/messenger/delete_message';
        } else if (messenger_page === 'top_page') {
            if ($('.project-tab-product-comment').hasClass('active')) {
                data.append('type', 'project');
                let project_id = $('.project-item.active').attr('data-project-id');
                data.append('project_id', project_id);
            } else {
                data.append('type', 'scene');
            }
            url_page = "/top/delete_comment";
        }

        bootbox.confirm({
            message: gettext('Do you really want to delete this?'),
            buttons: {
                confirm: {
                    label: 'はい',
                    className: 'btn--tertiary btn-delete-message'
                },
                cancel: {
                    label: 'いいえ',
                    className: 'btn--primary btn-cancel-message'
                }
            },
            callback: function (result) {
                if (result) {
                    $.ajax({
                        type: "POST",
                        contentType: false,
                        processData: false,
                        cache: false,
                        data: data,
                        url: url_page,
                        success: function (data) {
                            console.log('OK');
                            autoLoadMore();
                        },
                        complete: function() {
                            resetFormContract();
                        }
                    });
                }
            }
        });
    });

    $main_target.on('click', '.mmessage-resolve', function (e) {
        let $message_target = $(this).parents('.mmessage');
        let message_id = $message_target.attr('data-message-id');
        $message_target.removeClass('editing');
        resetInputMessage($main_target);
        e.stopPropagation();
        e.preventDefault();
        let data = {};
        let resolved = $(this).hasClass('mmessage-resolved');

        let parent_dom = $main_target.parents('.pd-section');
        let $message_child = parent_dom.find('.mmessage[data-parent-id=' + message_id + ']');
        if (!resolved) {
            if (parent_dom.hasClass('show-comment-unresolved')) {
                if ($message_target.find('.s-audio-control.active').length) {
                    $message_target.find('.s-audio-control.active').trigger('click')
                }
                if ($message_child.find('.s-audio-control.active').length) {
                    $message_child.find('.s-audio-control.active').trigger('click')
                }
                $message_target.fadeOut('slow');
                $message_child.fadeOut('slow');
            }
            setTimeout(function () {
                $message_target.addClass('resolved');
                $message_child.addClass('resolved');
            }, 300);

            $message_target.find('.mmessage-resolve ').addClass('mmessage-resolved');
            $message_child.find('.mmessage-resolve ').addClass('mmessage-resolved');
            if ($('.owner-top').hasClass('scene-detail-page') || $('.owner-top').hasClass('prdt')){
                 $message_target.find('.mmessage-resolve .txt-item-comment').text('進行中に戻す')
            }
        } else {
            $message_target.css('display', 'flex');
            $message_child.css('display', 'flex');
            $message_target.find('.mmessage-resolve').removeClass('mmessage-resolved');
            $message_child.find('.mmessage-resolve').removeClass('mmessage-resolved');
            $message_target.removeClass('resolved');
            $message_child.removeClass('resolved');
             if ($('.owner-top').hasClass('scene-detail-page') || $('.owner-top').hasClass('prdt')){
                 $message_target.find('.mmessage-resolve .txt-item-comment').text('解決済みにする')
            }
        }

        if ($(this).parents('.pd-product-comment').length > 0) {
            data = {
                'comment_id': message_id,
                'resolved': resolved,
                'type': 'project',
            };
        } else {
            data = {
                'comment_id': message_id,
                'resolved': resolved,
                'type': 'scene',
            };
        }

        $.ajax({
            type: "POST",
            datatype: "json",
            url: "/top/resolve_comment",
            data: data,
            success: function () {
                console.log('ok');
            },
            error: function () {
                if (resolved) {
                    $message_target.addClass('resolved');
                    $message_child.addClass('resolved');
                    $message_target.find('.mmessage-resolve ').addClass('mmessage-resolved');
                    $message_child.find('.mmessage-resolve ').addClass('mmessage-resolved');
                } else {
                    $message_target.css('display', 'flex');
                    $message_child.css('display', 'flex');
                    $message_target.find('.mmessage-resolve').removeClass('mmessage-resolved');
                    $message_child.find('.mmessage-resolve').removeClass('mmessage-resolved');
                    $message_target.removeClass('resolved');
                    $message_child.removeClass('resolved');
                }
                autoLoadMore();
            }
        })

    });

    $main_target.on('click', '.mmessage-reply', function (e) {
        e.preventDefault();
        e.stopPropagation();
        list_file_remove = [];
        mzdrop["sended"] = true;
        mzdrop.removeAllFiles();
        list_file_id = {};
        $main_target.find('.mcommment .mcomment-input-text').val('');
        $('.mcommment-file').remove();
        $main_target.find('.mcomment-send.active').removeClass('active');
        $main_target.find('.mmessage').removeClass('editing');
        $main_target.find('.mcomment-send').removeClass('input-editing');
        $main_target.find('.mcommment').removeClass('border-editing');
        $main_target.find('.mcomment-pin').removeClass('active hide');
        if ($(document).width() > maxWidthIpadDevice) {
            $('.prdt .mmessage-list').removeClass('pd-main-message')
        }
        // $main_target.find('.mmessage-reply.active').removeClass('active');
        $('.cscene-vertical').removeClass('active');
        $main_target.find('.mmessage').removeClass('reply');
        $(this).toggleClass('active');
        if ($(this).hasClass('active')) {
            $main_target.find('.mmessage-reply').removeClass('active');
            $(this).addClass('active');

            var message_component_close = $(this).closest('.mmessage-component');
            // if (message_component_close.length < 1) {
            //     message_component_close = $(this).closest('.video-item-comment-content');
            // }
            let parent_id = $(this).parents('.mmessage').attr('data-message-id');
            $(this).parents('.mmessage').addClass('reply');
            message_component_close.find('.mcomment-input').removeClass('is-pin').addClass('is-reply');
            message_component_close.find('.mcomment-input').attr('data-parent-id', parent_id);
            message_component_close.find('.mcomment-input-title').html('<i class="icon icon--sicon-reply"></i>');
            message_component_close.find('.mcomment-bottom').trigger('click');
        } else {
            resetInputMessage($main_target);
        }
        let activeAudio = parseInt($(this).parents('.mmessage--received').find('.s-audio').attr('data-wavesurfer'));
        let activeAudioSceneDetailComment = $(this).parents('.mmessage--received').find('.comments-audio-block .s-audio');
        for (let i = 0; i < activeAudioSceneDetailComment.length; i++) {
            checkActive($(this), parseInt(activeAudioSceneDetailComment[i].getAttribute('data-wavesurfer')));
        }
        checkActive($(this), activeAudio);
        let activeAudioSent = parseInt($(this).parents('.mmessage--sent').find('.s-audio').attr('data-wavesurfer'));
        let activeAudioSentSceneDetailComment = $(this).parents('.mmessage--sent').find('.comments-audio-block .s-audio');
        for (let i = 0; i < activeAudioSentSceneDetailComment.length; i++) {
            checkActive($(this), parseInt(activeAudioSentSceneDetailComment[i].getAttribute('data-wavesurfer')));
        }
        checkActive($(this), activeAudioSent);
    });

    $(document).on('click', '.mcomment-input-close', function (e) {
        e.preventDefault();
        $('.mmessage-reply').removeClass('active');
        $(this).closest('.mcomment-input').removeClass('is-reply').removeClass('is-pin');
        $(this).closest('.mcomment-input').find('.mcomment-input-text').focus();
        resetInputMessage($main_target);

    });

    $main_target.on('click', '.mcomment-pin', function (e) {
        e.preventDefault();

        $('.mcomment-pin').toggleClass('active hide');
        var message_component_close = $(this).closest('.mmessage-component');

        message_component_close.find('.mcomment-input').toggleClass('is-pin');
        $('.cscene-vertical').toggleClass('active');
        $('.cscene-vertical').css({
            'border': 'none'
        })
            addBorderPreview(true);

        if ($(this).hasClass('active')) {
            let currentTime = '0:00';
            let video = $(this).parents('.pd-scene-title-detail').find('.cscene__variation.slick-current .cscene__version.slick-current.slick-active video')[0];
            if (video) {
                currentTime = video.currentTime;
            } else {
                let audio_dom = $(this).parents('.pd-scene-title-detail').find('.cscene__variation.slick-current .cscene__version.slick-current.slick-active .s-audio--white');
                if (audio_dom.length > 0) {
                    let current_wave_index = audio_dom.attr('data-wavesurfer');
                    let current_wave = wavesurfer_arr[current_wave_index];
                    currentTime = current_wave.getCurrentTime();
                } else {
                    currentTime = '';
                }
            }

            message_component_close.find('.mcomment-input-title').html('<i class="icon material-symbols-rounded">pin_drop</i>'+ '<span>' + msToTime(currentTime) +'</span>');
            message_component_close.find('.mcomment-bottom').trigger('click');
        } else {
            let $main_target = $('.mcolumn.mcolumn--main, .pd-comment__main');
            resetInputMessage($main_target);
        }

    });

    $main_target.on('click', '.icon--sicon-pin', function () {
        $('.mcomment-pin').removeClass('active hide');
        $('.mcomment-input').removeClass('is-pin');
        $('.cscene-vertical').removeClass('active');

        addBorderPreview();
        $main_target.find('.mcomment-input-text.mcomment-autoExpand').focus();
    })
};

function checkActive(controlDom, activeAudio) {
    if(controlDom.hasClass('active')) {
        setColorActive(wavesurfer_arr[activeAudio])
    }
    else {
        $('.s-audio-control').hasClass('active') ? setColorActive(wavesurfer_arr[activeAudio]) : setColorInActive(wavesurfer_arr[activeAudio]);
    }
}

function setColorInActive(wavesurfer) {
    if(wavesurfer) {
        wavesurfer.setWaveColor('rgba(83, 86, 90, 0.3)');
        wavesurfer.setProgressColor('#53565A');
    }
}

function setColorActive(wavesurfer) {
    if(wavesurfer) {
        // 白い背景でも見えるように青系の色に変更
        wavesurfer.setWaveColor('rgba(0, 154, 206, 0.3)'); // #009ace の30%透明度
        wavesurfer.setProgressColor('#009ace'); // 青色
    }
}

function datePicker() {
    if ($('.mcalendar').length > 0) {
        $('.mcalendar').each(function () {
            var date = new Date();
            date.setDate(date.getDate());

            var $this = $(this);
            var dates_deadline = $this.attr('data-dates-deadline');
            if (typeof dates_deadline === 'undefined') {
                return
            }
            dates_deadline = dates_deadline.split(',');
            var dates_disable = $this.attr('data-dates-disable');
            dates_disable = dates_disable.split(',');

            $this.datepicker({
                inline: true,
                weekStart: 1,
                startDate: date,
                multidate: false,
                format: 'dd-mm-yyyy',
                locale: 'ja',
                todayHighlight: true,
                todayBtn: true,
                debug: true,
                icons: {
                    previous: 'icon icon--sicon-prev',
                    next: 'icon icon--sicon-next',
                },
                beforeShowDay: function (date) {
                    var d = date;
                    var curr_date = ('0' + d.getDate()).slice(-2);
                    var curr_month = ('0' + (d.getMonth() + 1)).slice(-2);
                    var curr_year = d.getFullYear();
                    var formattedDate = curr_year + '-' + curr_month + '-' + curr_date;

                    if ($.inArray(formattedDate, dates_deadline) != -1) {
                        return {
                            classes: 'active-deadline'
                        };
                    }
                    if ($.inArray(formattedDate, dates_disable) != -1) {
                        return {
                            classes: 'busy-day'
                        };
                    }
                    return;
                }
            }).on('show', function (e) {
                console.log(22);

            }).on('changeDate', function (e) {
                $('.minfo-task').html('');
                let offer_id = $(this).parents('.mcolumn-content').attr('data-offer');
                let choosed_day = $(this).datepicker('getDate');
                if (choosed_day == 'Invalid Date') {
                    choosed_day = new Date()
                }

                let deadline = $.datepicker.formatDate("yy-mm-dd", choosed_day);
                let creator_id = $('#creator_settings__form').attr('data-id');
                if ($(this).hasClass('mcalendar--small')) {
                    $.ajax({
                        type: "POST",
                        url: "/messenger/get_task_deadline",
                        data: {
                            'offer_id': offer_id,
                            'deadline': deadline,
                            'creator_id': creator_id
                        },
                        dataType: 'json',
                        success: function (data) {
                            console.log("success");
                            $('.minfo-task').append(data.task_html)
                        },
                        error: function (e) {
                            console.log(e);
                        }
                    });

                }

            });

            setTimeout(function () {
                $this.find('.datepicker-days .today.day').addClass('disabled');
                $this.find('.datepicker-days .today.day').trigger('click');

                $this.find('.prev').html('<i class="icon icon--sicon-prev"></i>');
                $this.find('.next').html('<i class="icon icon--sicon-next"></i>');
            }, 500);

            setTimeout(function () {
                $this.find('.datepicker-days tfoot .today').trigger('click');
            }, 500);
        });
    }
}

function previewFile() {
    $(document).off('click', '.minfo-file_info, .mmessenger, .tfile-infor').on('click', '.minfo-file_info, .mmessenger, .tfile-infor', function () {
        let type = $(this).attr('data-type');
        let link = $(this).attr('data-link');
        let converted_link = $(this).attr('data-converted-link');
        let converted_path = $(this).attr('data-converted-path');
        let name = $(this).attr('data-name');
        let file_id = $(this).attr('data-file-id');
        let production_file = $(this).attr('data-type-file');
        if (!file_id) {
            file_id = $(this).attr('data-scene-id');
            if (file_id) {
                let version = $('.cscene__version.slick-slide[data-scene-id^=' + file_id + ']');
                let variation = version.parents('.cscene__variation');
                let version_index = version.attr('data-index');
                let variation_index = variation.attr('data-index');
                $('#sliderHorizontalNav0').slick('slickGoTo', variation_index);
                variation.find('.cscene__version-dot[data-index^=' + version_index + ']').trigger('click');
                
                const buttonDom = $(`.variation-button-container[data-scene-id=${file_id}]`);
                const listDom = buttonDom.closest('.list-variation-container');
                if(listDom.hasClass('hide')){
                    // if($(`.list-variation-container:not(.hide)`).length >= 3){
                    //     $($(`.list-variation-container:not(.hide)`)[0]).addClass('hide');
                    // }
                    listDom.removeClass('hide');
                    
                    $(`.list-variation-container`).each(function(index, item){
                        if($(this).hasClass('hide')) {
                            $($('.take-overlay-container')[index]).find('input').removeAttr('checked');
                        } else {
                            $($('.take-overlay-container')[index]).find('input').attr('checked', true);
                        }
                    });
                }
                buttonDom.trigger('click');
                $(".variation-button-container.active").get(0).scrollIntoView({behavior: 'smooth'});
            }
        }

        let data = new FormData();
        if (production_file === 'production_file') {
            data.append('production_file', production_file);
            let scene_title_id = $(this).attr('data-scene-title-id');
            $('#modal-document-popup').attr('data-scene-title-id', scene_title_id)
            $('#modal-image-popup').attr('data-scene-title-id', scene_title_id)
            $('#modal-video-popup').attr('data-scene-title-id', scene_title_id)
        }
        if (type === 'document') {
                data.append('file_id', file_id);
                previewFilePdf(data);
            $('#modal-document-popup').find('.file-name_modal').html(name);
            $('#modal-document-popup').attr('data-file-id', file_id);

            let offer_id = $('.button-confirm-contract-offer').parent().attr('data-offer');
            let project_id = $('.button-confirm-contract-offer').parent().attr('data-project');
            if(project_id && offer_id) {
                $(this).parents().find('.button-confirm-contract-offer').removeClass('disable');
            }
            previewFileStorage($(this), data)
        } else if (type === 'image') {
            data.append('file_id', file_id);
            $('#modal-image-popup').find('img').attr('src', link);
            $('#modal-image-popup').find('.image-popup__title').html(name);
            $('#modal-image-popup').attr('data-file-id', file_id);
            previewFileStorage($(this), data)
        } else if (type === 'video') {
            console.log(123)
            data.append('file_id', file_id);
            previewFileStorage($(this), data);
            $('#modal-video-popup').find('video').attr('src', converted_path ? converted_path : link);
            $('#modal-video-popup').find('video').attr('preload', 'auto');
            $('#modal-video-popup').find('.video-popup__title').html(name);
            $('#modal-video-popup').attr('data-file-id', file_id);
            let e = $('#modal-video-popup').find('video')[0];
            let ratio = e.videoWidth / e.videoHeight * 100 / 2;
            e.style.width = ratio.toString() + '%';
            if (converted_path) {
                hls = new Hls();
                hls.loadSource(converted_link);
                hls.attachMedia(e);
                hls.on(Hls.Events.MANIFEST_PARSED, function () {
                    e.play();
                });
            }
        } else if (type === 'other') {
            let file_id = $(this).attr('data-file-id');
            if (!file_id) {
                file_id = $(this).parents('.smodal').attr('data-scene-title-id');
            }
            if (file_id) {
                data.append('file_id', file_id);
                downloadFile(data, this);
            }
        }
        $('.smodal-download').off('click').on('click', function () {
            let file_id = $(this).parents('.smodal').attr('data-file-id');
            if (!file_id) {
                file_id = $(this).parents('.smodal').attr('data-scene-title-id');
            }

            if (file_id) {
                data.append('file_id', file_id);
                downloadFile(data)
            }
        })
    });
    $(document).on('hidden.bs.modal', '#modal-document-popup', function (e) {
        $('#modal-document-popup').find('iframe').attr('src', 'about:blank');
    })
}

function getDownloadAudio() {
    $(document).off('click', '.s-audio-name .icon--sicon-download:not(".icon--sicon-folder-download"), .icon--sicon-download:not(".icon--sicon-folder-download")')
    .on('click', '.s-audio-name .icon--sicon-download:not(".icon--sicon-folder-download"), .icon--sicon-download:not(".icon--sicon-folder-download")', function (e) {
        let data = new FormData();
        e.stopPropagation();
        let file_id;
        if ($(this).parents('.tfile-producttion-file').length > 0) {
            data.append('production_file', 'production_file');
            file_id = $(this).parents('.tfile-producttion-file').attr('data-scene-title-id')
        } else if ($(this).parents('.mfolder__sub').length) {
            file_id = $(this).parents('.mfolder__sub').data('file-id');
        } else if($(this).parents('#folderModal').length) {
            file_id = $(this).parents('.list-group-item').attr('data-file-id');
        }else {
            file_id = $(this).parents('.mmessage').attr('data-file-id');
            if (!file_id) {
                file_id = $(this).parents('.minfo-file_info').attr('data-file-id');
                if (!file_id) {
                    file_id = $(this).parents('.tfile-infor').attr('data-file-id');
                }
            }
            if (!file_id) {
                file_id = $(this).parents('.tfile-infor').attr('data-scene-id');
                data.append('type_infor', 'scene');
            }
        }

        if (file_id) {
            data.append('file_id', file_id);
            downloadFile(data, this)
        }
    })

    $(document).off('click', '.icon--sicon-folder-download').on('click', '.icon--sicon-folder-download', function (e) {
        e.stopPropagation();
        let folder = $(this).parent(".list-group-item");
        if (!folder.length){
            folder = $(this).parent(".parent-folder");
        }
        if (!folder.length){
            folder = $(this).parent(".mfolder__sub");
        }
        let folder_id = folder.find(".hasSub").attr("data-folder-id");
        DownloadFolder(folder_id, this);
    })
}

function DownloadFolder(folder_id, target){
    let data = new FormData();
    data.append('folder_id', folder_id);
    let href = window.location.href;
    let folderDom = $(target).parents('.parent-folder').length ? $(target).parent('.parent-folder').parents('.mfolder') : $(target).parent(".list-group-item");
    var list_icon_download = folderDom.find('.icon--sicon-download:not(.icon--sicon-folder-download)');
    list_icon_download = list_icon_download.toArray();
    if (!list_icon_download.length) {
        folderDom = folderDom.parents('.group-tree-modal');
        list_icon_download = folderDom.find('.icon--sicon-download:not(.icon--sicon-folder-download)');
        list_icon_download = list_icon_download.toArray();
    }
    let page;
    if (href.includes("/scene/")) {
        page = 'scene-comment';
    } else if (href.includes("tab=product-comment")) {
        page = 'project-comment';
    } else if (user_role !== 'admin') {
        page = 'messenger_owner';
    } else {
        page = 'messenger';
    }
    data.append('page', page);
    let offer_id;
    if ($('.mitem.mactive').length) {
        offer_id = $('.mitem.mactive').attr('data-offer');
        data.append('offer_id', offer_id);
    }
    $(target).prop('disabled', true);
    $.ajax({
        type: "POST",
        datatype: "json",
        contentType: false,
        processData: false,
        cache: false,
        url: "/get_link_download_folder",
        data: data,
        beforeSend: function (data) {
            // toastr.info('ダウンロードのリンクを作成しています。');
        },
        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            let links = response.links;
            links= links.split(",");
            var interval = setInterval(function(){
                let a_tag = document.createElement('a');
                a_tag.href = links[0];
                document.body.appendChild(a_tag);
                $(list_icon_download[0]).addClass('done');
                list_icon_download.shift();
                a_tag.click();
                links.shift();
                if (!links.length){
                    clearInterval(interval);
                    $(target).addClass('done');
                    $(target).prop('disabled', false);
                    // toastr.success('ダウンロードを開始しました');
                }
            },1500);
        },
        error: function () {
            toastr.error('エラーが発生しました');
        }
    })
}

function downloadFile(data, target=0, typeAction='') {
    let type_file;
    if (messenger_page === 'messenger_artist' || (messenger_page === 'top_page' && $('.pbanner-tab.active[data-show=messenger]').length)) {
        if (user_role !== 'admin') {
            type_file = 'message_owner';
        } else {
            type_file = 'message';
        }
    } else if (messenger_page === 'top_page') {
        if ($('.project-tab-product-comment').hasClass('active')) {
            type_file = 'project';
        } else {
            type_file = 'comment';
        }
    }
    data.append('type_file', type_file);

    if(target) {
        $(target).addClass('loading');
    }
    let offer_id;
    if ($('.mitem.mactive').length) {
        offer_id = $('.mitem.mactive').attr('data-offer');
        data.append('offer_id', offer_id);
    }

    $.ajax({
        type: "POST",
        datatype: "json",
        contentType: false,
        processData: false,
        cache: false,
        url: "/get_product_file_download_link",
        data: data,
        beforeSend: function (data) {
            if (!typeAction) {
                // toastr.info('ダウンロードのリンクを作成しています。');
            }
        },
        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            $(target).removeClass('loading').addClass('done');
            if (!typeAction) {
                let a_tag = document.createElement('a');
                a_tag.href = response.url;
                document.body.appendChild(a_tag);
                a_tag.click();
                // toastr.success('ダウンロードを開始しました');
            }
        },
        error: function () {
            if (!typeAction) {
                toastr.error('エラーが発生しました');
                $(target).removeClass('loading done');
            }
        }
    })
}

function previewFilePdf(data) {
    $('#modal-document-popup').find('iframe').attr('src', 'about:blank');
    let type_file;
    if (messenger_page === 'messenger_artist' || (messenger_page === 'top_page' && $('.pbanner-tab.active[data-show=messenger]').length)) {
        if (user_role !== 'admin') {
            type_file = 'message_owner';
        } else {
            type_file = 'message';
        }
    } else if (messenger_page === 'top_page') {
        if ($('.project-tab-product-comment').hasClass('active')) {
            type_file = 'project';
        } else {
            type_file = 'comment';
        }
    }
    data.append('type_file', type_file);
    let offer_id;
    if ($('.mitem.mactive').length) {
        offer_id = $('.mitem.mactive').attr('data-offer')
    }
    data.append('offer_id', offer_id);

    $.ajax({
        type: 'POST',
        datatype: 'json',
        contentType: false,
        processData: false,
        url: '/get_link_pdf_to_preview',
        data: data,
        success: function (data) {
            $('#modal-document-popup').find('iframe').attr('src', '/static/pdfjs/web/viewer.html?file=' + encodeURIComponent(data.url) + '#zoom=page-width');
        }
    })
}

function previewFileStorage(itemFile, data) {
    if ($('.pbanner-tab--exchange').hasClass('active')) {
        downloadFile(data, 0, 'preview')
    }
}

let file_contract;
let file_bill;

function loadMessage(offer_id) {
    let url_load = '';
    if (messenger_page === 'messenger_artist' || (messenger_page === 'top_page' && $('.pbanner-tab.active[data-show=messenger]').length)) {
        url_load = "/messenger/load_offer";
    }
    maudio_arr = [];
    wavesurfer_arr = [];
    $.ajax({
        type: "GET",
        datatype: "json",
        url: url_load,
        async: false,
        data: {
            'offer_id': offer_id,
        },
        success: function (data) {
            $(".mcolumn.mcolumn--main").html('');
            $(".mcolumn.mcolumn--main").append("<div class='offer-content-message offer-" + offer_id + "'><div class=\"messenger-detail\" ></div></div>");
            $(".mcolumn.mcolumn--right").html('');
            $(".mcolumn.mcolumn--right").append("<div class='offer-content-message offer-" + offer_id + "_infor" + "'><div class=\"messenger-infor\" ></div></div>");
            let active_offer = $('.offer-' + offer_id);

            active_offer.find(' .messenger-detail').html(data.html);
            let infor_offer = $('.offer-' + offer_id + '_infor');
            infor_offer.find(' .messenger-infor').html(data.infor_html);
            if (data.file_contract) {
                file_contract = data.file_contract
            }
            if (data.file_bill) {
                file_bill = data.file_bill
            }

            commentInput();
            messengerAudio();
            datePicker();
            removeDuplicatedDate();
            if ($('.mcolumn.mcolumn--right').hasClass('active')) {
                $('.mcolumn--right .mcolumn-header,.mcolumn--right .mcolumn-content').addClass('active');
            }

            // create message
            createMessage(active_offer);

            // edit
            editMessage(active_offer);

            scrollCommentBar();

            // seen message
            active_offer.on('click', '.mcomment-input-text, .mcomment-bottom', function () {
                if ($(this).parents('.mcontent').find('.mmessage-list').hasClass('not-seen')) {
                    let offer_id = $(this).parents('.maction').data('offer');
                    seenMessage(offer_id);
                }
            });

            $('.mscrollbar--bottom').mCustomScrollbar('scrollTo', 'bottom');
            if (!$('.prdt').length > 0) {
                $('.mcomment-top').hide();
            }

            let message = active_offer.find('.s-text');
            $.each(message, function (i, v) {
                if (!$(v).is('.align-center')) {
                    let regex = /(?:(?:https?|http|ftp):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:;,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!;:,.]*\)|[A-Z0-9+&@#\/%=~_|$])/igm;
                    v.innerHTML = v.innerHTML.replace(regex, "<a target='_blank' href=$&>$&</a>");
                }
            });

            initmCustomScrollbar();
            sScrollbarBottom();
            $('main')[0].style.overflow = 'hidden';
            $('.mmessage-list')[0].style.overflow = 'auto';

            if (user_role === 'admin' || user_role === 'master_admin'  && $('.pbanner-tab.active[data-show=messenger]').length) {
                actionMessengerArtist(infor_offer, active_offer);
            }

            $('.mmessage').last().addClass('load-lasted-message');
            total_offer_message = parseInt(data.total_page);
            scrollOfferMessage();
            autoLoadMoreOfferMessage();
            /*
                let planApproveBtn = $('.open-preview-plan-approve-modal');
                if (planApproveBtn.length > 0) {
                    console.log(planApproveBtn)
                    console.log(`${planApproveBtn.parent().width()}px`)
                    planApproveBtn.css('width', `${planApproveBtn.parent().width()}px`)
                }
             */
            scrollListComment($('.mmessage-list'))
            resizeCommentInput()
        },
        complete: function() {
            setWidthInput($('.mcommment[id="comment-input-1"]'));
             calcMoreActionComment($('.prdt .mmessage'));
             calcPositionDropdownComment2();
             hoverDropdownMessage()
            hoverBtnActionMessage()
            clickBtnActionMessage()
            resizeCommentInput()
            setTimeout(function () {
                calcPositionElementDM()
            }, 600)
        }
    })
}

function calcPositionElementDM() {
    let navigationBar = $('.block-navigation-bar');
    let footerCommentBlockOffer = $('#footerCommentBlockOffer');
    if (navigationBar.length > 0 && navigationBar.hasClass('hide') && footerCommentBlockOffer.length > 0) {
        footerCommentBlockOffer.css('bottom', 0)
    }
    let footer_comment_block = $('.footer-comment-block');
    let banner_block = $('.new-banner-project');
    let sheader_block = $('.sheader');
    let nav_top = $('.navigation-top-app-bar')
    let content_message = $('.project-tab.project-tab-messenger.active')
    let martist_block = $('.martist')
    let martist_master_client = $('.martist.role_master_client')
    let height_banner_block = banner_block.outerHeight();
    let height_sheader_block = sheader_block.outerHeight();
    let height_nav_top = nav_top.outerHeight();
    let height_footer_comment_block = footer_comment_block.outerHeight();
    let height_navigationBar = navigationBar.outerHeight();
    let total_space_top = height_sheader_block + height_banner_block + height_nav_top;
    $('.project-item__content').css('top', `${total_space_top}px`)
    $('.mcolumn--left').css('margin-top', '0')
    $('.mcolumn--right').css('margin-top', '0')
    let topHeaderValue = parseInt(sheader_block.css('top').replace('px', ''));
    if (topHeaderValue < 0) {
        if ($(window).width() < max_width_sp_device) {
            if (navigationBar.hasClass('hide')) {
                footerCommentBlockOffer.css('bottom', '0')
            } else {
                content_message.css('top', `${height_banner_block}px`)
                footerCommentBlockOffer.css('bottom', '0')
            }
        } else {
            content_message.css('top', `${height_banner_block + height_nav_top}px`)
        }
        $('.messenger-detail').css('height', `calc(100vh - ${height_footer_comment_block + height_banner_block}px)`)
    } else {
        if ($(window).width() < max_width_sp_device) {
            if (navigationBar.hasClass('hide')) {
                martist_master_client.css('margin-top', `-${height_sheader_block}px`)
                footerCommentBlockOffer.css('bottom', '0')
            } else {
                // Reset margin-bottom to get height 
                $('.action-panel-head').css('margin-bottom', '0')
                footerCommentBlockOffer.css('bottom', `${footerCommentBlockOffer.outerHeight() + height_navigationBar}px`)
                footerCommentBlockOffer.css('margin-bottom', '8px')
                content_message.css('top', `${height_banner_block + height_sheader_block}px`)
            }
        } else {
            content_message.css('top', `${height_banner_block + height_nav_top + height_sheader_block}px`)
        }
        $('.messenger-detail').css('height', `calc(100vh - ${height_footer_comment_block + height_banner_block + height_sheader_block}px)`)
    }
    $('.mcolumn-back').on('click', function () {
        const getTopHeaderValue = parseInt(sheader_block.css('top').replace('px', ''));
        if (nav_top.is(':hidden')){
            height_nav_top = 0;
        }
        if ($(window).width() < max_width_sp_device) {
            if (getTopHeaderValue < 0) {
                if (martist_block.length > 0 && martist_block.hasClass('hide-banner')) {
                    content_message.css('top', `${height_nav_top + height_banner_block}px`)
                }
            } else {
                if (martist_block.length > 0 && martist_block.hasClass('hide-banner')) {
                    content_message.css('top', `${height_banner_block + height_sheader_block + height_nav_top}px`)
                }
            }
        }
    })
}

function getProgressUploaded(){
    let file_previews = $(".mattach-previews").find(".mattach-template");
    let total = 0;
    let uploaded = 0;
    file_previews.each(function(i, item){
        total += parseInt($(item).attr("data-total"));
        uploaded += parseInt($(item).attr("data-loaded"));
    });
    return Math.max(2, uploaded/total * 70);
}

function createMessage(active_offer) {
    let url_page = '';
    if (messenger_page === 'messenger_artist' || (messenger_page === 'top_page' && $('.pbanner-tab.active[data-show=messenger]').length)) {
        url_page = '/offer_message/create';
    } else if (messenger_page === 'top_page') {
        url_page = '/top/create_comment';
    }
    active_offer.off('click').on("click", ".mcomment-send.active:not(.input-editing)", function (e) {
        e.stopPropagation();
        e.preventDefault();
        let button_send = $(this);
        if (!$(this).hasClass('input-editing')) {
            if (!$(this).hasClass('is-sending')) {
                $(this).addClass('is-sending');
                 $('.mcomment-bottom').addClass('disabled-mcomment')
                sScrollbarBottom();
                let offer_id = $(this).parents('.maction').data('offer');
                let last_message_id = '';
                if ($('.mmessage-list').find('.mmessage:not(.mmessage-confirm)').length) {
                    if (!$('.mmessage-list').find('.mmessage:not(.mmessage-confirm)').last().hasClass('mmessage-system')) {
                        last_message_id = $('.mmessage-list').find('.mmessage:not(.mmessage-confirm, .mmessage-system)').last().data('message-id')
                    }
                }
                let active_offer = $('.offer-' + offer_id);
                let scene_id = '';
                let pin_time = '';
                let scene_title_id = '';
                let parent_id = '';
                let has_pin = false;
                if (!offer_id) {
                    scene_title_id = $(this).parents('.pd-scene-title-detail').attr('data-scene-title-id');
                    active_offer = $('.pd-scene-title-detail[data-scene-title-id=' + scene_title_id + ']');
                    if (active_offer.find('.mcomment-input').hasClass('is-reply')) {
                        parent_id = active_offer.find('.mcomment-input.is-reply').attr('data-parent-id');
                    }

                    if ($(this).parents('.mcommment').find('.mcomment-pin').hasClass('active')) {
                        has_pin = true;
                        scene_id = $(this).parents('.pd-scene-title-detail').find('.cscene__variation.slick-current .cscene__version.slick-current.slick-active').attr('data-scene-id');
                        pin_time = $(this).parents('.mcommment').find('.mcomment-input-title').eq(0).text().replace('pin_drop', '').trim();
                        if (!pin_time.length) {
                            if ($(this).parents('.pd-scene-title-detail').find('.cscene__variation.slick-current .cscene__version.slick-current.slick-active iframe')) {
                                pin_time = '';
                            } else {
                                pin_time = '00:00';
                            }
                        }
                    }
                }
                if ($('.pd-section--detail.pd-product-comment').length > 0) {
                    active_offer = $('.pd-section--detail.pd-product-comment');
                }
                let input_message = $(this).closest('.mcommment');
                let messageContent = active_offer.find('.mcomment-input-text.mcomment-autoExpand').val();
                if (messageContent.trim() === '') {
                    messageContent = '';
                }

                let data = new FormData();
                data.append('offer_id', offer_id);
                data.append('message', messageContent);
                // data.append('before_message_id', last_message_id);
                data.append('scene_id', scene_id);
                data.append('pin_time', pin_time);
                data.append('scene_title_id', scene_title_id);
                data.append('has_pin', has_pin);
                if ($(this).parents('.pd-product-comment').length > 0) {
                    data.append('type', 'project');
                    let project_id = $('.project-item.active').attr('data-project-id');
                    parent_id = active_offer.find('.mcomment-input.is-reply').attr('data-parent-id');
                    data.append('project_id', project_id);
                    data.append('parent_id', parent_id);

                } else {
                    data.append('type', 'scene');
                    data.append('parent_id', parent_id);
                }

                if (scene_id !== '') {
                    let html_folder = '';

                    for (folder in list_folder_name) {
                        if (!folder.includes('/')) {
                            html_folder += `<div class="s-audio s-audio--audio s-audio--black" data-scene-id="${scene_id}">
                                            <div style="display: flex">
                                            <div class="s-audio-control video-pin-time">
                                            <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                                                play_circle
                                            </span>
                                            <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                                            </div>
                                            <div class="s-audio-time video-pin-start">${pin_time}</div>
                                            </div>
                                            <div class="s-audio-text s-audio-file">
                                            <div class="mmessenger mmessenger--file mmessenger--black">
                                            <div class="messenger-content">
                                            <div class="s-file s-file--file s-file--black">
                                            <i class="icon icon icon--sicon-storage"></i>${list_folder_name[folder]}</div>
                                            </div>
                                            </div>
                                            </div>
                                            </div>`
                        }
                    }


                    for (file_name in list_file_name) {
                        html_folder += `<div class="s-audio s-audio--audio s-audio--black" data-scene-id="${scene_id}">
                                        <div style="display: flex">
                                        <div class="s-audio-control video-pin-time">
                                        <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                                            play_circle
                                        </span>
                                        <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                                        </div>
                                        <div class="s-audio-time video-pin-start">${pin_time}</div>
                                        </div>
                                        <div class="s-audio-text s-audio-file">
                                        <div class="mmessenger mmessenger--file mmessenger--black">
                                        <div class="messenger-content">
                                        <div class="s-file s-file--file s-file--black">
                                        <i class="icon icon icon--sicon-clip"></i>${list_file_name[file_name]}</div>
                                        </div>
                                        </div>
                                        </div>
                                        </div>`
                    }

                    let html_content = '';
                    if (messageContent !== '') {
                        html_content = `<div class="mmessenger mmessenger--text mmessenger--black">
                                        <div class="messenger-content">
                                        <div class="s-audio s-audio--audio s-audio--black" data-scene-id="${scene_id}">
                                        <div style="display: flex">
                                        <div class="s-audio-control video-pin-time">
                                        <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                                            play_circle
                                        </span>
                                        <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                                        </div>
                                        <div class="s-audio-time video-pin-start">${pin_time}</div>
                                        </div>
                                        <div class="s-audio-text">${messageContent}</div>
                                        </div>
                                        </div>`
                    }

                    let message_html = `<div class="mmessage mmessage--sent clicked new-message">
                                          <div class="mmessage-main">
                                            <div class="mmessage-content">`
                                            + html_folder + html_content +

                                            `</div>
                                          </div>
                                        </div>`;

                    $(message_html).insertBefore($('.pd-section--detail').find('.mlast__content'));
                }else {
                    if ($('.owner-top').hasClass('scene-detail-page')) {
                    scene_id = $(this).parents('.pd-scene-title-detail').find('.cscene__variation.slick-current .cscene__version.slick-current.slick-active').attr('data-scene-id');
                    data.append('scene_id', scene_id);
                }
                }

                if (mzdrop.files.length > 0 && mzdrop.files[0]) {
                    let file_loaded = Object.values(list_file_id);
                    let file_loading = mzdrop.files.length - file_loaded.length;

                    if (file_loading) {
                        $('.upload-button-wrapper').css('display', 'flex');
                        $('.upload-button-wrapper').addClass('clicked');
                        $('.upload-button-wrapper .fill .process').css('width', '2%');
                        var waiting_file_loading = setInterval(function () {
                            let current_file_loaded = Object.values(list_file_id);
                            let current_file_loading = mzdrop.files.length - current_file_loaded.length;
                            input_message.find('.mcomment-top').show();
                            let progress = getProgressUploaded();
                            $('.upload-button-wrapper .fill .process').css('width', progress + '%');
                            if (!current_file_loading) {
                                data.append('list_file_id', Object.keys(list_file_id));
                                data.append('list_folder_id', Object.values(list_folder_id));
                                clearInterval(waiting_file_loading);
                                $.ajax({
                                    type: "POST",
                                    contentType: false,
                                    processData: false,
                                    cache: false,
                                    data: data,
                                    url: url_page,
                                    beforeSend: function (data) {
                                        // toastr.info('アップロード中…');
                                    },
                                    success: function (data) {

                                        sScrollbarBottom();

                                        $('.upload-button-wrapper .fill .process').css('width', '100%');
                                        if ($('.prdt').length < 1) {
                                            input_message.find('.mcomment-top').hide();
                                        }
                                        setTimeout(function () {
                                            // toastr.success(data.success_message);
                                            $('.upload-button-wrapper').removeClass('clicked').addClass('success')
                                        }, 1000);
                                        setTimeout(function () {
                                            $('.upload-button-wrapper').removeClass('success').css('display', 'none');
                                            $('.upload-button-wrapper .fill .process').css('width', '0');
                                        }, 2000);
                                    },
                                    complete: function () {
                                        mzdrop["sended"] = true;
                                        mzdrop.removeAllFiles();
                                        list_file_id = {};
                                        list_files_folders = {};
                                        list_folder_id = [];
                                        list_folder_name = [];
                                        list_file_name = [];
                                        button_send.removeClass('is-sending');
                                        $('.mcomment-bottom').removeClass('disabled-mcomment')
                                        resetInputMessage(active_offer);
                                        if ($('.scene-style').length > 0) {
                                            calcMoreActionComment($('.scene-style .mmessage'));
                                        } else if ($('.main-talk-room').length > 0) {
                                            calcMoreActionComment($('.main-talk-room .mmessage'));
                                        }
                                        calcPositionDropdownComment();
                                        calcPositionDropdownComment2();
                                        calcMoreActionComment($('.prdt .mmessage'));
                                        hoverDropdownMessage();
                                        hoverBtnActionMessage()
                                        clickBtnActionMessage()
                                    }
                                });
                            }
                        }, 100);
                    } else {
                        data.append('list_file_id', Object.keys(list_file_id));
                        data.append('list_folder_id', Object.values(list_folder_id));
                        $.ajax({
                            type: "POST",
                            contentType: false,
                            processData: false,
                            cache: false,
                            data: data,
                            url: url_page,
                            beforeSend: function (data) {
                                // toastr.info('アップロード中…');
                            },
                            success: function (data) {
                                // toastr.success("完了しました。");

                                sScrollbarBottom();
                            },
                            complete: function () {
                                mzdrop["sended"] = true;
                                mzdrop.removeAllFiles();
                                list_file_id = {};
                                list_files_folders = {};
                                list_folder_id = [];
                                button_send.removeClass('is-sending');
                                resetInputMessage(active_offer);
                                list_folder_name = [];
                                list_file_name = [];
                                $('.mcomment-bottom').removeClass('disabled-mcomment')
                                showLastCommentSceneDetailSP();
                                if ($('.scene-style').length > 0) {
                                    calcMoreActionComment($('.scene-style .mmessage'));
                                } else if ($('.main-talk-room').length > 0) {
                                    calcMoreActionComment($('.main-talk-room .mmessage'));
                                }
                                calcPositionDropdownComment();
                                calcPositionDropdownComment2();
                                calcMoreActionComment($('.prdt .mmessage'));
                                hoverDropdownMessage();
                                hoverBtnActionMessage()
                                clickBtnActionMessage()
                            }
                        });
                    }

                } else if(Object.keys(list_file_id).length) {
                    data.append('list_file_id', Object.keys(list_file_id));
                    data.append('list_folder_id', Object.values(list_folder_id));
                    $.ajax({
                        type: "POST",
                        contentType: false,
                        processData: false,
                        cache: false,
                        data: data,
                        url: url_page,
                        beforeSend: function (data) {
                            // toastr.info('アップロード中…');
                        },
                        success: function (data) {
                            // toastr.success("完了しました。");

                            sScrollbarBottom();
                            $(document).find('.mattach-previews').empty();
                        },
                        complete: function () {
                            mzdrop["sended"] = true;
                            mzdrop.removeAllFiles();
                            list_file_id = {};
                            list_files_folders = {};
                            list_folder_id = [];
                            button_send.removeClass('is-sending');
                            resetInputMessage(active_offer);
                            list_folder_name = [];
                            list_file_name = [];
                            $('.mcomment-bottom').removeClass('disabled-mcomment')
                            if ($('.scene-style').length > 0) {
                                calcMoreActionComment($('.scene-style .mmessage'));
                            } else if ($('.main-talk-room').length > 0) {
                                calcMoreActionComment($('.main-talk-room .mmessage'));
                            }
                             calcPositionDropdownComment();
                            calcPositionDropdownComment2();
                            calcMoreActionComment($('.prdt .mmessage'));
                            hoverDropdownMessage();
                            hoverBtnActionMessage()
                            clickBtnActionMessage()
                        }
                    });
                } else if (messageContent.trim() !== '') {
                    $.ajax({
                        type: "POST",
                        contentType: false,
                        processData: false,
                        cache: false,
                        data: data,
                        url: url_page,
                        success: function (data) {
                            sScrollbarBottom();
                        },
                        complete: function () {
                            button_send.removeClass('is-sending');
                            resetInputMessage(active_offer);
                            list_folder_name = [];
                            list_file_name = [];
                             $('.mcomment-bottom').removeClass('disabled-mcomment');
                            if ($('.scene-style').length > 0) {
                                calcMoreActionComment($('.scene-style .mmessage'));
                            } else if ($('.main-talk-room').length > 0) {
                                calcMoreActionComment($('.main-talk-room .mmessage'));
                            }
                            calcPositionDropdownComment();
                            calcPositionDropdownComment2();
                            calcMoreActionComment($('.prdt .mmessage'));
                            hoverDropdownMessage();
                            hoverBtnActionMessage()
                            clickBtnActionMessage()
                        }
                    });
                } else if (messageContent.trim() === '') {
                    button_send.removeClass('is-sending');
                    resetInputMessage(active_offer);
                    list_folder_name = [];
                    list_file_name = [];
                    $('.mcomment-bottom').removeClass('disabled-mcomment')
                }
            }
            $(document).find('.mcomment-input-text').attr('type_input', 'input');
            list_file_id = {};
            list_folder_id = {};
            valInput='';
            doneTyping('');
            setTimeout(function () {
                active_offer.find('.mcomment-input-placeholder').show();

                if (!$('.mcomment-top').hasClass('comment-top-area')) {
                    active_offer.find('.mcomment-top').hide();
                }
                if(!!$('.btn-tutorial-sp')) {
                    $('.btn-tutorial-sp').css('bottom', `${2 * Math.max(document.documentElement.clientWidth, window.innerWidth || 0)/100}px`)
                }
            }, 100);
            if (messenger_page === 'messenger_artist' || messenger_page === 'top_page') {
                $("html, body").animate({scrollTop: $('.mcommment').height() + 200}, 1000);
            }
            should_scroll = false;
            $('.mcomment-input-text.mcomment-autoExpand').css('height', '');
        }
    });
}

function editMessage(active_offer) {
    let url_page = '';
    active_offer.on('click', '.mcomment-send.active.input-editing', function (e) {
        e.stopPropagation();
        e.preventDefault();
        let button_dom = $(this);
        if (!$(this).hasClass('is-sending')) {
            button_dom.addClass('is-sending');
            let offer_id = $(this).parents('.maction').data('offer');
            let message_id = active_offer.find('.mmessage.editing').attr('data-message-id');
            if (message_id) {
                let message_content = active_offer.find('.mcommment .mcomment-input-text').val();
                if (message_content.trim() === '') {
                    message_content = '';
                }
                let pin_time = '';
                let scene_title_id = '';
                let scene_id = '';
                let has_pin = false;

                if (!offer_id) {
                    scene_title_id = $(this).parents('.pd-scene-title-detail').attr('data-scene-title-id');
                    active_offer = $('.pd-scene-title-detail[data-scene-title-id=' + scene_title_id + ']');

                    if ($(this).parents('.mcommment').find('.mcomment-pin').hasClass('active')) {
                        has_pin = true;
                        scene_id = $(this).parents('.pd-scene-title-detail').find('.cscene__variation.slick-current .cscene__version.slick-current.slick-active').attr('data-scene-id');
                        pin_time = $(this).parents('.mcommment').find('.mcomment-input-title').eq(0).text().replace('pin_drop', '').trim();
                        if (!pin_time.length) {
                            if ($(this).parents('.pd-scene-title-detail').find('.cscene__variation.slick-current .cscene__version.slick-current.slick-active iframe')) {
                                pin_time = '';
                            } else {
                                pin_time = '00:00';
                            }
                        }
                    }
                }

                if ($('.project-tab-product-comment').hasClass('active')) {
                    active_offer = $('.pd-section--detail.pd-product-comment');
                }

                data = new FormData();
                if (messenger_page === 'messenger_artist' || (messenger_page === 'top_page' && $('.pbanner-tab.active[data-show=messenger]').length)) {
                    url_page = '/messenger/update_offer_message';
                    data.append('offer_id', $('.maction').attr('data-offer'));
                } else if (messenger_page === 'top_page') {
                    url_page = '/top/update_scene_comment';

                    if ($('.project-tab-product-comment').hasClass('active')) {
                        data.append('type', 'project');
                        let project_id = $('.project-item.active').attr('data-project-id');
                        data.append('project_id', project_id);
                    } else {
                        data.append('pin_time', pin_time);
                        data.append('scene_title_id', scene_title_id);
                        data.append('scene_id', scene_id);
                        data.append('has_pin', has_pin);
                    }
                }
                data.append('message_id', message_id);
                data.append('message_content', message_content);
                data.append('file', mzdrop.files[0]);
                data.append('list_file_remove', list_file_remove);
                let file_loaded = Object.values(list_file_id);
                let file_loading = mzdrop.files.length - file_loaded.length;
                let number_files = $(this).parents('.mcomment-message').find('.mattach-template').length;
                if (!number_files && !message_content) {
                    $(`.mmessage--sent[data-message-id='${message_id}']`).find('.mmessage-delete').trigger("click");
                     button_dom.removeClass('is-sending');
                } else {
                    if (file_loading) {
                        $('.upload-button-wrapper').css('display', 'flex');
                        $('.upload-button-wrapper').addClass('clicked');
                        $('.upload-button-wrapper .fill .process').css('width', '2%');
                        var waiting_file_loading = setInterval(function () {
                            let current_file_loaded = Object.values(list_file_id);
                            let current_file_loading = mzdrop.files.length - current_file_loaded.length;
                            let progress = getProgressUploaded();
                            $('.upload-button-wrapper .fill .process').css('width', progress + '%');
                            if (!current_file_loading) {
                                data.append('list_file_id', Object.keys(list_file_id));
                                data.append('list_folder_id', Object.values(list_folder_id));
                                clearInterval(waiting_file_loading);
                                $.ajax({
                                    type: "POST",
                                    contentType: false,
                                    processData: false,
                                    cache: false,
                                    url: url_page,
                                    async: false,
                                    data: data,
                                    success: function (data) {
                                        $('.upload-button-wrapper .fill .process').css('width', '100%');
                                        setTimeout(function () {
                                            // toastr.success(data.success_message);
                                            $('.upload-button-wrapper').removeClass('clicked').addClass('success')
                                        }, 1000);
                                        setTimeout(function () {
                                            $('.upload-button-wrapper').removeClass('success').css('display', 'none');
                                            $('.upload-button-wrapper .fill .process').css('width', '0');
                                        }, 2000);
                                    },
                                    complete: function () {
                                        mzdrop["sended"] = true;
                                        mzdrop.removeAllFiles();
                                        list_file_id = {};
                                        list_files_folders = {};
                                        list_folder_id = [];
                                        resetInputMessage(active_offer);
                                        button_dom.removeClass('is-sending');
                                        resetFormContract();
                                        hoverDropdownMessage();
                                        hoverBtnActionMessage()
                                        clickBtnActionMessage()
                                    }
                                });
                                $("html, body").animate({scrollTop: $('.mcommment').height() + 200}, 1000);
                                setTimeout(function () {
                                    active_offer.find('.mcomment-input-placeholder').show();
                                    active_offer.find('.mcomment-top').hide();
                                }, 100)
                            }
                        }, 100);
                    } else {
                        data.append('list_file_id', Object.keys(list_file_id));
                        data.append('list_folder_id', Object.values(list_folder_id));
                        $.ajax({
                            type: "POST",
                            contentType: false,
                            processData: false,
                            cache: false,
                            url: url_page,
                            async: false,
                            data: data,
                            success: function (data) {
                            },
                            complete: function () {
                                mzdrop["sended"] = true;
                                mzdrop.removeAllFiles();
                                list_file_id = {};
                                list_folder_id = [];
                                list_files_folders = {};
                                resetInputMessage(active_offer);
                                button_dom.removeClass('is-sending');
                                resetFormContract();
                                hoverDropdownMessage();
                                hoverBtnActionMessage()
                                clickBtnActionMessage()
                            }
                        });
                        if (messenger_page === 'messenger_artist' || (messenger_page === 'top_page' && $('.pbanner-tab.active[data-show=messenger]').length)) {
                            $("html, body").animate({scrollTop: $('.mcommment').height() + 200}, 1000);
                        }
                        setTimeout(function () {
                            active_offer.find('.mcomment-input-placeholder').show();
                            if (!$('.prdt').length > 0){
                                active_offer.find('.mcomment-top').hide();
                                active_offer.find('.mcomment-attached').css('padding', 0);
                            }
                        }, 100)
                    }
                }
            }
        }
        $('.mcomment-input-text.mcomment-autoExpand').css('height', '');
         hoverDropdownMessage();
        hoverBtnActionMessage()
        clickBtnActionMessage()
        calcMoreActionComment($('.prdt .mmessage'))
        calcPositionDropdownComment2();
    });
}

function resetInputMessage(active_offer) {
    list_file_remove = [];
    mzdrop["sended"] = true;
    mzdrop.removeAllFiles();
    list_file_id = {};
    list_folder_name = [];
    list_file_name = [];
    active_offer.find('.mcommment .mcomment-input-text').val('');
    // active_offer.find('.mcommment .mcomment-input-text').height('20px');
    $('.maction .mcommment-file').remove();
    active_offer.find('.mcomment-send.active').removeClass('active');
    active_offer.find('.mmessage').removeClass('editing');
    active_offer.find('.mmessage').removeClass('reply');
    active_offer.find('.mcomment-send').removeClass('input-editing');
    active_offer.find('.mcommment').removeClass('border-editing');
    active_offer.find('.btn-remove-msg').removeClass('d-block')
    active_offer.find('.block-remove-msg-editing').addClass('d-none')
    if ($(document).width() > maxWidthIpadDevice) {
        $('.prdt .mmessage-list').removeClass('pd-main-message')
    }
    active_offer.find('.mmessage-reply.active').removeClass('active');
    active_offer.find('.mcomment-pin').removeClass('active hide');
    active_offer.find('.mcomment-input').removeClass('is-reply').removeClass('is-pin');
    $('.cscene-vertical').removeClass('active');
     if ($('.prdt').length > 0){
         let commentInputPrdt = active_offer.find('.mcommment .mcomment-input-text');
         commentInputPrdt.height('30px')
         commentInputPrdt.css('overflow', 'hidden')

     }
}

function searchOffer() {
    $(document).on('keydown', '#pm-search', function (e) {
        if ((e.key === 'Enter' || e.keyCode === 13) && $('.project-tab-messenger.active').length) {
            let keyword = $(this).val();
            if (keyword !== '') {
                let project_id = $('.project-item.active').attr('data-project-id');
                $.ajax({
                    type: "GET",
                    url: '/ajax/search_offer_creator',
                    data: {
                        'keyword': keyword,
                        'project_id': project_id
                    },
                    beforeSend: function() {
                        $('.list--offers-search').empty();
                    },
                    beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                        removeOfferActive();
                        $('.list--offers-project').addClass('hide');
                        $('.list--offers-search').empty();
                        $('.list--offers-search').append($(`<div class="count--list-offers-search">検索結果（${response.result_offer}件） </div>`));
                        $('.list--offers-search').append(response.list_offers_search_html);
                        activeOffer($(".mlist .list--offers-search"));
                    },
                    error: function (response) {
                        toastr.warning("エラーが発生しました");
                    },
                    complete: function() {
                        setWidthInput($('.mcommment[id="comment-input-1"]'));
                    }
                });
            } else {
                removeSearchOffer();
                setWidthInput($('.mcommment[id="comment-input-1"]'));
            }
        }
    });
}

function removeSearchOffer() {
    $('.list--offers-project').removeClass('hide');
    $('.list--offers-search').empty();
    activeOffer($(".mlist .list--offers-project"));
}

function removeOfferActive() {
    $('.messenger-detail .mcolumn-back').trigger('click');
    $('.mcolumn.mcolumn--main').empty();
    $('.mcolumn.mcolumn--right').empty();
    $('.mscene.mitem').removeClass('mactive');
}

function activeOffer(listOffer) {
    if (is_pc === 'True' && listOffer.find('.list-offer').children('.mitem').length > 0) {
        listOffer.find('.list-offer').children('.mitem').eq(0).trigger('click');
    } else {
        removeOfferActive();
    }
}

function showInforMessage() {
    $(document).on('mouseenter mouseleave', '.mmessage', function (e) {
        let parent = $(this).parents('.mmessage-list');
        if (!parent.hasClass('view_only')) {
            if ($(this).parents('.martist').length && !$('.scene-title__action .button-edit_offer').length) {
                $('.button-edit_offer.message-first__message').remove();
            }
            removeButtonDelete();
            let message_active = $(this);

            if ($(this).hasClass('mmessage--sent') || !$('input[name="switch-checkbox-comment"]').is(':checked')) {
                message_active.find('.mmessage-status').toggleClass('clicked', e.type === 'mouseenter');
                if (message_active.height() <= 75) {
                    if(!!$('.message-actions-container').children().length && !!message_active.find('.message-actions-container .mmessage-action').children().length){
                        message_active.find('.mmessage-status').toggleClass('hide', e.type === 'mouseenter');
                    }
                } else {
                    message_active.css('cursor', 'default');
                    message_active.find('.mmessage-info').css({height: message_active.find('.mmessage-main').height()})
                }
                message_active.find('.mmessage-action').toggleClass('show-action-hover', e.type === 'mouseenter');
                if (e.type === 'mouseleave') {
                    message_active.find('.mmessage-action').addClass('hide');
                    message_active.find('.mmessage-status').removeClass('hide');
                }
            }
        }
    });
}

function checkMessengerOwner() {
    return $('.pbanner-tab-message.active').length && user_role !== 'admin'
}

function removeButtonDelete() {
    if (file_contract) {
        let messageContractDom = $('.mmessenger[data-file-id=' + file_contract + ']').parents('.mmessage');
        messageContractDom.find('.mmessage-delete').remove()
    }
    if (file_bill) {
        let messageBillDom = $('.mmessenger[data-file-id=' + file_bill + ']').parents('.mmessage');
        messageBillDom.find('.mmessage-delete').remove()
    }
}

function appHeight() {
    if(!is_pc) {
        document.style.overflow = 'hidden';
        $('body')[0].style.overflow = 'hidden';
        $('main')[0].style.overflow = 'hidden';
        $('.mcontainer')[0].style.overflow = 'hidden';
    }
    const doc = document.documentElement;
    doc.style.setProperty('--app-height', `${window.innerHeight}px`)
    doc.style.height = 'auto';
}

function removeDuplicatedDate() {
    let last_date;
    let date;
    let list_item = [];
    let count = 0;
     $('.tfile-item-time').removeClass('hide');
    if ($('.pd-scene-title-detail').length) {
        $('.tfile-item-time').each(function (i, e) {
            if ($(this).hasClass('item-scene')) {
                count = $('.item-scene[data-time="' + e.innerHTML + '"]').length
            }
            if (count > 0) {
                if (last_date === e.innerHTML) {
                    list_item.push($(this));
                }
                if (list_item.length >= count) {
                    let sort_by_variation = function (a, b) {
                        let a_value = a.parentElement.getAttribute('data-variation-index');
                        let b_value = b.parentElement.getAttribute('data-variation-index');
                        if (a_value === b_value) {
                            return -1
                        } else {
                            return a_value.localeCompare(b_value);
                        }
                    };
                    let list = $('.item-scene[data-time="' + e.innerHTML + '"]').get();
                    list.sort(sort_by_variation);

                    for (let i = 1; i <= list.length - 1; i++) {
                        console.log(i + " " + list[i].parentElement.getAttribute('data-variation-index'))
                        $(list[i].parentNode).insertBefore($(list[i - 1].parentNode));
                    }
                    list_item = [];
                } else if (list_item.length === 0) {
                    list_item.push(e)
                }
                last_date = e.innerHTML;
            }
        })
    }

    $('.tfile-item-time').each(function (i, e) {
        if (date === e.innerHTML) {
            $(e).addClass('hide');
        }
        date = e.innerHTML;
    })
    $('.tfile-item-offer-white').find('.s-file').removeClass('s-file--gray');
    $('.tfile-item-offer-gray').find('.s-file').addClass('s-file--gray');
}

function traverseFileTree(item, name, flag, folder_id, page) {
    if (item.isFile && name) {
        if (list_files_folders[name]) {
            list_files_folders[name].push(item.name);
        } else {
            list_files_folders[name] = [item.name]
        }
    } else if (item.isDirectory) {
        let current_path = name;
        if (item.name.trim().length > 128){
            is_exceeded_length = true;
        }
        if (flag) {
            current_path +=  item.name + '/';
        }
        var dirReader = item.createReader();
        let offer_id;
        if ($('.mitem.mactive').length) {
            offer_id = $('.mitem.mactive').attr('data-offer')
        }
        $.ajax({
            type: "POST",
            data: {
                'parent_id': folder_id,
                'name': item.name,
                'full_path': 'storage/' + current_path,
                'message_type': page,
                'offer_id': offer_id
            },
            async: false,
            url: '/upload/create_folder',
            success: function (data) {
                console.log('folder created')
                let folder_pk = data.id;
                list_temp_folder_id[current_path] = folder_pk;
                dirReader.readEntries(function (entries) {
                    for (let i = 0; i < entries.length; i++) {
                        traverseFileTree(entries[i], current_path, 1, folder_pk, page);
                    }
                });
            },
        });
    }
}

function uploadFileS3(file, file_dom, path){
    file_dom.find('.determinate').css('width', '0%');
    let page = getPage(file_dom);
    $.ajax({
        type: "GET",
        datatype: "json",
        url: "/get_presigned_url",
        data: {
            'file_name': "storage" + path + "/" + file.name,
            'file_type': file.type,
        },
        success: function (data) {
            let url = data.presigned_post.url;
            let key_file = data.presigned_post.fields["key"];
            var xhr = new XMLHttpRequest();
            xhr.open("POST", url);
            let postData = new FormData();
            for(key in data.presigned_post.fields){
                postData.append(key, data.presigned_post.fields[key]);
            }
            postData.append('file', file);
            xhr.upload.addEventListener("progress", function (evt) {
                if (evt.lengthComputable) {
                    let percentComplete = (evt.loaded / evt.total) * 70 + '%';
                    file_dom.find('.determinate').css('transition', '0');
                    file_dom.find('.determinate').css('transition', '1s')
                    file_dom.find('.determinate').css('width', percentComplete);
                    if(file_dom.length){
                        if(!file_dom.attr("data-total")){
                            file_dom.attr("data-total", evt.total);
                        }
                        file_dom.attr("data-loaded", evt.loaded);
                    }
                }
            }, false);
            xhr.onreadystatechange = function() {
                if(xhr.readyState === 4){
                    if(xhr.status === 200 || xhr.status === 204){
                        let data = new FormData();
                        let folder_id = "";
                        if(path){
                            folder_id = list_folder_id[path];
                            if(path.includes("/")){
                                path = path.slice(0, path.indexOf("---")) + path.slice(path.indexOf("/"));
                            }else{
                                 path = path.slice(0, path.indexOf("---"));
                            }
                            path += "/";
                        }
                        data.append('file', file);
                        data.append('message_type', page);
                        data.append('key_file', key_file);
                        data.append('file_name', file.name);
                        data.append('folder_id', folder_id);
                        if ($('.mitem.mactive').length) {
                            data.append('offer_id', $('.mitem.mactive').attr('data-offer'));
                        }
                        $.ajax({
                            type: "POST",
                            data: data,
                            contentType: false,
                            processData: false,
                            url: '/upload/create_file',
                            success: function (data) {
                                Object.assign(list_file_id, data);
                                file_dom.find('.determinate').css('width', '100%');
                                file_dom.find('.determinate').attr('data');
                                $('.mcomment-input-text').attr('type_input', 'input');
                                setTimeout(async () => {
                                    if($(document).find('textarea.mcomment-input-text').length){
                                        await doneTyping($(document).find('textarea.mcomment-input-text').val());
                                    }
                                }, 2000);
                                if (data.status === 'error') {
                                    toastr.error('ファイルをアップロード失敗しました。');
                                }
                            },
                            error: function (e) {
                                toastr.error('ファイルをアップロード失敗しました。');
                            }
                        });
                    }
                    else{
                        alert("Could not upload file.");
                    }
                }
            };
            xhr.send(postData);
        }
    })
}

function getPage(target) {
    let page = ''
    if (target.parents('.project-tab-product-comment').length) {
        page = 'product_comment'
    } else if (target.parents('.pd-scene-title-detail').length) {
        page = 'scene_comment'
    } else if (user_role !== 'admin') {
        page = 'message_owner'
    } else if (target.parents('.martist').length) {
        page = 'message'
    }
    return page
}

function projectFolder() {
    $(document).on('click', ".hasSub", function () {
        $(this).parent().toggleClass("subactivated");
        $(this).parent().children("ul:first").toggle();
        let sub_item = $(this);
        if ($(this).find("i").hasClass("glyphicon-folder-open")) {
            $(this).find("i").removeClass("glyphicon-folder-open").addClass("glyphicon-folder-close");
        } else {
            $(this).find("i").removeClass("glyphicon-folder-close").addClass("glyphicon-folder-open");
        }
    });

    $(".menufilter").keyup(function () {
        var searchTerm = $(".menufilter").val();
        var listItem = $(".foldertreeview").children("li");
        var searchSplit = searchTerm.replace(/ /g, "'):containsi('");

        $.extend($.expr[":"], {
            containsi: function (elem, i, match, array) {
                return (elem.textContent || elem.innerText || "").toLowerCase().indexOf((match[3] || "").toLowerCase()) >= 0;
            },
        });

        $(".foldertreeview li")
            .not(":containsi('" + searchSplit + "')")
            .each(function (e) {
                $(this).hide();
            });

        $(".foldertreeview li:containsi('" + searchSplit + "')").each(function (e) {
            $(this).show();
        });
    });

    $(document).on('click', '.sfolder, .messager-folder', function (e) {
        $('#folderModal').modal('show');
        let type_message = '';
        let message_id;
        if ($(this).parents('.pd-product-comment').length > 0) {
            type_message = 'project'
        } else if ($(this).parents('.pd-scene-title-detail').length > 0) {
            type_message = 'scene'
        } else if (user_role !== 'admin') {
            type_message = 'message_owner'
        } else if ($(this).parents('.martist').length > 0) {
            type_message = 'message_artist'
        }

        $('#folderModal').attr('data-type-message', type_message);
        if ($(this).hasClass('messager-folder')) {
            folder_id = $(this).attr('data-file-id');
        } else {
            folder_id = $(this).attr('data-folder-id');
        }
        let offer_id;
        if ($('.mitem.mactive').length) {
            offer_id = $('.mitem.mactive').attr('data-offer')
        }
        $.ajax({
            type: "GET",
            url: "/top/get_item_in_message",
            data: {
                "folder_id": folder_id,
                'type_message': type_message,
                'offer_id': offer_id
            },
            success: function (data) {
                $('#folderModal').find('.group-tree-modal').empty();
                $('#folderModal').find('.group-tree-modal').append(data.html);
            }
        })
    });

    $('#folderModal').on('hidden.bs.modal', function () {
        $('.menufilter.form-control').val('');
    })

}

function filterOffer() {

    $(document).on('change', '#offer-filter', function () {
        let filter_offer = 'waiting';
        // if ($(this).is(':checked') && $(this).parent().find('.text-right').hasClass('navbar-active')) {
        if ($('.switch-dm-txt.text-right').hasClass('navbar-active')) {
            filter_offer = 'processing';
        }
        $('.search-delete').trigger('click');
        let project_id = $('.project-item.active').attr('data-project-id');
        get_messenger_artist(project_id, null, filter_offer);
    });

    $(document).on('click', '.messenger-add', function () {
        $('.navigation-top-app-bar').addClass('hide-top-bar');
        let project_id = $('.project-item.active').attr('data-project-id');
        $('.search-delete').trigger('click');
        get_messenger_artist(project_id, null, 'search');
        closeNav();
    });
    $(document).on('click', '.go__to-messenger', function () {
        let project_id = $('.project-item.active').attr('data-project-id');
        get_messenger_artist(project_id, null, 'waiting');
    });
}

function actionMessengerArtist(infor_offer, active_offer) {
    // check back
    infor_offer.off('click').on('click', '.accept_production_file', function () {
        let button_dom = $(this);
        let offer_id = button_dom.parents('.mcolumn-content').attr('data-offer');
        bootbox.confirm({
            message: " 検収しますか？",
            buttons: {
                confirm: {
                    label: 'はい',
                    className: 'btn btn-success btn--primary'
                },
                cancel: {
                    label: 'いいえ',
                    className: 'btn btn-danger btn--tertiary'
                }
            },
            callback: function (result) {
                if (result) {
                    $.ajax({
                        type: "POST",
                        data: {
                            'offer_id': offer_id,
                            'status': 4
                        },
                        url: '/messenger/update_status_offer',
                        success: function (data) {
                            console.log('ok')
                        }
                    });
                }
            }
        });
    });

    // // creator accept_offer
    // infor_offer.find(".mmessenger-director__item-action-accept").on("click", function () {
    //     if (!$(this).children().is('.disabled')) {
    //         let offer_id = $(this).parents('.mcolumn-content').attr('data-offer');
    //         ajaxAcceptOffer(offer_id, active_offer);
    //     }
    // });

    // active_offer.find(".mmessenger-director__item-action-accept").on("click", function () {
    //     let offer_id = $(this).parents('.mmessage-confirm').attr('data-offer');
    //     ajaxAcceptOffer(offer_id, active_offer);
    // });

    $('#minfo-accept-policy, #minfo-accept-policy2').on('click', function () {
        if ($(this).is(':checked')) {
            $('#minfo-accept-policy, #minfo-accept-policy2').prop('checked', true);
            $('.mmessenger-director__item-action-accept').removeClass('disabled');
        } else {
            $('#minfo-accept-policy, #minfo-accept-policy2').prop('checked', false);
            $('.mmessenger-director__item-action-accept').addClass('disabled');
        }
    });

    // review
    infor_offer.on('click', '.btn-icon-unlike, .btn-icon-like, .btn-icon-normal', function () {
        let offer_id = $(this).parents('.mcolumn-content').attr('data-offer');
        let value = $(this).data('value');
        $.ajax({
            type: "POST",
            data: {
                'offer_id': offer_id,
                'value': value
            },
            url: '/messenger/update_review_offer',
            success: function (data) {
                // toastr.success('評価しました。');
                infor_offer.find('.minfo-contract').html(data.infor_html);
                infor_offer.find('.minfo-rating').remove();
            }
        });
    });

    // see contract
    infor_offer.on('click', '.see-contract', function () {
        let offer_id = $(this).parents('.mcolumn-content').attr('data-offer');
        get_content_modal_contract(offer_id)
    });

    active_offer.on('click', '.see-contract', function () {
        let offer_id = $(this).parents('.mmessage-confirm').attr('data-offer');
        get_content_modal_contract(offer_id)
    });

    editFormOffer();
}

function addBorderPreview(isPin = false, sceneCurrent = null) {
    let widthOnSP = 767;
    let slider = $('.cscene-horizontal.slick-initialized.slick-slider');
    let mainEl = $('main.owner-top');
    let slickActive = $('.cscene__version.slick-slide.slick-current.slick-active');
    let commentBottom = $('.mcomment-bottom')
    if ($(window).width() > widthOnSP) {
        let preview = slickActive.children().children();
        let previewActive = slickActive.children();
        let spaceHeightPreview, maxWidthViewPort;
        if (isPin) {
            if (sceneCurrent) {
                slickActive = sceneCurrent;
            }
            mainEl.addClass('pin-preview')
            spaceHeightPreview = slider.height() - paddingHeightActivePreview;
            maxWidthViewPort = slider.width() - paddingWidthActivePreview;
            commentBottom.addClass('padding-pin')
            pinActivePreview(preview, previewActive, spaceHeightPreview, maxWidthViewPort, slickActive);
        } else {
            mainEl.removeClass('pin-preview')
            spaceHeightPreview = slider.outerHeight();
            maxWidthViewPort = slider.outerWidth();
            commentBottom.removeClass('padding-pin')
            removePinActivePreview(preview, previewActive, spaceHeightPreview, maxWidthViewPort);
        }
    } else {
        let slickActive = $('.cscene__version.slick-slide.slick-current.slick-active');
        let preview = slickActive.children().children();
        let previewActive = slickActive.children();
        let spaceHeightPreview, maxWidthViewPort;
        if (isPin) {
            mainEl.addClass('pin-preview')
            spaceHeightPreview = slider.height() - paddingHeightActivePreview;
            maxWidthViewPort = slider.width() - paddingWidthActivePreview;
            commentBottom.addClass('padding-pin')
            pinActivePreviewSP(preview, previewActive, spaceHeightPreview, maxWidthViewPort, slickActive);
        } else {
            mainEl.removeClass('pin-preview')
            spaceHeightPreview = slider.outerHeight();
            maxWidthViewPort = slider.outerWidth();
            commentBottom.removeClass('padding-pin')
            removePinActivePreviewSP(preview, previewActive, spaceHeightPreview, maxWidthViewPort);
        }
    }
}

function removePinActivePreview(preview, previewActive, spaceHeightPreview, maxWidthViewPort) {
    let slickSlider = $('.cscene-horizontal.slick-initialized.slick-slider')
    let dataWidthPreview, dataHeightPreview, result, heightPreview, widthPreview;
    previewActive.removeClass('space-top')
    if (previewActive.hasClass('scene-type-video')) {
        dataWidthPreview = previewActive.children().attr('data-width');
        dataHeightPreview = previewActive.children().attr('data-height');
        result = checkSizePreview(0, spaceHeightPreview, dataWidthPreview, dataHeightPreview, maxWidthViewPort);
                heightPreview = result.heightPreview;
                widthPreview = result.widthPreview;
                preview.css({
                    'width': `${widthPreview}px`,
                    'height': `${heightPreview}px`,
                })
                previewActive.css({
                    'width': `${widthPreview}px`,
                    'height': `${heightPreview}px`,
                })
                previewActive.removeClass('active-scene')
    } else {
        dataWidthPreview = maxWidthViewPort;
        dataHeightPreview = spaceHeightPreview;
        result = checkSizePreview(0, spaceHeightPreview, dataWidthPreview, dataHeightPreview, maxWidthViewPort);
        widthPreview = result.widthPreview;
        preview.css({
            'width': `${widthPreview}px`,
        })
        previewActive.css({
            'width': `${widthPreview}px`,
            'transform': 'translateY(-50%)'
        })
        previewActive.parent().css({
            'width': `${widthPreview}px`,
        })
        slickSlider.removeClass('active-scene')
    }
}

function pinActivePreview(preview, previewActive, spaceHeightPreview, maxWidthViewPort, slickActive) {
    let dataWidthPreview, dataHeightPreview, result, heightPreview, widthPreview;
    let slickSlider = $('.cscene-horizontal.slick-initialized.slick-slider');
    slickActive.addClass('clear-border');
    if (previewActive.hasClass('scene-type-video')) {
        dataWidthPreview = previewActive.children().attr('data-width');
        dataHeightPreview = previewActive.children().attr('data-height');
        result = checkSizePreview(0, spaceHeightPreview, dataWidthPreview, dataHeightPreview, maxWidthViewPort);
        heightPreview = result.heightPreview;
        widthPreview = result.widthPreview;
        preview.css({
            'width': `${widthPreview}px`,
            'height': `${heightPreview}px`,
        })
        previewActive.css({
            'width': `${widthPreview + paddingWidthActivePreview}px`,
            'height': `${heightPreview + paddingHeightActivePreview}px`,
        })
        previewActive.addClass('active-scene')
        slickSlider.removeClass('active-scene')
    }else {
        dataWidthPreview = maxWidthViewPort;
        dataHeightPreview = spaceHeightPreview;
        result = checkSizePreview(0, spaceHeightPreview, dataWidthPreview, dataHeightPreview, maxWidthViewPort);
        widthPreview = result.widthPreview;
        slickSlider.addClass('active-scene')
        preview.css({
            'width': `${widthPreview}px`,
        })
        previewActive.css({
            'width': `${widthPreview}px`,
            'transform': 'translateY(-51%)'
        })
        previewActive.parent().css({
            'width': `${widthPreview}px`,
        })
        previewActive.removeClass('active-scene')
        previewActive.addClass('space-top')
    }
}

function pinActivePreviewSP(preview, previewActive, spaceHeightPreview, maxWidthViewPort, slickActive) {
    let dataWidthPreview, dataHeightPreview, result, heightPreview, widthPreview;
    let slickList = previewActive.parent().parent().parent();
    if (previewActive.hasClass('scene-type-video')) {
        dataWidthPreview = previewActive.children().attr('data-width');
        dataHeightPreview = previewActive.children().attr('data-height');
        result = checkSizePreview(0, spaceHeightPreview, dataWidthPreview, dataHeightPreview, maxWidthViewPort);
        heightPreview = result.heightPreview;
        widthPreview = result.widthPreview;
        preview.css({
            'width': `${widthPreview}px`,
            'height': `${heightPreview}px`,
        })
        previewActive.css({
            'width': `${widthPreview + paddingWidthActivePreview}px`,
            'height': `${heightPreview + paddingHeightActivePreview}px`,
        })
        previewActive.addClass('active-scene')
    }else {
        dataWidthPreview = maxWidthViewPort;
        dataHeightPreview = spaceHeightPreview;
        result = checkSizePreview(0, spaceHeightPreview, dataWidthPreview, dataHeightPreview, maxWidthViewPort);
        widthPreview = result.widthPreview;
        slickList.addClass('active-scene')
        preview.css({
            'width': `${widthPreview}px`,
        })
        previewActive.css({
            'width': `${widthPreview}px`,
        })
        previewActive.parent().css({
            'width': `${widthPreview}px`,
        })
    }
}


function removePinActivePreviewSP(preview, previewActive, spaceHeightPreview, maxWidthViewPort) {
    let slickList = previewActive.parent().parent().parent();
    let dataWidthPreview, dataHeightPreview, result, heightPreview, widthPreview;
    if (previewActive.hasClass('scene-type-video')) {
        dataWidthPreview = previewActive.children().attr('data-width');
        dataHeightPreview = previewActive.children().attr('data-height');
        result = checkSizePreview(0, spaceHeightPreview, dataWidthPreview, dataHeightPreview, maxWidthViewPort);
                heightPreview = result.heightPreview;
                widthPreview = result.widthPreview;
                preview.css({
                    'width': `${widthPreview}px`,
                    'height': `${heightPreview}px`,
                })
                previewActive.css({
                    'width': `${widthPreview}px`,
                    'height': `${heightPreview}px`,
                })
                previewActive.removeClass('active-scene')
    } else {
        dataWidthPreview = maxWidthViewPort;
        dataHeightPreview = spaceHeightPreview;
        result = checkSizePreview(0, spaceHeightPreview, dataWidthPreview, dataHeightPreview, maxWidthViewPort);
        widthPreview = result.widthPreview;
        preview.css({
            'width': `${widthPreview}px`,
        })
        previewActive.css({
            'width': `${widthPreview}px`,
        })
        previewActive.parent().css({
            'width': `${widthPreview}px`,
        })
        slickList.removeClass('active-scene')
    }
}

function showLastCommentSceneDetailSP() {
    const widthViewPortSP = 767;
    if ($(document).width() <= widthViewPortSP) {
        const listComment = $('.message-list-new .mmessage.clicked')
        listComment.removeClass('load-lasted-message');
        listComment.last().addClass('load-lasted-message')
    }
}

function getDownloadFileScene() {
    $(document).on('click', '.block-download-file .scene-file-download', function (e) {
        let data = new FormData();
        e.stopPropagation();
        let file_id;
        if ($(this).parents('.tfile-producttion-file').length > 0) {
            data.append('production_file', 'production_file');
            file_id = $(this).parents('.tfile-producttion-file').attr('data-scene-title-id')
        } else {
            file_id = $(this).parents('.block-download-file').attr('data-file-id');
        }
        data.append('file_id', file_id);
        downloadFile(data, this)
    })
}

function calcPositionDropdownComment() {
    let listMessageSent = $('.scene-style .mmessage--sent');
    let listMessageReceived = $('.scene-style .mmessage--received');
    const widthDropdownCmt = 200;
    listMessageSent.each(function (el) {
        let widthMessageMain = $(this).find('.mmessage-main').outerWidth();
        let widthMessageInfo = $(this).find('.mmessage-info').outerWidth();
        let resultLeftDropdown = widthDropdownCmt - (widthMessageMain + widthMessageInfo);
        if (resultLeftDropdown > 0) {
            let dropdownMenu = $(this).find('.mmessage-info .dropdown-comment-new.dropdown-comment .dropdown-menu.dropdown-menu-comment')
            dropdownMenu.css('left', `-${resultLeftDropdown + 10}px`)
        }
    })
    const messageListWidth = $('.pd-comment').width();
    listMessageReceived.each(function (el) {
        let widthMessageMain = $(this).find('.mmessage-main').outerWidth();
        let widthMessageInfo = $(this).find('.mmessage-info').outerWidth();
        let resultLeftDropdown = widthDropdownCmt - (widthMessageMain + widthMessageInfo);
        if (resultLeftDropdown > 0) {
            let dropdownMenu = $(this).find('.mmessage-info .dropdown-comment-new.dropdown-comment .dropdown-menu.dropdown-menu-comment')
            dropdownMenu.css('left', `-${resultLeftDropdown + 10}px`)
        } else {
            let resultLongReceivedCmt = messageListWidth - (widthMessageMain + widthMessageInfo);
            if (resultLongReceivedCmt < widthDropdownCmt) {
                let dropdownMenuReceived = $(this).find('.mmessage-info .dropdown.dropdown-comment-new.dropdown-comment-received .dropdown-menu.dropdown-menu-comment');
                dropdownMenuReceived.css('left', `-${widthDropdownCmt - resultLongReceivedCmt}px`)
            }
        }
    })
}

function calcMoreActionComment(el) {
    if (el) {
        let listMessage = el;
        const heightShowMoreAction = 10;
        listMessage.each(function () {
            let heightMessageMain = $(this).find('.mmessage-main').outerHeight();
            let messageInfo = $(this).find('.mmessage-info');
            let dropdownCommentNew = $(this).find('.dropdown-comment-new');
            messageInfo.css('height', heightMessageMain)
            let messageInfoContainer = messageInfo.find('.message-info-container');
            if ((heightMessageMain / 2) - heightShowMoreAction <= messageInfoContainer.height()) {
                messageInfoContainer.css('height', 'auto')
                dropdownCommentNew.css('height', `${heightMessageMain - messageInfoContainer.height()}`)
                if (heightMessageMain - messageInfoContainer.height() >= (heightMessageMain / 2) + (heightShowMoreAction / 2)) {
                    dropdownCommentNew.css('height', 'calc(50% + 5px)')
                    messageInfoContainer.css('height', 'calc(50% - 5px)')
                }
            } else {
                dropdownCommentNew.css('height', 'calc(50% + 5px)')
                messageInfoContainer.css('height', 'calc(50% - 5px)')
            }
        })
    }
}

function calcPositionDropdownComment2() {
    let listMessageSent = $('.prdt .mmessage--sent');
    let listMessageReceived = $('.prdt .mmessage--received');
    const widthDropdownCmt = 180;
    const paddingDropdown = 34;
    const spaceLeftScreen = 50;
    listMessageSent.each(function (el) {
        let widthMessageMain = $(this).find('.mmessage-main').outerWidth();
        let widthMessageInfo = $(this).find('.mmessage-info').outerWidth();
        let resultLeftDropdown = widthDropdownCmt - (widthMessageMain + widthMessageInfo);
        let numberLeftDropdown = 0;
        if (resultLeftDropdown > 0) {
            numberLeftDropdown = -resultLeftDropdown - paddingDropdown;
        }
        let dropdownMenu = $(this).find('.mmessage-info .dropdown-comment-new.dropdown-comment .dropdown-menu.dropdown-menu-comment')
        dropdownMenu.css('left', `${numberLeftDropdown}px`)
    })
    const messageListWidth = $('.pd-comment').width();
    listMessageReceived.each(function (el) {
        let widthMessageMain = $(this).find('.mmessage-main').outerWidth();
        let widthMessageInfo = $(this).find('.mmessage-info').outerWidth();
        let resultLeftDropdown = widthDropdownCmt - (widthMessageMain + widthMessageInfo);
        if (resultLeftDropdown > 0) {
            let dropdownMenu = $(this).find('.mmessage-info .dropdown-comment-new.dropdown-comment .dropdown-menu.dropdown-menu-comment')
            let left_dropdown = resultLeftDropdown - paddingDropdown;
            if (left_dropdown > spaceLeftScreen) {
                left_dropdown = spaceLeftScreen;
            }
            dropdownMenu.css('left', `-${left_dropdown}px`)
        } else {
            let resultLongReceivedCmt = messageListWidth - (widthMessageMain + widthMessageInfo);
            if (resultLongReceivedCmt < widthDropdownCmt) {
                let dropdownMenuReceived = $(this).find('.mmessage-info .dropdown.dropdown-comment-new.dropdown-comment-received .dropdown-menu.dropdown-menu-comment');
                dropdownMenuReceived.css('left', `-${widthDropdownCmt - resultLongReceivedCmt}px`)
            }
        }
    })
}

$(window).scroll(function () {
    if ($(window).scrollTop() + $(window).height() === $(document).height()) {
        // styleBtnPlanApprove(true)
        $('.prdt .dm-block-message .mcommment').css('bottom', '32px')
    } else {
        // styleBtnPlanApprove()
        $('.prdt .dm-block-message .mcommment').css('bottom', 0)
    }
});

function scrollCommentBar() {
    let lastScrollTop = 0;
    let footerComment = $('.footer-comment-block');
    let btnAddOffer = $('.action-add-offer')
    let navigationBar = $('.block-navigation-bar');
    let newBannerProject = $('.new-banner-project');
    let sheader = $('.sheader');
    let navigationTopAppBar = $('.navigation-top-app-bar');
    let heightNavigationBar = 0;
    if (navigationBar.length > 0 && !navigationBar.hasClass('hide')) {
        heightNavigationBar = navigationBar.outerHeight();
    }
    let heightNewBannerProject = newBannerProject.outerHeight();
    let heightNavigationTopAppBar = navigationTopAppBar.outerHeight();
    let heightSheader = sheader.outerHeight();
    let totalHeightBannerTopNav = heightNewBannerProject + heightNavigationTopAppBar;
    let totalHeightSpaceTop = heightNewBannerProject + heightNavigationTopAppBar + heightSheader;
    const space_margin_top = '40px';
    navigationBar.css('transition', 'bottom 0.2s')
    footerComment.css('transition', 'bottom 0.2s')
    btnAddOffer.css('transition', 'bottom 0.2s')
    $(window).scroll(function (event) {
        let messengerContent = $('.prdt .messenger-detail')
        let st = $(this).scrollTop();
        if (st > lastScrollTop) {
                console.log('Scroll xuống scrollCommentBar');
                messengerContent.css('height', `calc(100vh - ${totalHeightBannerTopNav}px)`);
                if (navigationBar.length > 0 && !navigationBar.hasClass('hide')) {
                    footerComment.css('bottom', '0');
                    btnAddOffer.css('bottom', '0');
                    $('#mColumnWrap .mcolumn-content').css('height', '82vh')
                }
                navigationBar.css('bottom', `-${heightNavigationBar}px`)
                $('.column-list-offer').css('margin-top', space_margin_top)
                $('.mcolumn--right').css('margin-top', space_margin_top)
        } else {
                console.log('Scroll lên scrollCommentBar');
                if (navigationBar.length > 0 && !navigationBar.hasClass('hide')) {
                    footerComment.css('bottom', `${heightNavigationBar}px`);
                    btnAddOffer.css('bottom', `${heightNavigationBar}px`);
                    // $('#mColumnWrap .mcolumn-content').css('height', '65vh')
                }
                navigationBar.css('bottom', '0')
                messengerContent.css('height', `calc(100vh - ${totalHeightSpaceTop}px)`);
                $('.column-list-offer').css('margin-top', '0')
                $('.mcolumn--right').css('margin-top', '0')
        }
        lastScrollTop = st;
    });
}

/* code fixed button preview plan approve

    function styleBtnPlanApprove(isInputCommentUp = false) {
        const windowWidth = $(window).width();
        const planApproveBtn = $('.open-preview-plan-approve-modal');
        let bottomSP = 26;
        let bottomIPad = 19.5;
        let bottomPC = 9;
        if (isInputCommentUp) {
            bottomSP = 29.5;
            bottomIPad = 22;
            bottomPC = 12.5;
        }
        if (planApproveBtn.length > 0) {
            if (windowWidth < widthSP) {
                planApproveBtn.css('bottom', `${bottomSP}%`)
            } else if (windowWidth > widthSP && windowWidth < widthIpad) {
                planApproveBtn.css('bottom', `${bottomIPad}%`)
            } else {
                planApproveBtn.css('bottom', `${bottomPC}%`)
            }
        }
    }

 */

$(document).ready(function () {
    let navbar_active_class = 'navbar-active';
    $('.switch-checkbox-tr').on('change', function () {
        let switch_right = $('.switch-talk-room.text-right');
        let switch_left = $('.switch-talk-room.text-left');
        let checked = $(this).is(':checked');
        console.log('checked: ', checked)
        let orderMonthy = $('input#order-monthy');
        // orderMonthy.parent('.form-group-new').find('.switch-talk-room').removeClass('navbar-active')
        if (checked) {
            // orderMonthy.parent('.form-group-new').find('.text-right').addClass('navbar-active')
            $('.pd-product-comment').removeClass('show-comment-unresolved').addClass('show-comment-all')
            switch_left.removeClass(navbar_active_class);
            switch_right.addClass(navbar_active_class);
        } else {
            // orderMonthy.parent('.form-group-new').find('.text-left').addClass('navbar-active')
            $('.pd-product-comment').removeClass('show-comment-all').addClass('show-comment-unresolved')
            switch_right.removeClass(navbar_active_class);
            switch_left.addClass(navbar_active_class);
        }
    });
    $('.switch-talk-room').on('click', function (e) {
        if ($(this).hasClass('navbar-active')) {
            e.preventDefault();
            return;
        }
        should_scroll = false;
    })

    $('.switch-dm-txt').on('click', function (e) {
        console.log('click switch dm')
        if ($(this).hasClass('navbar-active')) {
            e.preventDefault();
            return;
        }
        $('.switch-dm').addClass('tab-disabled');
        $('.switch-dm-txt').removeClass('navbar-active');
        $(this).addClass('navbar-active');
        should_scroll = false;
    })
})

function scrollListComment(el) {
    let navigationBar = $('.block-navigation-bar');
    let footerCommentBlockOffer = $('#footerCommentBlockOffer');
    if (navigationBar.length > 0 && navigationBar.hasClass('hide') && footerCommentBlockOffer.length > 0) {
        footerCommentBlockOffer.css('bottom', 0)
    }
    let lastScrollTop = 0;
    let headerGlobal = $('.sheader')
    let topAppBar = $('.new-banner-project')
    let navTopAppBar = $('.navigation-top-app-bar')
    let leftSidebar = $('#left-sidebar')
    let budgetLog = $('#budgetLogSidebar')
    let projectItemDetail = $('#projectItemDetail')
    let footerComment = $('.footer-comment-block')
    let btnAddOffer = $('.action-add-offer')
    let pdSection_file = $('.pd-section-file')
    let content_message = $('.project-tab.project-tab-messenger.active')
    let left_offer = $('.column-list-offer .mcolumn-content')
    let add_offer_button = $('.action-panel-head-custom')
    let column_header = $('.mcolumn-header');
    let height_column_header = column_header.outerHeight(true);
    const space_margin_top = '40px';
    const space_margin_top_small = '2px';
    const height_input_box = 85;
    topAppBar.css('transition', 'top 0.2s')
    headerGlobal.css('transition', 'top 0.2s')
    navigationBar.css('transition', 'bottom 0.2s')
    leftSidebar.css('transition', 'bottom 0.2s')
    leftSidebar.css('transition', 'top 0.2s')
    budgetLog.css('transition', 'top 0.2s')
    let height_project_banner = topAppBar.outerHeight();
    let height_sheader = headerGlobal.outerHeight();
    let height_navbar_bottom = navigationBar.outerHeight();
    let height_nav_top_app_bar = 0;
    let height_bottom_nav_bar = navigationBar.outerHeight();
    let height_footer_comment = footerComment.outerHeight();
    let height_block_nav_bottom_bar = 0;
    let message_list = $('.mmessage-list');
    let first_scroll_up = true;
    let first_scroll_down = true;
    if (navigationBar.length > 0 && !navigationBar.hasClass('hide')) {
        height_block_nav_bottom_bar = navigationBar.outerHeight();
    }
    if (navTopAppBar.length > 0) {
        height_nav_top_app_bar = navTopAppBar.outerHeight();
    }
    let total_height_banner_header = height_sheader + height_project_banner;
    let space_top_content = total_height_banner_header + height_nav_top_app_bar;
    let timeStamp_last_scroll = 0;
    el.scroll(function (e) {
        e.preventDefault()
        let st = $(this).scrollTop();
        let timeStamp_scroll = e.timeStamp;
        if (!should_scroll || timeStamp_scroll - timeStamp_last_scroll < 130) {
            e.stopPropagation();
            setTimeout(() => {
                should_scroll = true;
            }, 1500);
            return false;
        }
        // scroll down
        if (st > lastScrollTop && lastScrollTop > 0) {
            if (first_scroll_down) {
                first_scroll_down = false;
                first_scroll_up = true;
                console.log('message list scroll down');
                headerGlobal.css('top', `-${height_sheader}px`);
                topAppBar.css('top', '0')
                navigationBar.css('bottom', `-${height_block_nav_bottom_bar}px`)
                navTopAppBar.css('top', `${height_project_banner}px`);
                leftSidebar.css('top', `${height_project_banner}px`);
                footerComment.css('bottom', '0px');
                btnAddOffer.css('bottom', '0');
                budgetLog.css('top', `${height_project_banner}px`);
                projectItemDetail.css('top', `${height_project_banner}px`);
                $('.mmessage-component').css('max-height', `calc(100vh - ${height_project_banner + height_footer_comment}px)`);
                if ($(window).width() < max_width_sp_device) {
                    $('.column-list-offer').css('margin-top', space_margin_top_small);
                    if ($('.project-tab-messenger.active').length > 0) {
                        footerComment.css({
                            'bottom': '0',
                            'margin-bottom': '8px'
                        })
                        footerComment.find('.action-panel-head').css('margin-bottom', '8px')
                    } else {
                        footerComment.css('bottom', '0')
                    }
                    content_message.css('top', `${height_project_banner}px`);
                    left_offer.css('height', `calc(100vh - ${height_project_banner + height_nav_top_app_bar + add_offer_button.outerHeight()}px)`)
                    if (navigationBar.hasClass('hide')) {
                        $('.martist.role_master_client').css('margin-top', '0')
                    }
                    // message_list.css('height', `calc(100vh - ${height_column_header + height_project_banner + height_footer_comment}px)`)
                } else if ($(window).width() > max_width_sp_device && $(window).width() < max_width_tablet_device) {
                    content_message.css('top', `${height_project_banner}px`);
                    $('.mcolumn--right').css('margin-top', space_margin_top)
                    footerComment.css('bottom', '0');
                    $('.column-list-offer').css('margin-top', space_margin_top);
                } else {
                    footerComment.css('bottom', '0');
                    content_message.css('top', `${height_project_banner}px`);
                    $('.mcolumn--right').css('margin-top', space_margin_top)
                    $('.column-list-offer').css('margin-top', space_margin_top);
                }
                pdSection_file.css({
                    'height': `calc(100vh - ${height_project_banner + height_nav_top_app_bar}px)`,
                    'max-height': `calc(100vh - ${height_project_banner + height_nav_top_app_bar}px)`,
                })
            }
        } else {
            if (first_scroll_up && lastScrollTop > 0) {
                first_scroll_up = false;
                first_scroll_down = true;
                console.log('message list scroll up');
                headerGlobal.css('top', '0')
                topAppBar.css('top', `${height_sheader}px`)
                navigationBar.css('bottom', '0')
                navTopAppBar.css('top', `${total_height_banner_header}px`);
                leftSidebar.css('top', `${total_height_banner_header}px`);
                btnAddOffer.css('bottom', `${height_block_nav_bottom_bar}px`);
                budgetLog.css('top', `${total_height_banner_header}px`);
                $('.mmessage-component').css('max-height', `calc(100vh - ${headerGlobal.outerHeight() + height_project_banner + height_footer_comment + navigationBar.outerHeight()}px)`);
                if ($(window).width() < max_width_sp_device) {
                    if ($('.project-tab-messenger.active').length > 0) {
                        if (navigationBar.hasClass('hide')) {
                            footerComment.css({
                                'bottom': `${height_navbar_bottom}px`,
                                'margin-bottom': '0'
                            })
                        } else {
                            footerComment.css({
                                'bottom': `${(Math.min(footerCommentBlockOffer.innerHeight() || 0, 80)) + height_navbar_bottom}px`,
                                'margin-bottom': '0'
                            })
                        }
                        footerComment.find('.action-panel-head').css('margin-bottom', '0px')
                        // message_list.css('height', `calc(100vh - ${height_sheader + height_column_header + height_project_banner + height_footer_comment + height_block_nav_bottom_bar}px)`)
                    } else {
                        footerComment.css('bottom', `${height_navbar_bottom}px`)
                    }
                    $('.prdt .column-list-offer').css('margin-top', space_margin_top_small)
                    content_message.css('top', `${height_sheader + height_project_banner}px`);
                    left_offer.css('height', `calc(100vh - ${headerGlobal.outerHeight() + height_bottom_nav_bar + height_project_banner + height_nav_top_app_bar + add_offer_button.outerHeight()}px)`)
                    if (navigationBar.hasClass('hide')) {
                        $('.martist.role_master_client').css('margin-top', '0')
                    }
                } else {
                    footerComment.css('bottom', `${height_block_nav_bottom_bar}px`);
                    content_message.css('top', `${total_height_banner_header}px`);
                    $('.prdt .column-list-offer').css('margin-top', space_margin_top)
                }
                $('.prdt .mcolumn--right').css('margin-top', is_pc === "True" ? space_margin_top : '')
                pdSection_file.css({
                    'height': `calc(100vh - ${height_project_banner + height_nav_top_app_bar + height_bottom_nav_bar + height_sheader}px)`,
                    'max-height': `calc(100vh - ${height_project_banner + height_nav_top_app_bar + height_bottom_nav_bar + height_sheader}px)`,
                })
                if (st === 0) {
                    if ($(window).width() < max_width_sp_device) {
                        $('.column-list-offer').css('margin-top', space_margin_top_small)
                        content_message.css('top', `${height_sheader + height_project_banner}px`);
                    } else {
                        $('.column-list-offer').css('margin-top', '0')
                        content_message.css('top', `${space_top_content}px`);
                    }
                    $('.mcolumn--right').css('margin-top', '0')
                    projectItemDetail.css('top', `${height_project_banner + height_sheader + height_nav_top_app_bar}px`);
                } else {
                    projectItemDetail.css('top', `${height_project_banner + height_sheader}px`);
                }
            }
        }
        setTimeout(function () {
            calcHeightCalendarModal();
        }, 300)
        if ($(window).width() > max_width_sp_device && $(window).width() < max_width_tablet_device) {
            setTimeout(function () {
                calcHeightColumnRight()
            }, 230)
        }
        timeStamp_last_scroll = timeStamp_scroll;
        // scroll max bottom
        lastScrollTop = st;
    });
}

function initializeHLSVideo(video) {
    if (!video) return;
    
    const videoSrc = video.getAttribute('data-video-src');
    const isHLS = video.getAttribute('data-is-hls') === 'true';
    
    if (isHLS && videoSrc && videoSrc !== 'undefined') {
        if (typeof Hls !== 'undefined' && Hls.isSupported()) {
            const hls = new Hls();
            hls.loadSource(videoSrc);
            hls.attachMedia(video);
            
            // Store HLS instance for cleanup
            video._hlsInstance = hls;
        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
            // Safari native HLS support
            video.src = videoSrc;
        }
    }
}

function initializeAllHLSVideos() {
    // Initialize all videos with HLS data attributes
    const videos = document.querySelectorAll('video[data-is-hls="true"]');
    videos.forEach(video => {
        if (!video._hlsInstance) {
            initializeHLSVideo(video);
        }
    });
}

// Auto-initialize HLS videos when DOM changes
if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        // Check if the added node is a video or contains videos
                        const videos = node.tagName === 'VIDEO' ? [node] : node.querySelectorAll ? node.querySelectorAll('video[data-is-hls="true"]') : [];
                        videos.forEach(initializeHLSVideo);
                    }
                });
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}