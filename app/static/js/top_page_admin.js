let thumbnail_base64 = [];
function initAdminNewUpload() {
    let wait_length = 0;
    let id_movie = $('#id_movie');
    let title_dom = $('#id_title_val');
    title_dom.prop('required', true);

    $("#id_movie").off('change').on('change', function () {
        let filenames = [];
        let common_prefix = "";

        $('.modal-container:not(.file-selected)').addClass('file-selected');
        $('#id_list_width, #id_list_height').val('');
        $('#thumbnail_base_64').val('');
        $('.canvas').css('display', 'none');
        $('input.btn-submit-upload-scene-title:not(.hide)').addClass('hide');

        $('#video-demo-container').empty();

        $('#video-demo-container').append(`
            <div class="video-demo-header">
                <div class="video-header col-12 col-sm-6 col-md-4">プレビュー:</div>
                <div class="thumbnail-header col-12 col-sm-6 col-md-4">サムネイル:</div>
            </div>`);

        // Object Url as the video source
        let upload_files = this.files;
        let len_file = upload_files.length;

        if(!len_file) {
            hideButtonUpload();
        }
        if (len_file > 1) {
            $('#id_thumbnail').parent('p').hide();
            $('#pre_image').attr('src', '');

            for (let index = 0; index < len_file; index++){
                filenames.push(upload_files[index].name);
            }

            common_prefix = longestCommonPrefix(filenames);

        } else {
            $('#id_thumbnail').parent('p').show();
        }

        for (let index = 0; index < len_file; index++) {
            let file_name = upload_files[index].name;
            thumbnail_base64.push(index);
            setTimeout(function() {
                if (file_name.match(/\.(mp4|x-m4v|webm)$/)) {
                    $('#video-demo-container').append(`
                    <div class="video-demo-item">
                        <div class="dom-video col-12 col-sm-6 col-md-4">
                            <video id="main-video-${index}" controls>
                                <source type="video/mp4" src="">
                            </video>
                        </div>  
                        <div class="canvas col-12 col-sm-6 col-md-4">
                            <canvas id="video-canvas-${index}"></canvas>
                            <canvas id="video-canvas-resize-${index}" class="hide"></canvas>
                        </div>  
                    </div>
                    <div class="variation-values">
                        <p class="file-name-${index}"></p>
                        <label for="id_variation_val_${index}">バリエーション名　＊</label>
                        <input type="text" name="variation_val_${index}" maxlength="128" id="id_variation_val_${index}" required
                               oninput="this.setCustomValidity('')">
                        <div class="error-message">このフィールドは必須項目です。</div>
                    </div>`);
                } else if (file_name.match(/\.(mp3|wav)$/)) {
                    $('#video-demo-container').append(`
                    <div class="video-demo-item">
                        <div class="dom-video col-12 col-sm-6 col-md-4">
                            <video id="main-video-${index}" controls>
                                <source type="video/mp4" src="">
                            </video>
                        </div>  
                        <div class="canvas col-12 col-sm-6 col-md-4">
                            <canvas id="video-canvas-${index}"></canvas>
                            <canvas id="video-canvas-resize-${index}" class="hide"></canvas>
                        </div>  
                    </div>
                    <div class="variation-values">
                        <p class="file-name-${index}"></p>
                        <label for="id_variation_val_${index}">バリエーション名　＊</label>
                        <input type="text" name="variation_val_${index}" maxlength="128" id="id_variation_val_${index}" required
                               oninput="this.setCustomValidity('')">
                        <div class="error-message">このフィールドは必須項目です。</div>
                    </div>`);

                } else if (file_name.match(/\.(pdf)$/)) {
                     $('#video-demo-container').append(`
                    <div class="video-demo-item">
                        <div class="dom-video col-12 col-sm-6 col-md-4">
                        <iframe id="main-video-${index}" class="scrollbar" style="width: 100%; height: 300px"></iframe>
                        </div>  
                        <div class="canvas col-12 col-sm-6 col-md-4">
                            <canvas id="video-canvas-${index}"></canvas>
                            <canvas id="video-canvas-resize-${index}" class="hide"></canvas>
                        </div>  
                    </div>
                    <div class="variation-values">
                        <p class="file-name-${index}"></p>
                        <label for="id_variation_val_${index}">バリエーション名　＊</label>
                        <input type="text" name="variation_val_${index}" maxlength="128" id="id_variation_val_${index}" required
                               oninput="this.setCustomValidity('')">
                        <div class="error-message">このフィールドは必須項目です。</div>
                    </div>`);
                }

            //set value for variations
            let variation_val = upload_files[index].name.replace(common_prefix, "").split('.')[0];
            $('input#id_variation_val_'+index).val(variation_val);
            $('.variation-values .file-name-'+index).text('ファイル名:　' + upload_files[index].name);


            let reader = new FileReader();
                reader.onload = function (readerEvent) {
                    if (document.querySelector("#main-video-" + index + " source")) {
                        document.querySelector("#main-video-" + index + " source").setAttribute('src', URL.createObjectURL(document.querySelector('#id_movie').files[index]));
                        let _VIDEO = $('video#main-video-' + index).get(0);
                        _VIDEO.style.display = 'inline';
                        _VIDEO.preload = 'auto';
                        $(_VIDEO).on('canplay', function () {
                            $(_VIDEO).off('canplay');
                            let _CANVAS_DOM = $(_VIDEO).parent().siblings('.canvas');
                            let _CANVAS = _CANVAS_DOM.find("#video-canvas-" + index).get(0);
                            let _CANVAS_RESIZE = _CANVAS_DOM.find("#video-canvas-resize-" + index).get(0);
                            let _CTX = _CANVAS.getContext("2d");
                            let _CTX_RESIZE = _CANVAS_RESIZE.getContext("2d");


                            let video_duration = _VIDEO.duration,
                                duration_options_html = '';

                            // Set options in dropdown at 4 second interval
                            for (let i = 0; i < Math.floor(video_duration); i = i + 4) {
                                duration_options_html += '<option value="' + i + '">' + i + '</option>';
                            }

                            // Set canvas dimensions same as video dimensions
                            _CANVAS.width = _VIDEO.videoWidth;
                            _CANVAS.height = _VIDEO.videoHeight;
                            _CANVAS_DOM.css('display', 'block');
                            let timeout = 1000;

                            setTimeout(function () {
                                getThumbnail(_CANVAS_RESIZE, _CTX_RESIZE, _CTX, _VIDEO);
                                thumbnail_base64[index] = _CANVAS_RESIZE.toDataURL();
                                $(_VIDEO).on('seeked', function () {
                                    $('.canvas').css('display', 'block');
                                    $('#pre_image').css('display', 'none');
                                    // $('.canvas').css('display', 'block');
                                    getThumbnail(_CANVAS_RESIZE, _CTX_RESIZE, _CTX, _VIDEO);
                                    thumbnail_base64[index] = _CANVAS_RESIZE.toDataURL();
                                });
                            }, timeout + index * 500);
                        });
                        _VIDEO.load();

                } else {
                    document.querySelector("#main-video-" + index).setAttribute('src', URL.createObjectURL(document.querySelector('#id_movie').files[index]));

                }

            };

            reader.readAsDataURL(document.querySelector('#id_movie').files[index]);
            }, index*1000);
        }

        setTimeout(function() {
            showButtonUpload();
        }, 2000)
    });

    let scene_id = window.location.search.split('&scene_id=')[1];
    if (scene_id) {
        $('#id_version').val(scene_id);
    }
    let resizeImg = function (width, height) {

        let targetWidth = 480;
        if (width <= targetWidth) {
            return {width: width, height: height}
        }

        let ratio = targetWidth / width;

        return {
            width: parseInt(width * ratio),
            height: parseInt(height * ratio)
        }
    };

    let storefile = 'false';
    let thumbnail = $('#id_thumbnail');
    // thumbnail.on('click', function () {
    //     if (storefile != 'false') {
    //         storefile = this.files
    //     }
    // });
    //
    // thumbnail.on('change', function () {
    //     if (this.files && this.files[0] && this.files[0].name.match(/\.(jpg|jpeg|png|gif|JPG|PNG|JPEG|GIF)$/)) {
    //         let reader = new FileReader();
    //         storefile = this.files;
    //         reader.onload = function (e) {
    //             $('#pre_image').attr('src', e.target.result);
    //         };
    //         reader.readAsDataURL(this.files[0]);
    //         $('.canvas').css('display', 'none');
    //         $('#pre_image').css('display', 'block');
    //     } else {
    //         if (storefile == 'false') {
    //             alert('画像をアップロードしてください。アップロードしたファイルは画像でないか、または壊れています。');
    //             $('#id_thumbnail').val('').clone(true);
    //         } else {
    //             this.files = storefile;
    //         }
    //     }
    // });

    id_movie.on('change', function () {
        if (this.files && this.files.length != 0) {
            for (x = 0; x < this.files.length; x++) {
                let file_name = this.files[x].name.toLowerCase();
                if (!file_name.match(/\.(mp4|x-m4v|webm|pdf|mp3|wav|mov|gif)$/)) {
                    alert('ファイルのフォーマットが正しくありません。MP4, webm, mov, mp3, wav, pdfファイルをアップロードしてください。');
                    id_movie.val('').clone(true);
                }
            }
        } else {
            $("#main-video").css('display', 'none');
            return false;
        }
    });

    function getThumbnail(_CANVAS_RESIZE, _CTX_RESIZE, _CTX, _VIDEO) {
        let imgResize = resizeImg(_VIDEO.videoWidth, _VIDEO.videoHeight);
        _CANVAS_RESIZE.width = imgResize.width;
        _CANVAS_RESIZE.height = imgResize.height;
        _CTX_RESIZE.drawImage(_VIDEO, 0, 0, imgResize.width, imgResize.height);

        _CTX.drawImage(_VIDEO, 0, 0, _VIDEO.videoWidth, _VIDEO.videoHeight);
    }

    function hideButtonUpload () {
        let btn = $('input.btn-submit-upload-scene-title');
        btn.addClass('hide');
        btn.off();
    }

    function showButtonUpload () {
        let btn = $('input.btn-submit-upload-scene-title');
        btn.removeClass('hide');
        btn.off().on('click', function(e) {
            e.preventDefault();
            let form = $('#form-upload');
            let url = '';
            let message = '';
            let success_message = '';
            let success_message_title = '';
            if (form.length) {
                if (form[0].action === window.location.origin + '/top/upload_video') {
                    url = '/top/upload_video';
                    success_message_title = '';
                    success_message = 'アップロード完了';

                    let variation_values = [];
                    for (let i = 0; i < id_movie[0].files.length; i++) {
                        if (i === 0) {
                            $('#thumbnail_base_64').val(thumbnail_base64[0]);
                        } else {
                            $('#thumbnail_base_64').val($('#thumbnail_base_64').val() + '|' + thumbnail_base64[i]);
                        }
                    }

                    $('[id^=main-video]').each(function (i, e) {
                        let video_with = '0';
                        let video_height = '0';
                        if ($(this)[0].nodeName === 'VIDEO') {
                            video_with = this.videoWidth;
                            video_height = this.videoHeight;
                        }
                        if (i === 0) {
                            $('#id_list_width').val(video_with);
                            $('#id_list_height').val(video_height);
                        } else {
                            $('#id_list_width').val($('#id_list_width').val() + ',' + video_with);
                            $('#id_list_height').val($('#id_list_height').val() + ',' + video_height);
                        }

                    });

                    $('input[id^=id_variation_val]').each(function (i, e) {
                        let variation_value = $('input#id_variation_val_' + i).val();
                        if (variation_value) {
                            variation_values.push(variation_value);
                        }
                    });

                    if (title_dom.prop('type') !== 'hidden' && title_dom.val() === null || title_dom.prop('type') !== 'hidden' && title_dom.val().match(/^ *$|^　*$/) !== null) {

                        title_dom[0].setCustomValidity('正しいタイトルを入力してください。');
                        title_dom.on('input', function () {
                            $(this)[0].setCustomValidity('');
                        })
                    } else if ($('#id_thumbnail').val() === '' && $('#thumbnail_base_64').val() === '') {
                        return false;
                    }

                    //get product_scene_id
                    let product_scene_val = $('#id-product-scene').val().trim();
                    let product_scene_id = $("#product_scene option[data-name='" + product_scene_val + "']");
                    if (!product_scene_val.length && !product_scene_id.length) {
                        $('#id-product-scene')[0].setCustomValidity('this is a required field');
                    }

                    let thumbnail = $('#id_thumbnail');
                    let thumbnail_base_64 = $('#thumbnail_base_64');
                    let movies = $('#id_movie');
                    let version = $('#id_version');
                    let title = $('#id_title_val');
                    let product = $("#id_product");
                    product.val($('.project-item.active').data('project-id'));
                    let no_thumbnail = thumbnail.val() === '' && thumbnail_base_64.val() === '';
                    let list_width = $('#id_list_width');
                    let list_height = $('#id_list_height');
                    let data = new FormData();

                    if (!no_thumbnail && movies.length && movies[0].files.length && title.length && title.val().length && product.length && product.val().length && list_width.length && list_height.length && version.length) {
                        data.append('variation_values', JSON.stringify(variation_values));
                        data.append('list_width', list_width.val());
                        data.append('list_height', list_height.val());
                        data.append('version', version.val());
                        data.append('thumbnail', thumbnail.val());
                        data.append('thumbnail_base_64', thumbnail_base_64.val());
                        data.append('movies', movies.files);
                        for (let x = 0; x < movies[0].files.length; x++) {
                            data.append("movie", movies[0].files[x]);
                        }
                        if (title.attr('data-title-id')) {
                            data.append('title', title.attr('data-title-id'));
                        } else {
                            data.append('title', title.val());
                        }
                        data.append('product', product.val());
                        if (product_scene_id.length) {
                            data.append('product_scene_id', product_scene_id.attr('data-value'));
                        }
                        data.append('product_scene_val', product_scene_val);
                    }

                    if (!$('form#form-upload')[0].checkValidity()) {
                        $('input:invalid').each(function (i, e) {
                            $(e).siblings('.error-message').addClass('show');
                        });
                        return false;
                    } else {
                        let valid = true;
                        $('input[name^="variation_val_"]').each(function (i, e) {
                            if (!e.checkValidity()) {
                                $(e).siblings('.error-message').addClass('show');
                                e.focus();

                                $(e).off().on('input', function () {
                                    $(this).siblings('.error-message').removeClass('show');
                                });

                                valid = false;
                                return false;
                            }
                        });

                        if (valid) {
                            sendForm(data, url, success_message, success_message_title, 'upload');
                        }
                    }
                } else if (form[0].action === window.location.origin + '/top/edit_scene') {
                    url = '/top/edit_scene';
                    message = 'アップロードしています…';
                    success_message_title = '動画編集';
                    success_message = '編集しました';

                    let variation_values = [];
                    $('#thumbnail_base_64').val(thumbnail_base64[0]);

                    $('video[id^=main-video]').each(function (i, e) {
                        $('#id_list_width').val(this.videoWidth);
                        $('#id_list_height').val(this.videoHeight);
                    });

                    $('input[id^=id_variation_val]').each(function (i, e) {
                        let variation_value = $('input#id_variation_val_' + i).val();
                        if (variation_value) {
                            variation_values.push(variation_value);
                        }
                    });

                    //get product_scene_id
                    let product_scene_val = $('#id-product-scene').val();
                    let product_scene_id = $("#product_scene option[data-name='" + product_scene_val + "']");

                    let thumbnail = $('#id_thumbnail');
                    let thumbnail_base_64 = $('#thumbnail_base_64');
                    let movies = $('#id_movie');
                    let version = $('#id_version');
                    let title = $('#id_title_val');
                    let product = $("#id_product");
                    product.val($('.project-item.active').data('project-id'));
                    let no_thumbnail = thumbnail.val() === '' && thumbnail_base_64.val() === '';
                    let list_width = $('#id_list_width');
                    let list_height = $('#id_list_height');
                    let data = new FormData();

                    if (!no_thumbnail && title.length && title.val().length && product.length && product.val().length && list_width.length && list_height.length && version.length) {
                        data.append('variation_values', JSON.stringify(variation_values));
                        data.append('list_width', list_width.val());
                        data.append('list_height', list_height.val());
                        data.append('version', version.val());
                        data.append('thumbnail', thumbnail.val());
                        data.append('thumbnail_base_64', thumbnail_base_64.val());
                        if( movies.length && movies[0].files.length) {
                            data.append("movie", movies[0].files[0]);
                        }

                        if (title.attr('data-title-id')) {
                            data.append('title_id', title.attr('data-title-id'));
                            data.append('title', title.val());
                        } else {
                            data.append('title', title.val());
                        }

                        data.append('product', product.val());
                        if (product_scene_id.length) {
                            data.append('product_scene_id', product_scene_id.attr('data-value'));
                        }
                        data.append('product_scene_val', product_scene_val);
                    }

                    let valid = true;
                    let variation = $('input[name^="variation_val_0"]').get(0);
                    if (!variation.checkValidity()) {
                        $(variation).siblings('.error-message').addClass('show');
                        variation.focus();

                        $(variation).off().on('input', function () {
                            $(this).siblings('.error-message').removeClass('show');
                        });

                        valid = false;
                        return false;
                    }

                    if (valid) {
                        sendForm(data, url, success_message, success_message_title, 'update');
                    }
                }
            }
        });

        $('input').not('#id_title_val').on('input', function () {
            $(this).siblings('.error-message').removeClass('show');
        })
    }

    function sendForm(data, url, success_message, success_message_title, type) {
        let progressDom = $('.upload-button-wrapper');
        let timeoutUpload;
        $.ajax({
            type: "POST",
            url: url,
            data: data,
            cache: false,
            processData: false,
            contentType: false,
            beforeSend: function() {
                activeProgress();
                progressDom.find('.fill .process').css('width', '10%');
            },
            xhr: function () {
                let xhr = new window.XMLHttpRequest();
                xhr.upload.addEventListener('progress', function (evt) {
                    if (evt.lengthComputable) {
                        let percentComplete = (evt.loaded / evt.total) * 70;
                        $('.upload-button-wrapper .fill .process').css('width', percentComplete + '%');
                    }
                }, false);
                return xhr;
            },
            success: function (data) {
                if (data.error){
                    toastr.error('エラーコード: ' + data.error);
                    setTimeout(function () {
                        progressDom.removeClass('clicked').addClass('success');
                        removeProgress();
                        $('.popup__close')[0].click();
                    }, 200);
                } else {
                    // toastr.success(success_message, success_message_title);
                    $('.popup__close')[0].click();
                    if (data.is_new) {
                        let add_scene_dom = $('.pd-chapter[data-product-scene-id^=' + data.product_scene_id + ']').find('.pd-chapter__add');
                        $(data.cscene_html).insertAfter(add_scene_dom);
                    }
                    progressDom.find('.fill .process').css('width', '100%');
                    let variation_active = data.variation;
                    updateTopProjectVideoWattingFeedback();
                    setTimeout(function () {
                        progressDom.removeClass('clicked').addClass('success');
                    }, 200);
                    setTimeout(function () {
                        removeProgress();
                        window.location = window.location.pathname + '/scene/' + variation_active;
                    }, 1000)
                }
            },
            fail: function (data) {
                toastr.error('エラーコード: '+ data.error);
                removeProgress();
            },
            error: function(response){
                toastr.error('エラーコード:' + response.responseJSON.message);
                removeProgress();
            },
            complete:function(data){
                clearInterval(timeoutUpload);
                $(".popup-inner").find(".loader").hide();
            }
        })
    }

    $('.video-upload').off().on('click', function() {
        $('form#form-upload')[0].reset();
        $('form#form-upload input#id_version').val(null);
        $('#id_title_val').val($(this).parents('.project-video-item').attr('data-scene-title'));
        $('#id_title_val').attr('readonly', 'true');
        $('#id_title_val').attr('data-title-id', $(this).parents('.project-video-item').attr('data-scene-title-id'));
        $('#id-product-scene').val($(this).parents('.project-video-item').attr('data-product-scene'));
        $('#id-product-scene').attr('readonly', 'true');
        $('.modal-container h1').text('バリエーションをアップロード');
        $('.modal-container').css('display', 'flex');
        $('#form-upload input[type^="submit"]')[0].value = 'アップロード';
        $('#form-upload input[type^="file"]')[0].setAttribute('multiple', '');
        $('.preview #video-demo-container').empty();
        $('.button-delete-video').addClass('hide');
    });

    $('.button-upload-video-scene-title').off().on('click', function() {
//        $('#id_title_val').removeAttr('readonly');
//        $('form#form-upload')[0].reset();
//        $('.modal-container h1').text('演出をアップロード');
        $('.modal-container').css('display', 'flex');
        $('#create-chapter').find('input.input-chapter-name').val('')
//        $('#form-upload input[type^="submit"]')[0].value = 'アップロード';
//        $('#form-upload input[type^="file"]')[0].setAttribute('multiple', '');
//        $('.preview #video-demo-container').empty();
//        $('.button-delete-video').addClass('hide');
        $('#create-chapter .smodal-close').on('click', function(e) {
            e.preventDefault();
            $('.modal-container').css('display', 'none');
        })
    });

    $('.popup__close').on('click', function(e) {
        $(this).parents('.popup-add-chapter').hide();

    });

    $('.video-item-edit').off().on('click', function () {
        let form_upload =  $('form#form-upload').first();
        let origin_title = $(this).parents('.project-video-item.show-comment').data('scene-title');
        $('form#form-upload')[0].action = window.location.origin + '/top/edit_scene';
        $('form#form-upload')[0].reset();
        let scene_id = $(this).parents('.video-item-list.active .video-item-component.active').attr('data-scene-id');
        form_upload.find('#id_version').val(scene_id);
        form_upload.find('#id_title_val').val(origin_title);
        form_upload.find('#id_title_val').attr('data-title-id', $(this).parents('.project-video-item.show-comment').data('scene-title-id'));
        form_upload.find('#id-product-scene').val($(this).parents('.project-video-item').data('product-scene'));
        form_upload.find('#id-product-scene').attr('readonly', 'true');
        $('.modal-container h1').text('動画編集');
        $('.modal-container').css('display', 'flex');
        $('.preview #video-demo-container').empty();
        form_upload.find('input[type^="submit"]')[0].value = '更新';
        $('.button-delete-video').removeClass('hide');
        $('.button-delete-video').attr('data-scene-id', scene_id);
        $('#video-demo-container').append(`
            <div class="video-demo-header">
                <div class="video-header col-12 col-sm-6 col-md-4">プレビュー:</div>
                <div class="thumbnail-header col-12 col-sm-6 col-md-4">サムネイル:</div>
            </div>
            <div class="video-demo-item">
                <div class="dom-video col-12 col-sm-6 col-md-4">
                    <video id="main-video-0" crossorigin="anonymous" controls>
                        <source type="video/mp4">
                    </video>
                </div>

                <div class="canvas col-12 col-sm-6 col-md-4">
                    <canvas id="video-canvas-0"></canvas>
                    <canvas id="video-canvas-resize-0" class="hide"></canvas>
                </div>

                <div class="center-img-thumbnail">
                    <img src="" id="pre_image" alt="">
                </div>
            </div>

            <div class="variation-values">
                <p class="file-name-0">ファイル名:　${$(this).siblings('video').find('source')[0].src.match(/[^/]+$/g)}</p>
                <label for="id_variation_val_0">バリエーション名　＊</label>
                <input type="text" name="variation_val_0" maxlength="128" id="id_variation_val_0" required
                    oninput="this.setCustomValidity('')">
                <div class="error-message">このフィールドは必須項目です。</div>
            </div>`);

        form_upload.find('.error-message').removeClass('show');
        let exists_title_el = form_upload.find('#exists-title-vals .title');
        let exists_title_vals = [];
        exists_title_el.each(function (index, item) {
            exists_title_vals.push($(item).text());
        });

        form_upload.find('#id_title_val').on('input',function () {
            let title_val = $(this).val().trim();
            let error_message_els = $(this).parent().find('.error-message');
            if (title_val !== origin_title && exists_title_vals.includes(title_val)){
                error_message_els.first().text('このタイトルは存在しました');
                error_message_els.first().addClass('show');
                $(this).focus();
            }
            else{
                error_message_els.first().text('このフィールドは必須項目です。');
                error_message_els.first().removeClass('show');
                $(this).focus();
            }
        });

        $('input#id_variation_val_0').val($(this).siblings('.video-item-variation').text());

        let _VIDEO = $("#main-video-0")[0];
        $("#main-video-0 source")[0].setAttribute('src', $(this).siblings('video').find('source')[0].src);
        _VIDEO.style.display = 'inline';
        _VIDEO.preload = 'auto';
        $('#video-demo-container .canvas').css('background-image','url(' + $(this).siblings('video')[0].poster + ')');
        $('#video-demo-container .canvas').css('background-size','contain');
        $(_VIDEO).on('canplay', function () {
            $(_VIDEO).off('canplay');
            let _CANVAS_DOM = $(_VIDEO).parent().siblings('.canvas');
            let _CANVAS = _CANVAS_DOM.find("#video-canvas-0").get(0);
            let _CANVAS_RESIZE = _CANVAS_DOM.find("#video-canvas-resize-0").get(0);
            let _CTX = _CANVAS.getContext("2d");
            let _CTX_RESIZE = _CANVAS_RESIZE.getContext("2d");
            // Set canvas dimensions same as video dimensions
            _CANVAS.width = _VIDEO.videoWidth;
            _CANVAS.height = _VIDEO.videoHeight;
            _CANVAS_DOM.css('display', 'block');
            let timeout = 1000;

            setTimeout(function () {
                $(_VIDEO).on('seeked', function () {
                    _CANVAS_DOM.css('background-image', '');
                    $('.canvas').css('display', 'block');
                    $('#pre_image').css('display', 'none');
                    getThumbnail(_CANVAS_RESIZE, _CTX_RESIZE, _CTX, _VIDEO);
                    thumbnail_base64[0] = _CANVAS_RESIZE.toDataURL();
                });
            }, timeout);
        });

        showButtonUpload();

        $('.button-delete-video').off().on('click', function() {
            deleteVideo($(this));
        })
    });
}

function longestCommonPrefix(strs) {
    if (strs && strs.length === 0) {
        return '';
    }
    strs.sort((prev, next) => prev.length - next.length)
    let shortestStr = strs[0]
    length = shortestStr && shortestStr.length
    if (!length) {
        return ''
    }
    for (let i = length; i > 0; i--) {
        const searchStr = shortestStr.substr(0, i);
        let flag = strs.every((item) => item && item.startsWith && item.startsWith(searchStr))
        if (flag) {
            return searchStr;
        }
    }
    return ''
}


function deleteVideo(target) {
    let scene_id = target.attr('data-scene-id');
    bootbox.confirm({
        message: "本当に削除しますか？",
        buttons: {
            confirm: {
                label: 'はい',
                className: 'btn-danger btn-delete-message'
            },
            cancel: {
                label: 'いいえ',
                className: 'btn-light btn-cancel-message'
            }
        },
        callback: function (result) {
            if (result) {
                $.ajax({
                    type: "POST",
                    url: "/top/delete_scene",
                    data: {
                        'scene_id': scene_id
                    },
                    datatype: "json",
                    success: function (data) {
                        window.location = window.location.href.replace('#popup', '');
                    },
                    fail: function (data) {
                        toastr.error("エラーが発生しました", "動画削除");
                    }
                })
            }
        }
    });
}

function updateTopProjectVideoWattingFeedback(event) {
    if($('.tab--video-watting_checkback ').length > 0) {
        let projectId = $('.project-item').first().data('project-id');
        let projectItem = $('.project-item');

        if(!projectId) {
            return
        }
        ajaxUpdateListWattingsFeedback(projectItem, projectId);
    }
}

$(document).ready(function () {
    $('#choice-owner').off().on('click', function () {
        let list_owner = [];
        $("#owners option:selected").each(function () {
            list_owner.push($(this).get(0).value);
        });
        let product_id = $("#owners").data('product');
        $.ajax({
            url: "/top/update_product_owner",
            method: 'POST',
            data: {
                'list_owner': list_owner,
                'product_id': product_id
            },
            datatype: 'json',
            success: function (data) {
                if (data.status === 'success') {
                    // toastr.success('更新しました。');
                }
            },
            error: function () {
                toastr.error('何か問題が発生しました。');
            }
        });
    })
    newUpdoadEditScene();
});

// New upload - edit scene
var svg_upload = `<svg class="icon_upload_svg" width="65" height="64" viewBox="0 0 65 64" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M20.233 26.6666H24.473V39.9999C24.473 41.4666 25.673 42.6666 27.1397 42.6666H37.8063C39.273 42.6666 40.473 41.4666 40.473 39.9999V26.6666H44.713C47.0863 26.6666 48.2863 23.7866 46.6063 22.1066L34.3663 9.86661C33.3263 8.82661 31.6463 8.82661 30.6063 9.86661L18.3663 22.1066C16.6863 23.7866 17.8597 26.6666 20.233 26.6666ZM13.833 50.6666C13.833 52.1333 15.033 53.3333 16.4997 53.3333H48.4997C49.9663 53.3333 51.1663 52.1333 51.1663 50.6666C51.1663 49.1999 49.9663 47.9999 48.4997 47.9999H16.4997C15.033 47.9999 13.833 49.1999 13.833 50.6666Z" fill="#F0F0F0"/>
</svg>
`
var $dropzoneScene;
var $dropzoneTake;
let listFile = [];
let thumbnailFile = [];
let listFileName = [];
let listHeightFile = [];
let listWidthFile = [];
let defaultName = [];
var jsmediatags = window.jsmediatags;

function arrToStringWithDot(arr) {
    if(!Array.isArray(arr) || !arr.length) {
        return ''
    }

    let newStr = '';
    for(i = 0; i<arr.length; i++) {
        if(!arr[i] || i === arr.length - 1) {
            newStr += arr[i];
        } else {
            newStr += arr[i] + '.';
        }
    }

    return newStr;
}

function awaitableJsmediatags(filename) {
    return new Promise(function(resolve, reject) {
      jsmediatags.read(filename, {
        onSuccess: function(tag) {
          resolve(tag);
        },
        onError: function(error) {
        //   reject(error);
        resolve('');
        }
      });
    });
}

function awaitableFileReaderGetDementionsVideo(file) {
    return new Promise(function (resolve, reject) {
        const mime = file.type;
        const rd = new FileReader();
        rd.addEventListener('load', function (e) {
            const url = (URL || webkitURL).createObjectURL(file) // create o-URL
            const video = document.createElement("video"); // create video element

            video.preload = "metadata"; // preload setting

            video.addEventListener("loadedmetadata", function () { // when enough data loads
                resolve(video);
                (URL || webkitURL).revokeObjectURL(url); // clean up
            });
            video.src = url; // start video load
        });
        var chunk = file.slice(0, 5000000); // .5MB
        rd.readAsArrayBuffer(chunk); // read file object
        rd.onerror = function(error) {
            reject('')
        }
    })

}

function awaitableGetVideoThumb(video) {
    return new Promise(function (resolve, reject) {
        // $(video).on('canplay', function(){
        //     const _canvas = document.createElement("canvas");
        //     const _ctx = _canvas.getContext('2d');
        //     _canvas.width = video.videoWidth;
        //     _canvas.height = video.videoHeight;
        //     video.play();
        //     setTimeout(() => {
        //         video.pause();
        //         $(video).off('canplay');
        //         getThumbVideo(_canvas, _ctx, video)
        //         resolve(_canvas.toDataURL());
        //     }, 1000);
        // });
         let data = new FormData();
          data.append('file_video', video);
        $.ajax({
            type: "POST",
            url: "/get_thumbnail_video",
            processData: false,
            contentType: false,
            data: data,
            success: function (data) {
                if (data.data) {
                    resolve(data.data);
                } else {
                    toastr.error('データが見つかりません!');
                }
            },
            error: function (e) {
                toastr.error('エラーが発生しました');
            }
        });
        $(video).onerror=function(err){
            reject(err)
        }
        // return reject("")
    })

}

function awaitableFileReaderGetDementionsImage(file) {
    return new Promise(function (resolve, reject) {
        let reader = new FileReader();
        reader.onload = (function(file) {
            var image = new Image();
            image.src = file.target.result;
            image.onload = function(file) {
                height = this.height;
                width = this.width;

                var img = {
                    height: this.height,
                    width: this.width,
                    base64: this.src
                }
                resolve(img);
            };
        });
        reader.readAsDataURL(file);
    })
}

function sendAjax(data, url, success_message, success_message_title, type) {
    let progressDom = $('.upload-button-wrapper');
    let timeoutUpload;
    let chapter_id = null
    let chapter_active = $('.filter-item-project.item-chapter-active');
    let searchParams = new URLSearchParams(window.location.search);
    if (chapter_active.length > 0) {
        chapter_id = chapter_active.attr('data-ps-id');
    }else if(searchParams.has('chapter_id')) {
        chapter_id = searchParams.get('chapter_id');
    }
    return $.ajax({
        type: "POST",
        url: url,
        data: data,
        cache: false,
        processData: false,
        contentType: false,
        beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
            activeProgress();
            progressDom.find('.fill .process').css('width', '10%');
            progressDom.removeClass('success').addClass('clicked');
        },
        xhr: function () {
            let xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener('progress', function (evt) {
                if (evt.lengthComputable) {
                    let percentComplete = (evt.loaded / evt.total) * 70;
                    $('.upload-button-wrapper .fill .process').css('width', percentComplete + '%');
                }
            }, false);
            return xhr;
        },
        success: function (data) {
            if (data.error){
                toastr.error('エラーコード: ' + data.error);
                setTimeout(function () {
                    progressDom.removeClass('clicked').addClass('success');
                    removeProgress();
                }, 200);
            } else {
                progressDom.find('.fill .process').css('width', '100%');
                if(url === '/top/upload_video') {
                    // toastr.success(success_message, success_message_title);
                    if (data.is_new) {
                        let add_scene_dom = $('.pd-chapter[data-product-scene-id^=' + data.product_scene_id + ']').find('.pd-chapter__add');
                        $(data.cscene_html).insertAfter(add_scene_dom);
                    }
                    const dateTimeString = new Date().toLocaleString("en-US", {timeZone: "Asia/Tokyo"});
                    const currentDateTimeMls = new Date(dateTimeString).getTime();
                    $('.btn-upload-scene').addClass('disabled');
                    if(!data.is_empty && (new Date(data.schedule_date.replace(' - ', ' ')).getTime() < currentDateTimeMls)){
                        let variation_active = data.variation;
                        updateTopProjectVideoWattingFeedback();
                        setTimeout(function () {
                            progressDom.removeClass('clicked').addClass('success');
                        }, 200);
                        setTimeout(function () {
                            removeProgress();
                            let url_redirect = window.location.pathname + '/scene/' + variation_active;
                            if (chapter_id){
                                url_redirect += `?chapter_id=${chapter_id}`
                            }
                            window.location = url_redirect;
                        }, 1000)
                    } else {
                        setTimeout(function () {
                            progressDom.removeClass('clicked').addClass('success');
                        }, 200);
                        setTimeout(function () {
                            removeProgress();
                            window.location = window.location.pathname + `?tab=progress&chapter=${data.product_scene_id}`
                        }, 1000)
                    }
                } else if(url === '/top/upload_scene_for_scenetitle') {
                    // toastr.success(success_message, success_message_title);
                    setTimeout(function () {
                        progressDom.removeClass('clicked').addClass('success');
                    }, 200);
                    removeProgress();
                    if(data.type_upload === "take-upload"){
                        $('#modal-upload-scene .scene-list-take').append(data.html);

                        let countIndex = $('.scene-take-container').length + 1;
                        if(countIndex > 1) {
                            $('label[for="create-scene-upload-dropzone"] .contract__form-label').text('テイク'+ countIndex +'を追加');
                        } else {
                            $('label[for="create-scene-upload-dropzone"] .contract__form-label').text('ファーストテイクを追加');
                        }

                        // if(count_take === 0) {
                        // const date = new Date();
                        const newScheduleDate = moment().tz('Asia/Tokyo');
                        $("#id_scene_schedule" ).datepicker("destroy");
                        $('#id_scene_schedule').datepicker({
                            format: 'yyyy/m/d',
                            locale: 'ja',
                            container: '#modal-upload-scene .create-scene',
                            forceParse: false,
                        });

                        $('#id_scene_schedule').datepicker('setDate', formatDate(newScheduleDate));
                        $('#id_scene_schedule_time').val(`${newScheduleDate.hours()}:${newScheduleDate.minutes() >= 10 ? newScheduleDate.minutes() : '0' + newScheduleDate.minutes().toString()}`);
                        count_take = $('.scene-take-container').length;
                        // }
                    } else {
                        data.html.forEach((value) => {
                            $('#modal-take-scene .version-container').append(value);
                        })

                    }
                } else if(url === '/top/get_scene_detail_edit') {
                    if(data.type === 'detail-take') {
                        $('#modal-take-scene').find('.take-scene-name').text(`${"ファーストテイク" ? data.index === '0' : 'テイク' +(parseInt(data.index) + 1).toString()}`);
                        $('#modal-take-scene').find('.take-scene-time').text(data.created_date);
                        $('#modal-take-scene').find('.version-container').append(data.html);
                        $('#modal-take-scene').modal('show');
                        $($('.loader')[0]).hide();

                    }
                } else if(url === '/top/edit_version_detail_scene') {
                    // toastr.success(success_message, success_message_title);
                    $(data.html).insertAfter($('#modal-take-scene').find(`.version-item[data-scene-id="${data.scene_id}"]`));
                    $($('#modal-take-scene').find(`.version-item[data-scene-id="${data.scene_id}"]`)[0]).remove();
                    setTimeout(function () {
                        removeProgress();
                        progressDom.removeClass('clicked').addClass('success');
                    }, 200);
                } else if(url === '/top/update_detail_scenes') {
                    // toastr.success(success_message, success_message_title);
                    setTimeout(function () {
                        progressDom.removeClass('clicked').addClass('success');
                        removeProgress();
                        $('.btn-upload-scene').addClass('disabled');
                        if(window.location.pathname.includes('/scene/')) {
                            if((data.scene_count === 1 && !data.last_version) || !data.out_of_date) {
                                 if (!chapter_id){
                                    chapter_id = $('.txt-des-above').attr('data-product-scene-id')
                                }
                                window.location = window.location.pathname.split('/scene/')[0] + `?tab=progress&chapter_id=${chapter_id}`
                            } else {
                                if (!chapter_id){
                                    chapter_id = $('.txt-des-above').attr('data-product-scene-id')
                                }
                                if (chapter_id) {
                                    window.location.href = window.location.origin  + window.location.pathname + `?chapter_id=${chapter_id}`
                                } else {
                                    window.location.reload()
                                }
                            }
                        } else {
                            if((data.scene_count === 1 && !data.last_version) || !data.out_of_date) {
                                window.location = window.location.pathname + `?tab=progress&chapter=${data.product_scene_id}`
                            } else {
                                window.location = window.location.pathname + `/scene/${data.scene_last_version_id}`
                            }
                        }
                    }, 200);
                }

                listFile = [];
                thumbnailFile = [];
                listFileName = [];
                listHeightFile = [];
                listWidthFile = [];
                defaultName = [];
            }
        },
        fail: function (data) {
            toastr.error('エラーコード: '+ data.error);
            removeProgress();
        },
        error: function(response){
            toastr.error('エラーコード:' + response.responseJSON.message);
            removeProgress();
        },
        complete:function(data){
            clearInterval(timeoutUpload);
            removeProgress();
            $(".popup-inner").find(".loader").hide();
        }
    })
}

function scrollTopScene() {
    $(document).on('click', '#modal-upload-scene .button-scroll-top-container', function(e) {
        $($(document).find('#modal-upload-scene .create-scene')[0]).animate({
            scrollTop: 0
        }, 500);
    })
}
let arrFile = [];
let arrThumb = [];
let arrName = [];
let arrWidth = [];
let arrHeight = [];
let arrDefault = [];
let count_take = 0;

function newUpdoadEditScene() {
    let thisModal = $('#modal-upload-scene');
    thisModal.on('shown.bs.modal', function(e){
        e.preventDefault();
        initDateTimeSchedule();
        validateFormUpload();
        initDragDropScene();
        count_take = $('.scene-take-container').length;

        let countIndex = $('.scene-take-container').length + 1;
        if(countIndex > 1) {
            $('label[for="create-scene-upload-dropzone"] .contract__form-label').text('テイク'+ countIndex +'を追加');
        } else {
            $('label[for="create-scene-upload-dropzone"] .contract__form-label').text('ファーストテイクを追加');
            $(this).find('.scene-list-take').empty();
        }
    })
    actionClickTake();
    removeDataModal();
    scrollTopScene();
    
    $(document).on('shown.bs.modal', '#modal-take-scene', function(e){
        initDragDropTake();
        initSortableVersion();
        submitModalVersion();
    });
    actionUploadModalThumb();
    $(document).on('shown.bs.modal', '#modal-scene-thumbnail', function(e){
    //    actionUploadModalThumb();
    });
   
    $(document).on('hide.bs.modal', '#modal-take-scene', function(e){
        let video_modal = $('#modal-upload-scene');
        let action = $(this).attr('action-type');
        if(action === 'edit') {
            let scene_id = $('#modal-upload-scene').attr('data-scene-id');
            $.ajax({
                method: "GET",
                url: "/top/get_video_modal?scene_id=" + scene_id,
                beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                    const tempDateSchedule = $("#id_scene_schedule").val();
                    const tempTimeSchedule =  $('#id_scene_schedule_time').val();
                    video_modal.find('.modal-body').empty();
                    video_modal.find('.modal-body').html(response.html);

                    $('#id_scene_schedule').datepicker({
                        format: 'yyyy/m/d',
                        locale: 'ja',
                        container: '#modal-upload-scene .create-scene',
                        forceParse: false,
                    });

                    $('#id_scene_schedule').datepicker('setDate', formatDate(new Date(tempDateSchedule)));
                    $('#id_scene_schedule_time').val(tempTimeSchedule);
                    video_modal.modal('show');
                    let countIndex = $('.scene-take-container').length + 1;
                    if(countIndex > 1) {
                        $('label[for="create-scene-upload-dropzone"] .contract__form-label').text('テイク'+ countIndex +'を追加');
                    } else {
                        $('label[for="create-scene-upload-dropzone"] .contract__form-label').text('ファーストテイクを追加');
                        $('#modal-upload-scene .scene-list-take').empty();
                    }
                    initDragDropScene();
                    initDateTimeSchedule();

                    if ($('.scene-take-container').length === 0) {
                        // const date = new Date();
                        const newScheduleDate = moment().tz('Asia/Tokyo');
                        $("#id_scene_schedule").datepicker("destroy");
                        $('#id_scene_schedule').datepicker({
                            format: 'yyyy/m/d',
                            locale: 'ja',
                            container: '#modal-upload-scene .create-scene',
                            forceParse: false,
                        });
                        const optionGetTime = { timeZone: "Asia/Tokyo", hour12: false, hour: "2-digit", minute: "2-digit" };
                        const optionGetDate = { year: 'numeric', month: '2-digit', day: '2-digit', timeZone: 'Asia/Tokyo' };
                        const timeString = new Date().toLocaleString("ja-JP", optionGetTime);
                        const dateString = new Date().toLocaleDateString("ja-JP", optionGetDate);
                        /**  old code
                         $('#id_scene_schedule').datepicker('setDate', formatDate(newScheduleDate.add(5, 'days')));
                         $('#id_scene_schedule_time').val("12:00");
                         **/
                        //new code updated at 12/06/2023
                        $('#id_scene_schedule').datepicker('setDate', dateString);
                        $('#id_scene_schedule_time').val(timeString);
                        count_take = $('.scene-take-container').length;
                    }
                },
                error: function (response) {
                    toastr.error(response.responseJSON.message);
                }
            });
        } else {
            const scene_id = $(this).attr('data-scene-id')
            const targetDom = $(`.scene-take-container[data-scene-id=${scene_id}]`);
            if($('#modal-take-scene .version-item').length) {
                targetDom.find('.scene-take-list-variant').empty();
                for (i = 0; i < listFileName[scene_id].length; i++) {
                    targetDom.find('.scene-take-list-variant').append(`
                    <div class="variant-name-container" data-scene-id="${i}">
                    <div class="scene-variant-name">${defaultName[scene_id][i]}</div>
                    <div class="scene-variant-file">${listFileName[scene_id][i]}</div>
                    </div>`)
                }
            } else {
                targetDom.remove();
                listFile.splice((scene_id), 1)
                listFileName.splice((scene_id), 1)
                listWidthFile.splice((scene_id), 1)
                listHeightFile.splice((scene_id), 1)
                thumbnailFile.splice((scene_id), 1)
                defaultName.splice((scene_id), 1)
            }

            let countIndex = $('.scene-take-container').length + 1;
            if(countIndex > 1) {
                $('label[for="create-scene-upload-dropzone"] .contract__form-label').text('テイク'+ countIndex +'を追加');
            } else {
                $('label[for="create-scene-upload-dropzone"] .contract__form-label').text('ファーストテイクを追加');
            }

            for(i = 0; i < $('#modal-upload-scene .scene-take-container').length; i++) {
                $($('#modal-upload-scene .scene-take-container')[i]).find('.scene-take-name-content').text(`${i === 0 ? 'ファーストテイク' : `テイク${i + 1}`}`);
                $($('#modal-upload-scene .scene-take-container')[i]).attr('data-scene-id', i);
            }
        }
    });

    $(document).on('focusout', '#id_scene_schedule', function() {
        if(!$(this).val() || !(moment(new Date($(this).val()), 'YYYY/M/D', true).isValid())) {
            initDateTimeSchedule();
        }
    })

    let thumbnailNew =[];
    let listWidth =[];
    let listHeigh =[];
    let name =[];

    $('.btn-upload-scene').on('click', function() {
        const buttonDom = $(this);
        const action = thisModal.attr('action-type');
        const sceneId = thisModal.attr('product-scene-id');
        const sceneTitle = $('#id_scene_name').val().trim();
        const productId = $('.project-item.active').data('project-id');
        const productSceneVal = thisModal.attr('product-scene-val');
        const schedule_date = `${$('#id_scene_schedule').val()} - ${$('#id_scene_schedule_time').val()}`
        // const variationValues = listFileName.map((n) => {
        //     return n.map((n1) => {
        
        thumbnailNew = [];
        listWidth = [];
        listHeigh = [];
        name = [];

        if (!sceneTitle || !productId || !action) {
            return;
        }

        let url = '';
        let success_message_title = '';
        let success_message = '';

        if(action === 'create') {
            url = '/top/upload_video';
            success_message_title = '';
            success_message = 'アップロード完了';
            const dataCreate = new FormData();

            for(i = 0; i < listFile.length; i++) {
                for(j = 0; j < listFile[i].length; j++){
                    dataCreate.append(`movies_${i}`, listFile[i][j])
                }

                thumbnailNew[i] = thumbnailFile[i].join('|');
                listWidth[i] = listWidthFile[i].join('|');
                listHeigh[i] = listHeightFile[i].join('|');
                name[i] = defaultName[i].join('|');
            }
            
            dataCreate.append(`count_list_file`, listFile.length)

            dataCreate.append('product_scene_id', sceneId);
            dataCreate.append('list_width', listWidth.join('!||!'));
            dataCreate.append('list_height', listHeigh.join('!||!'));
            dataCreate.append('thumbnail_base_64', thumbnailNew.join('!||!'));
            // dataCreate.append('movies', files);
            dataCreate.append('title', sceneTitle);
            dataCreate.append('file_names', listFileName);
            dataCreate.append('product_scene_text', productSceneVal);
            dataCreate.append('variation_values', name.join('!||!'));
            dataCreate.append('schedule_date', schedule_date);
            dataCreate.append('product', productId);

            sendAjax(dataCreate, url, success_message, success_message_title, 'upload');
        } else if(action === 'edit') {
            const sceneTitleId = $('.create-scene__content').attr('data-scene-title-id');
            const dataEdit = new FormData();
            dataEdit.append('data_scene_title_id', sceneTitleId);
            dataEdit.append('scene_title_name', sceneTitle);
            dataEdit.append('schedule_date', schedule_date);

            url = '/top/update_detail_scenes';
            success_message_title = '';
            success_message = '更新しました';

            sendAjax(dataEdit, url, success_message, success_message_title, 'upload');
        }
    })
}

function validateFormUpload() {
    if(!!$('#modal-upload-scene #id_scene_name').val().trim()) {
        $('.btn-upload-scene').removeClass('disabled');
    } else {
        $('.btn-upload-scene').addClass('disabled');
    }

    $('#modal-upload-scene #id_scene_name').on('change input', function(e){
        if(!!$(this).val().trim()) {
            $('.btn-upload-scene').removeClass('disabled');
        } else {
            $('.btn-upload-scene').addClass('disabled');
        }
    })
}

function actionClickTake() {
    const thisModal = $('#modal-upload-scene');
    const takeModal = $('#modal-take-scene');
    $(thisModal).on('click', '.scene-take-container', function(e) {
        const action = thisModal.attr('action-type');
        let id_scene = $(this).attr('data-scene-id');
        if(action === 'create') {
            if($(this).find('.variant-name-container').length !== defaultName[$(this).attr('data-scene-id')].length) {
                return
            }
            takeModal.attr('data-scene-id', id_scene);
            takeModal.attr('action-type', 'create');
            takeModal.find('.take-scene-name').text($(this).find('.scene-take-name-content').text());
            takeModal.find('.take-scene-time').text($(this).find('.scene-take-time-content').text());
            id_scene = parseInt(id_scene);
            console.log("chekc length", listFileName, id_scene);
            $('#modal-take-scene .version-container').empty();
            for(i = 0; i < listFileName[id_scene].length; i++){
                $('#modal-take-scene .version-container').append(`<div class="version-item" data-scene-id="${i}" data-type="${listFile[id_scene][i].type}">
                <img src="${thumbnailFile[id_scene][i]}" alt="">
                <div class="content-container">
                  <div class="version-real-name">${defaultName[id_scene][i]}</div>
                  <div class="version-file-name">${listFileName[id_scene][i]}</div>
                </div>
                <div class="version-action-container">
                  <div class="version-action__button-container">
                    <span class="member-item__btn-move">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <g clip-path="url(#clip0_20694_7610)">
                          <path
                            d="M19 9H5C4.45 9 4 9.45 4 10C4 10.55 4.45 11 5 11H19C19.55 11 20 10.55 20 10C20 9.45 19.55 9 19 9ZM5 15H19C19.55 15 20 14.55 20 14C20 13.45 19.55 13 19 13H5C4.45 13 4 13.45 4 14C4 14.55 4.45 15 5 15Z"
                            fill="#A7A8A9" />
                        </g>
                        <defs>
                          <clipPath id="clip0_20694_7610">
                            <rect width="24" height="24" fill="white" />
                          </clipPath>
                        </defs>
                      </svg>
                    </span>
                    <span class="member-item__btn-delete">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <g clip-path="url(#clip0_20694_7613)">
                          <path
                            d="M6 19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V9C18 7.9 17.1 7 16 7H8C6.9 7 6 7.9 6 9V19ZM18 4H15.5L14.79 3.29C14.61 3.11 14.35 3 14.09 3H9.91C9.65 3 9.39 3.11 9.21 3.29L8.5 4H6C5.45 4 5 4.45 5 5C5 5.55 5.45 6 6 6H18C18.55 6 19 5.55 19 5C19 4.45 18.55 4 18 4Z"
                            fill="#A7A8A9" />
                        </g>
                        <defs>
                          <clipPath id="clip0_20694_7613">
                            <rect width="24" height="24" fill="white" />
                          </clipPath>
                        </defs>
                      </svg>
                      </span>
                  </div>
                </div>
              </div>`)
            }
            takeModal.modal('show');
        } else {
            takeModal.attr('data-scene-id', id_scene);
            takeModal.attr('action-type', 'edit');
            $('.scene-take-container').each(function(indx) {
                $(this).attr('index', indx)
            })
            const url = '/top/get_scene_detail_edit';
            const index = $(this).attr('index');
            const type = 'detail-take';
            const dataGet = new FormData();
            dataGet.append('index', index);
            dataGet.append('scene_id', id_scene);
            dataGet.append('type_data', type);
           
            $('#modal-take-scene .version-container').empty();
            // takeModal.find('.take-scene-name').text($(this).find('.scene-take-name-content').text());
            // takeModal.find('.take-scene-time').text($(this).find('.scene-take-time-content').text());
            $.ajax({
                type: "POST",
                url: url,
                data: dataGet,
                cache: false,
                processData: false,
                contentType: false,
                beforeSend: function() {
                     $('.scene-take-container').append(`<div id="loading_animation" class="loading_animation_container"></div>`);
                    addLoadingAnimation();
                },
                success: function (data) {
                    if (data.error){
                        toastr.error('エラーコード: ' + data.error);
                        setTimeout(function () {
                            $($('.loader')[0]).hide();
                        }, 200);
                    } else {
                            if(data.type === 'detail-take') {
                                $('#modal-take-scene').find('.take-scene-name').text(`${data.index === '0' ? "ファーストテイク" : 'テイク' +(parseInt(data.index) + 1).toString()}`);
                                $('#modal-take-scene').find('.take-scene-time').text(data.created_date);
                                $('#modal-take-scene').find('.version-container').append(data.html);
                                $('#modal-take-scene').modal('show');
                                $($('.loader')[0]).hide();
                            }
                    }
                },
                fail: function (data) {
                    $($('.loader')[0]).hide();

                    toastr.error('エラーコード: ' + data.error);
                },
                error: function (response) {
                    $($('.loader')[0]).hide();

                    toastr.error('エラーコード:' + response.message);
                },
                complete: function (data) {
                    $('.loading_animation_container').remove();

                }
            })  
        }
    });
}

function getWeekDay(day){
    const days = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];
    return days[day.getDay()];
}

function initDateTimeSchedule() {
    // const date = new Date();
    const newScheduleDate = moment().tz('Asia/Tokyo');
    
    $('#id_scene_schedule_time').datetimepicker({ format: 'HH:mm', ignoreReadonly: true, });
    //old code
    // if (!$('#id_scene_schedule').val()) {
    //     $('#id_scene_schedule').datepicker('setDate', formatDate(newScheduleDate.add(5, 'days')));
    // }
    // if (!$('#id_scene_schedule_time').val()) {
    //     $('#id_scene_schedule_time').val('10:00');
    // }
    // New code: set value to current datetime
    let current_date = $("#id_scene_schedule").val();
    if (!$('#id_scene_schedule').val()) {
        current_date = formatDate(newScheduleDate);
    }
    flatpickr("#id_scene_schedule", {
          mode: "single",
          dateFormat: "Y/m/d",
          defaultDate: formatDate(current_date),
          showMonths: 1,
          onOpen: function (selectedDates, dateStr, instance) {
            $(instance.element)
              .next(".c-icon-date-range")
              .addClass("is-icon-active");
          },
          onClose: function (selectedDates, dateStr, instance) {
            $(instance.element)
              .next(".c-icon-date-range")
              .removeClass("is-icon-active");
          },
            onChange: function (selectedDates, dateStr, instance) {
              let startDate = selectedDates[0];
              let endDate = selectedDates[1];
              $(instance.element).attr(
                "data-start-time",
                moment(startDate).format("yyyy-MM-DD HH:mm:ss")
              );
              if (endDate)
                $(instance.element).attr(
                  "data-end-time",
                  moment(endDate).format("yyyy-MM-DD HH:mm:ss")
                );
            },
          // minDate: "today",
          // maxDate: new Date().fp_incr(120),
        });
    if (!$('#id_scene_schedule_time').val()) {
        $('#id_scene_schedule_time').val(newScheduleDate.format('HH:mm'));
    }

    // else {
    //     $('#id_scene_schedule_time').val($('#id_scene_schedule_time').val().split(' - ')[1]);
    // }

    function changeCountTime() {
        if($('#id_scene_schedule').val() && $('#id_scene_schedule_time').val()){
            const scheduleTime = new Date(`${$('#id_scene_schedule').val()} ${$('#id_scene_schedule_time').val()}`);
            const dateTimeString = new Date().toLocaleString("en-US", {timeZone: "Asia/Tokyo"});
            const currentDateTimeMls = new Date(dateTimeString).getTime();
            const durationTime = scheduleTime.getTime() - currentDateTimeMls;
            const days = parseInt(durationTime / (1000 * 3600 * 24));
            if(days && days > 0) {
                $('#modal-upload-scene .text-count-day .pre-count').text(`残り `)
                $('#modal-upload-scene .text-count-day .count-days').text(`${days}日`)
            } else {
                if(parseInt(durationTime / (1000 * 3600)) > 0) {
                    $('#modal-upload-scene .text-count-day .pre-count').text(`残り `);
                    $('#modal-upload-scene .text-count-day .count-days').text(`1日`);
                } else {
                    $('#modal-upload-scene .text-count-day .count-days').text(`配信済み`);
                    $('#modal-upload-scene .text-count-day .pre-count').text(``);
                }
            }
        }
    }
    changeCountTime();

    $(document).on('change input dp.change', '#id_scene_schedule, #id_scene_schedule_time', function (e) {
        changeCountTime();
    })

    $(document).on('focusout', '#id_scene_schedule_time', function (e) {
        if (!$(this).val()) {
            $(this).val('10:00');
            changeCountTime();
        }
    })
}

function getThumbVideo(_CANVAS_RESIZE, _CTX_RESIZE, _VIDEO) {
    let imgResize = resizeThumb(_VIDEO.videoWidth, _VIDEO.videoHeight);
    _CANVAS_RESIZE.width = imgResize.width;
    _CANVAS_RESIZE.height = imgResize.height;
    _CTX_RESIZE.drawImage(_VIDEO, 0, 0, imgResize.width, imgResize.height);
}

let resizeThumb = function (width, height) {
    let targetWidth = 480;
    if (width <= targetWidth) {
        return {width: width, height: height}
    }

    let ratio = targetWidth / width;

    return {
        width: parseInt(width * ratio),
        height: parseInt(height * ratio)
    }
};

const resizeDownImg = file => new Promise((resolve, reject) => {
    const img = new Image();
    img.src = file;

    img.onload = function resizeImage() {
        const data = resizeThumb(img.width, img.height);
        var newDataUri = imageToDataUri(img, data.width, data.height);
        resolve(newDataUri);
    }
    img.onerror = error => reject(error);
});

function imageToDataUri(img, width, height) {
    var canvas = document.createElement('canvas'),
        ctx = canvas.getContext('2d');
    canvas.width = width;
    canvas.height = height;
    ctx.drawImage(img, 0, 0, width, height);
    return canvas.toDataURL();
}


// function checkDoneUploadFile(index) {
//     progressDom.removeClass('clicked').addClass('success');
// }

function removeCommonAffix(arr) {
    if (arr.length === 0) return arr;

    // プレフィックスを特定
    let prefix = arr[0];
    for (let i = 1; i < arr.length; i++) {
        while (!arr[i].startsWith(prefix) && prefix.length > 0) {
            prefix = prefix.slice(0, -1); // プレフィックスを一文字ずつ短くする
        }
    }

    // サフィックスを特定
    let suffix = arr[0];
    for (let i = 1; i < arr.length; i++) {
        while (!arr[i].endsWith(suffix) && suffix.length > 0) {
            suffix = suffix.slice(1); // サフィックスを一文字ずつ短くする
        }
    }

    // 各要素から共通プレフィックスとサフィックスを削除
    return arr.map(item => item.slice(prefix.length, item.length - suffix.length));
}

function initDragDropScene () {
    let thisModal = $('#modal-upload-scene');
    if (thisModal.find('#create-scene-upload-dropzone').length > 0) {
        var previewNode = thisModal.find('.mattach-template-form');
        var previewTemplate = previewNode.parent().html();
        previewNode.parent().empty();

        $dropzoneScene = new Dropzone('#create-scene-upload-dropzone', {
            maxFilesize: 4500,
            timeout: 900000,
            autoDiscover: false,
            previewsContainer: '.mattach-form-upload-scene .mattach-preview-container-form',
            previewTemplate: previewTemplate,
            url: "/",
            autoProcessQueue: false,
            autoQueue: false,
            clickable: '#create-scene-upload-dropzone',
            acceptedFiles: '.mp4, .MP4, .mov, .MOV, application/pdf, .mp3, .wav, .WAV , ' +
                '.png, .PNG, .jpg, .JPG, .jpeg, .bmp, .gif, .svg',
            // maxFiles: 1,
            dictDefaultMessage: svg_upload +'\n' + '<p>ファイルを選択</p>',
        });

        $dropzoneScene.on("maxfilesexceeded", function (file) {
            $dropzoneScene.removeAllFiles();
            $dropzoneScene.addFile(file);
        });

        $dropzoneScene.on('removedfile', function (file) {
        });

        $dropzoneScene.on('addedfiles', async function (file, e) {
            index = listFile.length;
            listFile.push(index);
            listFileName.push(index);
            thumbnailFile.push(index);
            listWidthFile.push(index);
            listHeightFile.push(index);
            defaultName.push(index);
            let tempFile = [];
            let tempThumb = [];
            let tempName = [];
            let tempWidth = [];
            let tempHeight = [];
            let tempDefaultName = [];

            let countFileDone = 0;
            tempFile.push.apply(tempFile, file);
            tempFile.sort((a, b) => {return b.size-a.size});
            $($('.loader')[0]).show();

            let fileNames = tempFile.map(x => x.name)
            let validationNames = removeCommonAffix(fileNames)

            const mapLoop = async _ => {
                activeProgress();
                let progressDom = $('.upload-button-wrapper');
                progressDom.removeClass('success').addClass('clicked');
                for (indexFile = 0; indexFile < tempFile.length; indexFile++) {
                    if(tempFile[indexFile].status == 'error') continue;
                    tempName[indexFile] = tempFile[indexFile]?.name
                    tempDefaultName[indexFile] = validationNames[indexFile];
                    if (tempFile[indexFile]?.type.split('/')[0] === 'audio') {
                        tempWidth[indexFile] = 0;
                        tempHeight[indexFile] = 0;
                        let tags = await awaitableJsmediatags(tempFile[indexFile]);
                        if(tags) {
                            var image = tags.tags.picture;
                            if (image) {
                                var base64String = '';
                                for (var j = 0; j < image.data.length; j++) {
                                    base64String += String.fromCharCode(image.data[j]);
                                }
                                const format = image.format.trim();
                                const base64Temp = window.btoa(base64String).trim();
                                // var base64 = (`data:${image.format};base64,${window.btoa(base64String)}`);
                                var base64 = `data:${format};base64,${base64Temp}`;
                                tempThumb[indexFile] = base64;
                                if (indexFile === 0) {
                                    $(`.scene-take-video img.count-index-${index}`).attr('src', base64.toString() || '')
                                }
                            } else {
                                tempThumb[indexFile] = '';
                            }
                        } else {
                            tempThumb[indexFile] = '';
                        }

                    } else if(tempFile[indexFile]?.type.split('/')[0] === 'video') {
                        let thumb = '';

                        const video = await awaitableFileReaderGetDementionsVideo(tempFile[indexFile]);
                        if(video) {
                            thumb = await awaitableGetVideoThumb(tempFile[indexFile]);
                        }
                        tempThumb[indexFile] = thumb;
                        if (indexFile === 0) {
                            $(`.scene-take-video img.count-index-${index}`).attr('src', thumb)
                        }
                        tempWidth[indexFile] = video.videoWidth;
                        tempHeight[indexFile] = video.videoHeight;
                    } else if(tempFile[indexFile]?.type.split('/')[0] === 'image') {
                        const image = await awaitableFileReaderGetDementionsImage(tempFile[indexFile]);
                        tempWidth[indexFile] = image.width;
                        tempHeight[indexFile] = image.height;
                        tempThumb[indexFile] = image.base64;
                    } else {
                        tempWidth[indexFile] = 0;
                        tempHeight[indexFile] = 0;
                        tempThumb[indexFile] = '';
                    }
                    countFileDone++;
                }
                await checkAndCallAjax(countFileDone, tempFile);
                setTimeout(function () {
                    removeProgress();
                }, 1000)
            }
            mapLoop();
            listFile[index] = tempFile;
            thumbnailFile[index] = tempThumb;
            listFileName[index] = tempName;
            listWidthFile[index] = tempWidth;
            listHeightFile[index] = tempHeight;
            defaultName[index] = tempDefaultName;
            $(this)[0].removeAllFiles();
            $($('.loader')[0]).hide();
            async function checkAndCallAjax(count, tempArrFile) {
                if(count !== tempArrFile.length) {
                    return;
                }
                $($('.loader')[0]).hide();

                const action = $('#modal-upload-scene').attr('action-type');
                if(action === 'edit'){
                    url = '/top/upload_scene_for_scenetitle';
                    success_message_title = '';
                    success_message = 'アップロード完了';
                    const sceneId = $('#modal-upload-scene').attr('product-scene-id');
                    const productId = $('.project-item.active').data('project-id');
                    const productSceneVal = $('#modal-upload-scene').attr('product-scene-val');
                    const titleId = $('.create-scene__content').attr('data-scene-title-id');
                    const variationValues = tempName.map((n) => {
                        return arrToStringWithDot(n.split('.').slice(0, -1))
                    })

                    let thumbnail = tempThumb;
                    let listWidth = tempWidth;
                    let listHeigh = tempHeight;
                    let name = tempDefaultName;
                    if (!titleId || !productId || !action) {
                        return;
                    }

                    const data = new FormData();
                    for(i=0; i<tempFile.length; i++){
                        data.append(`movies`, tempFile[i]);
                    }
                    thumbnail = thumbnail.join('|');
                    listWidth = listWidth.join('|');
                    listHeigh = listHeigh.join('|');
                    name = name.join('|');
                    data.append('product_scene_id', sceneId);
                    data.append('list_width', listWidth);
                    data.append('list_height', listHeigh);
                    data.append('thumbnail_base_64', thumbnail);
                    data.append('file_names', listFileName);
                    data.append('product_scene_text', productSceneVal);
                    data.append('variation_values', name);
                    data.append('product', productId);
                    data.append('index_variant', $('.scene-take-container').length + 1);
                    data.append('scene_title_id', titleId);
                    data.append('type_upload', 'take-upload');

                    await sendAjax(data, url, success_message, success_message_title, 'upload');
                } else {
                    function renderElement(arr) {
                        strElement = '';
                        arr.map((f, index) => {
                            strElement += `<div class="variant-name-container">
                                            <div class="scene-variant-name">${tempDefaultName[index]}</div>
                                            <div class="scene-variant-file">${f.name}</div>
                                            </div>`
                        })
                        return strElement;
                    }
                    if ($('#modal-upload-scene').attr('action-type') === 'create') {
                        $('.scene-list-take').append(`<div class="scene-take-container" data-scene-id="${index}">
                        <div class="scene-take-video" type="${tempFile[0].type}">
                          <img class="count-index-${index}" src="${tempThumb[tempThumb.length - 1]}" info="${index}"/>
                        </div>
                        <div class="scene-take-namne-container">
                            <div class="scene-take-name-left">
                            <div class="scene-take-name">
                            <div class="scene-take-name-content">${$('.scene-list-take').children().length === 0 ? 'ファーストテイク' : 'テイク' + ($('.scene-list-take').children().length + 1)}</div>
                            <div class="scene-take-time-content">${getWeekDay(new Date())}</div>
                        </div>
                        <div class="scene-take-list-variant">                     
                            ${renderElement(tempFile)}     
                        </div>
                        </div>
                        <div class="scene-take-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M9.2925 6.71002C8.9025 7.10002 8.9025 7.73002 9.2925 8.12002L13.1725 12L9.2925 15.88C8.9025 16.27 8.9025 16.9 9.2925 17.29C9.6825 17.68 10.3125 17.68 10.7025 17.29L15.2925 12.7C15.6825 12.31 15.6825 11.68 15.2925 11.29L10.7025 6.70002C10.3225 6.32002 9.6825 6.32002 9.2925 6.71002Z" fill="#A7A8A9"/>
                            </svg>
                            </div>
                        </div>
                      </div>`);

                        let countIndex = $('.scene-take-container').length + 1;
                        if (countIndex > 1) {
                            $('label[for="create-scene-upload-dropzone"] .contract__form-label').text('テイク' + countIndex + 'を追加');
                        } else {
                            $('label[for="create-scene-upload-dropzone"] .contract__form-label').text('ファーストテイクを追加');
                        }
                        let progressDom = $('.upload-button-wrapper');
                        progressDom.find('.fill .process').css('width', '100%');
                        setTimeout(function () {
                            progressDom.removeClass('clicked').addClass('success');
                            removeProgress();
                        }, 1000);
                    }
                }
            }
        });
    }
}

let takeFileName = '';
let takeDefaultName = '';
let takeHeight = '';
let takeWidth = '';
let takeFile = '';

let arr_order = [];
let fileThumbVersion;
let urlThumbVersion = '';
let changed = false;
let fileReupVersion;
let widthReup=0;
let heightReup=0;

let tempArrFile = [];
let tempArrThumb = [];
let tempArrName = [];
let tempArrWidth = [];
let tempArrHeight = [];
let tempArrDefault = [];

function initDragDropTake () {
    let thisModal = $('#modal-take-scene');
    if (thisModal.find('#version-upload-dropzone').length > 0) {
        var previewNode = thisModal.find('.mattach-template-form');
        var previewTemplate = previewNode.parent().html();
        previewNode.parent().empty();
        const scene_id = thisModal.attr('data-scene-id');

        $dropzoneTake = new Dropzone('#version-upload-dropzone', {
            maxFilesize: 4500,
            timeout: 900000,
            autoDiscover: false,
            previewsContainer: '.mattach-form-upload-scene .mattach-preview-container-form',
            previewTemplate: previewTemplate,
            url: "/",
            autoProcessQueue: false,
            autoQueue: false,
            clickable: '#version-upload-dropzone',
            acceptedFiles: '.mp4, .MP4, .mov, .MOV, application/pdf, .mp3, .wav'
                + '.png, .PNG, .jpg, .JPG, .jpeg, .bmp, .gif, .svg',
            // maxFiles: 1,
            dictDefaultMessage: svg_upload +'\n' + '<p>ファイルを選択</p>',
        });

        $dropzoneTake.on("maxfilesexceeded", function (file) {
            $dropzoneTake.removeAllFiles();
            $dropzoneTake.addFile(file);
        });

        $dropzoneTake.on('removedfile', function (file) {
        });
    }

    $dropzoneTake.on('addedfiles', async function (file, e) {
            let tempFile = [];
            let tempThumb = [];
            let tempName = [];
            let tempWidth = [];
            let tempHeight = [];
            let tempDefaultName = [];

            let indexNewFile = $('.version-item').length;
            let countFileDone = 0;

            const action = thisModal.attr('action-type');
            const scene_id = thisModal.attr('data-scene-id');
            tempFile.push.apply(tempFile, file);
            tempFile.sort((a, b) => {return b.size-a.size});

            if (action === 'create') {
                arrFile = listFile[scene_id];
                arrThumb = thumbnailFile[scene_id];
                arrName = listFileName[scene_id];
                arrWidth = listWidthFile[scene_id];
                arrHeight = listHeightFile[scene_id];
                arrDefault = defaultName[scene_id];
                let lastDefaultIndex = indexNewFile > 0 ? Math.max(...$('.version-real-name').map(function()  {
                        return $(this).text().charCodeAt(0) - 65;
                    }).get()) : null;
                const mapLoop = async _ => {
                    activeProgress();
                    let progressDom = $('.upload-button-wrapper');
                    progressDom.removeClass('success').addClass('clicked');
                    for (let index = 0; index < tempFile.length; index++) {
                        if(tempFile[index].status === 'error') continue;
                        let newDefaultName = String.fromCharCode((lastDefaultIndex != null ? lastDefaultIndex+1 : 0 ) + index + 65);
                        let videoWidth = 0;
                        let videoHeight = 0;
                        let base64Img = '';
            
                        if (tempFile[index]?.type.split('/')[0] === 'audio') {
                            videoHeight = 0;
                            videoWidth = 0;
                            let tags = await awaitableJsmediatags(tempFile[index]);
                            if (tags) {
                                var image = tags.tags.picture;
                                if (image) {
                                    var base64String = '';
                                    for (var j = 0; j < image.data.length; j++) {
                                        base64String += String.fromCharCode(image.data[j]);
                                    }
                                    var base64 = (`data:${image.format};base64,${window.btoa(base64String)}`);
                                    $(`.version-item[data-scene-id=${indexNewFile+index}] img`).attr('src', base64.toString());
                                    base64Img = base64;
                                    if (!arrThumb.length === indexNewFile+index) {
                                        arrThumb.push(base64Img);
                                    } else {
                                        arrThumb[indexNewFile+index] = base64Img;
                                    }
                                    thumbnailFile[scene_id] = arrThumb;
                                } else {
                                    base64Img = '';
                                }
                            } else {
                                base64Img = ''
                            }

                        } else if (tempFile[index]?.type.split('/')[0] === 'video') {
                            const video = await awaitableFileReaderGetDementionsVideo(tempFile[index]);
                            if (video) {
                                videoWidth = video.videoWidth;
                                videoHeight = video.videoHeight;
                                 base64Img = await awaitableGetVideoThumb(tempFile[index]);
                                $(`.version-item[data-scene-id=${indexNewFile+index}] img`).attr('src', base64Img.toString());
                                if (arrThumb.length === indexNewFile+index) {
                                    arrThumb.push(base64Img);
                                } else {
                                    arrThumb[indexNewFile + index] = base64Img;
                                }
                            }
                        } else if (tempFile[index]?.type.split('/')[0] === 'image') {
                            const image = await awaitableFileReaderGetDementionsImage(tempFile[index]);
                            videoWidth = image.width;
                            videoHeight = image.height;
                            base64Img = image.base64;
                            $(`.version-item[data-scene-id=${indexNewFile+index}] img`).attr('src', base64Img.toString());
                            if (arrThumb.length === indexNewFile+index) {
                                arrThumb.push(base64Img);
                            } else {
                                arrThumb[indexNewFile + index] = base64Img;
                            }
                        } else {
                            videoWidth = 0;
                            videoHeight = 0;
                            base64Img = '';
                        }
                        arrFile.push(tempFile[index]);
                        arrDefault.push(newDefaultName);
                        arrName.push(tempFile[index].name);
                        arrWidth.push(videoWidth);
                        arrHeight.push(videoHeight);
                    }
                    progressDom.find('.fill .process').css('width', '100%');
                    setTimeout(function () {
                        progressDom.removeClass('clicked').addClass('success');
                        removeProgress();
                    }, 1000);
                    // const targetDom = $(`.scene-take-container[data-scene-id=${scene_id}]`);
                    // targetDom.find('.scene-take-video img').attr('src', base64Img);
                    
                }

                await   mapLoop();

                listFile[scene_id] = arrFile
                listFileName[scene_id] = arrName
                listWidthFile[scene_id] = arrWidth
                listHeightFile[scene_id] = arrHeight
                thumbnailFile[scene_id] = arrThumb
                defaultName[scene_id] = arrDefault
                thumbnailFile[scene_id] = arrThumb;

                const targetDom = $(`.scene-take-container[data-scene-id=${scene_id}]`);
                targetDom.find('.scene-take-list-variant').empty();
                for (let i = 0; i < listFileName[scene_id].length; i++) {
                    targetDom.find('.scene-take-list-variant').append(`
                <div class="variant-name-container" data-scene-id="${i}">
                <div class="scene-variant-name">${defaultName[scene_id][i]}</div>
                <div class="scene-variant-file">${listFileName[scene_id][i]}</div>
                </div>`)
                }

                for (let i = indexNewFile; i < listFileName[scene_id].length; i++) {
                    $('#modal-take-scene .version-container').append(`<div class="version-item" data-scene-id="${i}" data-type="${listFileName[scene_id][i]}" style="">
                <img src="${thumbnailFile[scene_id][i]}">
                <div class="content-container">
                  <div class="version-real-name">${defaultName[scene_id][i]}</div>
                  <div class="version-file-name">${listFileName[scene_id][i]}</div>
                </div>
                <div class="version-action-container">
                  <div class="version-action__button-container">
                    <span class="member-item__btn-move ui-sortable-handle">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <g clip-path="url(#clip0_20694_7610)">
                          <path d="M19 9H5C4.45 9 4 9.45 4 10C4 10.55 4.45 11 5 11H19C19.55 11 20 10.55 20 10C20 9.45 19.55 9 19 9ZM5 15H19C19.55 15 20 14.55 20 14C20 13.45 19.55 13 19 13H5C4.45 13 4 13.45 4 14C4 14.55 4.45 15 5 15Z" fill="#A7A8A9"></path>
                        </g>
                        <defs>
                          <clipPath id="clip0_20694_7610">
                            <rect width="24" height="24" fill="white"></rect>
                          </clipPath>
                        </defs>
                      </svg>
                    </span>
                    <span class="member-item__btn-delete">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <g clip-path="url(#clip0_20694_7613)">
                          <path d="M6 19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V9C18 7.9 17.1 7 16 7H8C6.9 7 6 7.9 6 9V19ZM18 4H15.5L14.79 3.29C14.61 3.11 14.35 3 14.09 3H9.91C9.65 3 9.39 3.11 9.21 3.29L8.5 4H6C5.45 4 5 4.45 5 5C5 5.55 5.45 6 6 6H18C18.55 6 19 5.55 19 5C19 4.45 18.55 4 18 4Z" fill="#A7A8A9"></path>
                        </g>
                        <defs>
                          <clipPath id="clip0_20694_7613">
                            <rect width="24" height="24" fill="white"></rect>
                          </clipPath>
                        </defs>
                      </svg>
                      </span>
                  </div>
                </div>
              </div>`);
                }
              $dropzoneTake.removeAllFiles();
            } else {
            const mapLoop = async _ => {
                activeProgress();
                let progressDom = $('.upload-button-wrapper');
                progressDom.removeClass('success').addClass('clicked');
                let lastDefaultIndex = indexNewFile > 0 ? Math.max(...$('.version-real-name').map(function () {
                    return $(this).text().charCodeAt(0) - 65;
                }).get()) : null;
                for (indexFile = 0; indexFile < tempFile.length; indexFile++) {
                    if (tempFile[indexFile].status === 'error') continue;
                    tempName[indexFile] = tempFile[indexFile]?.name
                    tempDefaultName[indexFile] = String.fromCharCode((lastDefaultIndex != null ? lastDefaultIndex+1 : 0 ) + indexFile + 65);
                    if (tempFile[indexFile]?.type.split('/')[0] === 'audio') {
                        tempWidth[indexFile] = 0;
                        tempHeight[indexFile] = 0;
                        let tags = await awaitableJsmediatags(tempFile[indexFile]);
                        if(tags) {
                            var image = tags.tags.picture;
                            if (image) {
                                var base64String = '';
                                for (var j = 0; j < image.data.length; j++) {
                                    base64String += String.fromCharCode(image.data[j]);
                                }
                                const format = image.format.trim();
                                const base64Temp = window.btoa(base64String).trim();
                                // var base64 = (`data:${image.format};base64,${window.btoa(base64String)}`);
                                var base64 = `data:${format};base64,${base64Temp}`;
                                tempThumb[indexFile] = base64
                            } else {
                                 tempThumb[indexFile] = '';
                            }
                        } else {
                            tempThumb[indexFile] = '';
                        }

                    } else if(tempFile[indexFile]?.type.split('/')[0] === 'video') {
                        let thumb = '';
                        const video = await awaitableFileReaderGetDementionsVideo(tempFile[indexFile]);
                        if(video) {
                            thumb = await awaitableGetVideoThumb(tempFile[indexFile]);
                        }
                        tempThumb[indexFile] = thumb;
                        tempWidth[indexFile] = video.videoWidth;
                        tempHeight[indexFile] = video.videoHeight;
                    } else if (tempFile[indexFile]?.type.split('/')[0] === 'image') {
                        const image = await awaitableFileReaderGetDementionsImage(tempFile[indexFile]);
                        tempThumb[indexFile] = image.base64;
                        tempWidth[indexFile] = image.width;
                        tempHeight[indexFile] = image.height;
                    } else {
                        tempWidth[indexFile] = 0;
                        tempHeight[indexFile] = 0;
                        tempThumb[indexFile] = '';
                    }
                    countFileDone++;
                }
                await checkAndCallAjax(countFileDone, tempFile);
                setTimeout(function () {
                    removeProgress();
                }, 1000)
            }
            mapLoop();
            $(this)[0].removeAllFiles();
            async  function checkAndCallAjax(count, tempArrFile) {
                if (count !== tempArrFile.length) {
                    return;
                }

                url = '/top/upload_scene_for_scenetitle';
                success_message_title = '';
                success_message = 'アップロード完了';
                const sceneId = $('#modal-take-scene').attr('data-scene-id');
                const prdSceneId = $('#modal-upload-scene').attr('product-scene-id');
                const productId = $('.project-item.active').data('project-id');
                const productSceneVal = $('#modal-upload-scene').attr('product-scene-val');
                const titleId = $('.create-scene__content').attr('data-scene-title-id');
                const variationValues = tempName.map((n) => {
                    return arrToStringWithDot(n.split('.').slice(0, -1))
                })

                let thumbnail = tempThumb;
                let listWidth = tempWidth;
                let listHeigh = tempHeight;
                let name = tempDefaultName;
                if (!titleId || !productId || !action) {
                    return;
                }

                const data = new FormData();
                for (i = 0; i < tempFile.length; i++) {
                    data.append(`movies`, tempFile[i]);
                }
                thumbnail = thumbnail.join('|');
                listWidth = listWidth.join('|');
                listHeigh = listHeigh.join('|');
                name = name.join('|');
                data.append('variation_id', sceneId);
                data.append('product_scene_id', prdSceneId);
                data.append('list_width', listWidth);
                data.append('list_height', listHeigh);
                data.append('thumbnail_base_64', thumbnail);
                data.append('file_names', tempName);
                data.append('product_scene_text', productSceneVal);
                data.append('variation_values', name);
                data.append('product', productId);
                data.append('scene_title_id', titleId);
                data.append('type_upload', 'version-upload');

                await sendAjax(data, url, success_message, success_message_title, 'upload');

            }
            }
        });
}


function initSortableVersion() {
    $('#modal-take-scene .version-container').sortable({
        containment: "document",
        items: "> div, > a",
        handle: 'span.member-item__btn-move',
        cursor: "move",
        axis: 'y',
        tolerance: "pointer",
        start: function (event, ui) {
            ui.placeholder.height(ui.helper.outerHeight());
        },
        change: function (event, ui) {
        },
        update: function (event, ui) {
            arr_order=[];
            $('#modal-take-scene').find('.version-item').each(function (index, item) {
                arr_order.push(item.getAttribute('data-scene-id'));
            });
            const scene_id = $('#modal-take-scene').attr('data-scene-id');
            const action = $('#modal-take-scene').attr('action-type');
            if (action === 'create') {
                arrFile = listFile[scene_id];
                arrThumb = thumbnailFile[scene_id];
                arrName = listFileName[scene_id];
                arrWidth = listWidthFile[scene_id];
                arrHeight = listHeightFile[scene_id];
                arrDefault = defaultName[scene_id];

                tempArrFile = [];
                tempArrThumb = [];
                tempArrName = [];
                tempArrWidth = [];
                tempArrHeight = [];
                tempArrDefault = [];

                for (i = 0; i < arr_order.length; i++) {
                    tempArrFile.push(arrFile[parseInt(arr_order[i])]);
                    tempArrThumb.push(arrThumb[parseInt(arr_order[i])]);
                    tempArrName.push(arrName[parseInt(arr_order[i])]);
                    tempArrWidth.push(arrWidth[parseInt(arr_order[i])]);
                    tempArrHeight.push(arrHeight[parseInt(arr_order[i])]);
                    tempArrDefault.push(arrDefault[parseInt(arr_order[i])]);
                }

                listFile[scene_id] = tempArrFile;
                thumbnailFile[scene_id] = tempArrThumb;
                listFileName[scene_id] = tempArrName;
                listWidthFile[scene_id] = tempArrWidth;
                listHeightFile[scene_id] = tempArrHeight;
                defaultName[scene_id] = tempArrDefault;

                arr_order = [];

                $('.version-container .version-item').each(function(idx) {
                        $(this).attr('data-scene-id', idx);
                });

                const targetDom = $(`.scene-take-container[data-scene-id=${scene_id}]`);
                targetDom.find('.scene-take-list-variant').empty();
                for (i = 0; i < listFileName[scene_id].length; i++) {
                    targetDom.find('.scene-take-list-variant').append(`
                <div class="variant-name-container" data-scene-id="${i}">
                <div class="scene-variant-name">${defaultName[scene_id][i]}</div>
                <div class="scene-variant-file">${listFileName[scene_id][i]}</div>
                </div>`)
                }

                targetDom.find('img').attr('src', thumbnailFile[scene_id][0]);
            } else {
                let position = [];
                let arr_id = [];
                $('#modal-take-scene').find('.version-item').each(function (index, item) {
                    position.push(index + 1);
                    arr_id.push(item.getAttribute('data-scene-id'));
                });
                
                console.log('data', arr_order, position);
                const data = new FormData();
                data.append('list_id_dict', arr_order);
                data.append('position_order_dict', position);
                data.append('data_scene_id', scene_id);
                $.ajax({
                    type: "POST",
                    url: "/top/update_order_version",
                    data: data,
                    cache: false,
                    processData: false,
                    contentType: false,
                    beforeSend: function() {
                        $($('.loader')[0]).show();
                    },
                    success: function (data) {
                        $($('.loader')[0]).hide();
                        if (data.scene_title_root_id) {
                            $('#modal-upload-scene').attr('data-scene-id', data.scene_title_root_id)
                            $(`.pd-section .cvideo  .project-delivery-item-content[data-scene-id="${data.scene_id}"]`).each((index, item) => { item.setAttribute('data-scene-id', data.scene_title_root_id) })
                        }
                        if(data.scene_root_id){
                            $('#modal-take-scene').attr('data-scene-id', data.scene_root_id)
                        }
                    },
                    fail: function (data) {
                        $($('.loader')[0]).hide();
                        toastr.error("エラーが発生しました", "動画削除");
                    },
                    complete:function(data){
                        $($('.loader')[0]).hide();
                    }
                }) 
            }
        },
    });

    $(document).on('click', '#modal-take-scene .member-item__btn-delete', function(e){
        e.stopPropagation();
        e.preventDefault();
        const $this = $(this);
        bootbox.confirm({
            message: "本当に削除しますか？",
            buttons: {
                confirm: {
                    label: 'はい',
                    className: 'btn-danger btn-delete-message'
                },
                cancel: {
                    label: 'いいえ',
                    className: 'btn-light btn-cancel-message'
                }
            },
            callback: function (result) {
                if (result) {
                    $('.project-item__content').append(`<div id="loading_animation" class="loading_animation_container"></div>`);
                    addLoadingAnimation();
                        const action = $('#modal-take-scene').attr('action-type');
                    if(action === 'create'){
                        const scene_id = $('#modal-take-scene').attr('data-scene-id');
                        arrFile = listFile[scene_id];
                        arrThumb = thumbnailFile[scene_id];
                        arrName = listFileName[scene_id];
                        arrWidth = listWidthFile[scene_id];
                        arrHeight = listHeightFile[scene_id];
                        arrDefault = defaultName[scene_id];
                        index = $this.index('.member-item__btn-delete');
                        arrFile.splice(index, 1);
                        arrThumb.splice(index, 1);
                        arrName.splice(index, 1);
                        arrWidth.splice(index, 1);
                        arrHeight.splice(index, 1);
                        arrDefault.splice(index, 1);

                        $this.parents('.version-item').remove();
                        
                        listFile[scene_id] = arrFile;
                        thumbnailFile[scene_id] = arrThumb;
                        listFileName[scene_id] = arrName;
                        listWidthFile[scene_id] = arrWidth;
                        listHeightFile[scene_id] = arrHeight;
                        defaultName[scene_id] = arrDefault;

                        const targetDom = $(`.scene-take-container[data-scene-id=${scene_id}]`);
                        if($('#modal-take-scene .version-item').length) {
                            targetDom.find('.scene-take-list-variant').empty();
                            for (i = 0; i < listFileName[scene_id].length; i++) {
                                targetDom.find('.scene-take-list-variant').append(`
                            <div class="variant-name-container" data-scene-id="${i}">
                            <div class="scene-variant-name">${defaultName[scene_id][i]}</div>
                            <div class="scene-variant-file">${listFileName[scene_id][i]}</div>
                            </div>`)
                            }
                            targetDom.find('img').attr('src', thumbnailFile[scene_id][0]);
                        } else {
                            $('#modal-take-scene').modal('hide')
                        }

                        $('.loading_animation_container').remove();
                    } else {
                        const scene_id = $this.closest('.version-item').attr('data-scene-id');
                        $.ajax({
                            type: "POST",
                            url: "/top/delete_scene",
                            data: {
                                'scene_id': scene_id
                            },
                            datatype: "json",
                            success: function (data) {
                                $('.loading_animation_container').remove();
                                if(data.is_root_scene) {
                                   $('#modal-take-scene').attr('data-scene-id', data.new_root_id) 
                                   $('#modal-upload-scene').attr('data-scene-id', data.new_root_id)
                                   $(`.pd-section .cvideo  .project-delivery-item-content[data-scene-id="${data.scene_id}"]`).each((index, item) => { item.setAttribute('data-scene-id', data.new_root_id) })
                                }
                                if(data.is_deleted_take || data.is_deleted) {
                                    $('#modal-take-scene').attr('data-scene-id', data.new_root_id) 
                                    $('#modal-upload-scene').attr('data-scene-id', data.new_root_id)
                                    $(`.pd-section .cvideo  .project-delivery-item-content[data-scene-id="${data.scene_id}"]`).each((index, item) => { item.setAttribute('data-scene-id', data.new_root_id) })
                                    $('#modal-take-scene').modal('hide');
                                    initDragDropTake();
                                    initDateTimeSchedule();
                                    return
                                }
                                
                                $this.closest('.version-item').remove();
                            },
                            fail: function (data) {
                                $('.loading_animation_container').remove();
                                toastr.error("エラーが発生しました", "動画削除");
                            },
                            complete:function(data){
                                $('.loading_animation_container').remove();
                            }
                        })
                    }
                }
            }
        });
    })

    $(document).on('click', '#modal-take-scene .version-item .content-container, #modal-take-scene .version-item img', function(e){
        const action = $('#modal-take-scene').attr('action-type');
        let scene_id1 = $('#modal-take-scene').attr('data-scene-id');
        let scene_id2 = $(this).parent().attr('data-scene-id');
        const thumbModal = $('#modal-scene-thumbnail');
        if(action === 'create') {
            thumbModal.attr('action-type', 'create');
            thumbModal.attr('data-scene-id2', scene_id2);
            thumbModal.attr('data-scene-id1', scene_id1);

            thumbModal.find('.component-tab-container.segment .tab-pane img').attr('src', thumbnailFile[scene_id1][scene_id2]);
            thumbModal.find('.thumbnail-img-container-1 img').attr('src', thumbnailFile[scene_id1][scene_id2]);
            
            if(listFile[scene_id1][scene_id2]?.type.split('/')[0]==='video'){
                if($('#modal-scene-thumbnail .component-tab-container.segment').hasClass('hide')){
                    $('#modal-scene-thumbnail .component-tab-container.segment').removeClass('hide');
                }
                if(!$('#modal-scene-thumbnail .thumbnail-img-container-1').hasClass('hide')) {
                    $('#modal-scene-thumbnail .thumbnail-img-container-1').addClass('hide');
                }
                $('#modal-scene-thumbnail .thumbnail-video-container').empty();
                const url = (URL || webkitURL).createObjectURL(listFile[scene_id1][scene_id2]);
                $('#modal-scene-thumbnail .thumbnail-video-container').append(`<video id="video_thumb" preload="auto" controls="">
                    <source src="${url}">
                </video>`);
                urlThumbVersion = thumbnailFile[scene_id1][scene_id2];
                fileThumbVersion = thumbnailFile[scene_id1][scene_id2];
                let _VIDEO_1 = thumbModal.find('.component-tab-container.segment .tab-pane #video_thumb')[0]
                _VIDEO_1.load();
                $(_VIDEO_1).on('canplay', function () {
                    $(_VIDEO_1).off('canplay');

                    const _canvas_1 = document.createElement("canvas");
                    const _ctx = _canvas_1.getContext('2d');
                    _canvas_1.width = _VIDEO_1.videoWidth;
                    _canvas_1.height = _VIDEO_1.videoHeight;
                    getThumbVideo(_canvas_1, _ctx, _VIDEO_1);
                   
                    thumbModal.find('.component-tab-container.segment .tab-pane img').attr('src', urlThumbVersion);
                    thumbModal.find('.thumbnail-img-container-1 img').attr('src', urlThumbVersion);
                    $(_VIDEO_1).on('seeked', function () {
                        getThumbVideo(_canvas_1, _ctx, _VIDEO_1);
                        fileThumbVersion = _canvas_1.toDataURL();
                    });
                });

            } else {
                if(!$('#modal-scene-thumbnail .component-tab-container.segment').hasClass('hide')){
                    $('#modal-scene-thumbnail .component-tab-container.segment').addClass('hide');
                }
                if($('#modal-scene-thumbnail .thumbnail-img-container-1').hasClass('hide')) {
                    $('#modal-scene-thumbnail .thumbnail-img-container-1').removeClass('hide');
                }
                urlThumbVersion = thumbnailFile[scene_id1][scene_id2];
                fileThumbVersion = thumbnailFile[scene_id1][scene_id2];
                thumbModal.find('.component-tab-container.segment .tab-pane #video_thumb source')[0].src = '';
                thumbModal.find('.component-tab-container.segment .tab-pane #video_thumb source')[0].type = '';
            }
            thumbModal.find('#id_version_name').val(defaultName[scene_id1][scene_id2]);
            thumbModal.find('.version-name').text(listFileName[scene_id1][scene_id2]);            
            thumbModal.modal('show');
        } else {
            thumbModal.attr('data-scene-id', scene_id2);
            thumbModal.attr('action-type', 'edit');
            const url = '/top/get_scene_detail_edit';
            const type = 'detail-thumb';
            const dataGet = new FormData();
            dataGet.append('scene_id', scene_id2);
            dataGet.append('type_data', type);
            changed = false;
            $.ajax({
                type: "POST",
                url: url,
                data: dataGet,
                cache: false,
                processData: false,
                contentType: false,
                beforeSend: function() {
                    $($('.loader')[0]).show();
                },
                success: function (data) {
                    if (data.error){
                        toastr.error('エラーコード: ' + data.error);
                        setTimeout(function () {
                            $($('.loader')[0]).hide();
                        }, 200);
                    } else {
                        thumbModal.find('.component-tab-container.segment .tab-pane img').attr('src', data.thumbnail);
                        thumbModal.find('.thumbnail-img-container-1 img').attr('src', data.thumbnail);
                        thumbModal.find('#id_version_name').val(data.real_name);
                        thumbModal.find('.version-name').text(data.file_name);

                        if (data.file_type === 'video') {
                            if ($('#modal-scene-thumbnail .component-tab-container.segment').hasClass('hide')) {
                                $('#modal-scene-thumbnail .component-tab-container.segment').removeClass('hide');
                            }
                            if (!$('#modal-scene-thumbnail .thumbnail-img-container-1').hasClass('hide')) {
                                $('#modal-scene-thumbnail .thumbnail-img-container-1').addClass('hide');
                            }
                            $('#modal-scene-thumbnail .thumbnail-video-container').empty();
                            $('#modal-scene-thumbnail .thumbnail-video-container').append(`<video id="video_thumb" preload="auto" controls=""
                            crossorigin="anonymous">
                                        <source src="${data.video_url}">
                                    </video>`);

                            let _VIDEO_1 = thumbModal.find('.component-tab-container.segment .tab-pane #video_thumb')[0]
                            _VIDEO_1.load();
                            $(_VIDEO_1).on('canplay', function () {
                                $(_VIDEO_1).off('canplay');
                                // _VIDEO_1.load();
                                const _canvas_1 = document.createElement("canvas");
                                const _ctx = _canvas_1.getContext('2d');
                                _canvas_1.width = _VIDEO_1.videoWidth;
                                _canvas_1.height = _VIDEO_1.videoHeight;
                                // getThumbVideo(_canvas_1, _ctx, _VIDEO_1);
                                // fileThumbVersion = _canvas_1.toDataURL();
                                // urlThumbVersion = _canvas_1.toDataURL();
                                $(_VIDEO_1).on('seeked', function () {
                                    getThumbVideo(_canvas_1, _ctx, _VIDEO_1);
                                    fileThumbVersion = _canvas_1.toDataURL("image/png");
                                    changed = true;
                                });
                            });
                        } else {
                            if (!$('#modal-scene-thumbnail .component-tab-container.segment').hasClass('hide')) {
                                $('#modal-scene-thumbnail .component-tab-container.segment').addClass('hide');
                            }
                            if ($('#modal-scene-thumbnail .thumbnail-img-container-1').hasClass('hide')) {
                                $('#modal-scene-thumbnail .thumbnail-img-container-1').removeClass('hide');
                            }
                            thumbModal.find('.component-tab-container.segment .tab-pane #video_thumb source')[0].src = '';
                            thumbModal.find('.component-tab-container.segment .tab-pane #video_thumb source')[0].type = '';
                        }
                        thumbModal.modal('show');
                        $($('.loader')[0]).hide();

                    }
                },
                fail: function (data) {
                    $($('.loader')[0]).hide();

                    toastr.error('エラーコード: ' + data.error);
                },
                error: function (response) {
                    $($('.loader')[0]).hide();

                    toastr.error('エラーコード:' + response.message);
                },
                complete: function (data) {
                    $($('.loader')[0]).hide();

                }
            })
        }

    })
}


function submitModalVersion() {
    $(document).on('click', '.btn-confirm-take-scene', function(){
        arrFile = [];
        arrThumb = [];
        arrName = [];
        arrWidth = [];
        arrHeight = [];
        arrDefault = [];
        $dropzoneTake.removeAllFiles();
    })
}

function removeDataModal(){
    $(document).on('click', '#modal-upload-scene .btn-popup-close', function() {
        const action = $('#modal-upload-scene').attr('action-type');
        if(action === 'edit' && window.location.pathname.includes('/scene/')) {
            let chapter_id = null
            let chapter_active = $('.filter-item-project.item-chapter-active');
            let searchParams = new URLSearchParams(window.location.search);
            let data_ps_id = $('.txt-des-above').attr('data-product-scene-id')
            if (chapter_active.length > 0) {
                chapter_id = chapter_active.attr('data-ps-id');
            } else if (searchParams.has('chapter_id')) {
                chapter_id = searchParams.get('chapter_id');
            } else if (data_ps_id) {
                chapter_id = data_ps_id;
            }
            if (chapter_id) {
                window.location.href = window.location.origin  + window.location.pathname + `?chapter_id=${chapter_id}`
            } else {
                window.location.reload()
            }
        }
    });

    $('#modal-upload-scene').on('hidden.bs.modal', function(e){
        $('#id_scene_name').val('');
        $dropzoneScene.removeAllFiles();
        $(this).find('.scene-list-take').empty();
        $('#id_scene_schedule').val('');
        $('#id_scene_schedule_time').val('');
        listFile = [];
        thumbnailFile = [];
        listFileName = [];
        listHeightFile = [];
        listWidthFile = [];
        defaultName = [];
        $('label[for="create-scene-upload-dropzone"] .contract__form-label').text('ファーストテイクを追加');
    });
    
    $('#modal-scene-thumbnail').on('hidden.bs.modal', function(e){
        fileThumbVersion='';
        urlThumbVersion='';
        fileReupVersion='';
        changed = false;
        widthReup=0;
        heightReup=0;
        $(this).find('.component-tab-container.segment .tab-pane img').attr('src', urlThumbVersion);
        $(this).find('.thumbnail-img-container-1 img').attr('src', urlThumbVersion);
        URL.revokeObjectURL($(this).find('.component-tab-container.segment .tab-pane #video_thumb')[0].src)
        $(this).find('.component-tab-container.segment .tab-pane #video_thumb')[0].src = '';
        $(this).find('.component-tab-container.segment .tab-pane #video_thumb')[0].type = '';
    });
}

const toBase64 = file => new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
});

function actionUploadModalThumb() {
    const thumbModal = $('#modal-scene-thumbnail');
    $(document).on('click', '.component-tab-container.segment .thumbnail-img-container, .thumbnail-img-container-1', function(e) {
        e.stopPropagation();
        e.preventDefault();
        $('#upload-image-thumbnail').trigger('click');
    });

    $(document).on('click', '#modal-scene-thumbnail .btn-reupload', function(e) {
        e.stopPropagation();
        e.preventDefault();
        $('#reupload-file-version').trigger('click');
    });

    $(document).on('change', '#upload-image-thumbnail', async function(e) {
        const file = $(this).prop('files')[0];
        console.log("check ", file);
        // resizeThumb();
        const base64 = await toBase64(file) || '';
        urlThumbVersion = await resizeDownImg(base64);
        thumbModal.find('.component-tab-container.segment .tab-pane img').attr('src', base64);
        thumbModal.find('.thumbnail-img-container-1 img').attr('src', base64);
        $(this)[0].value = null;
        changed = true;
    });

    $(document).on('change', '#id_version_name', async function(e) {
        changed = true;
    });

    $(document).on('change', '#reupload-file-version', function(e) {
        fileReupVersion = $('#reupload-file-version').prop('files')[0];
        let scene_id1 = $('#modal-take-scene').attr('data-scene-id');
        let scene_id2 = $(this).parent().attr('data-scene-id');
        changed = true;
        $('#modal-scene-thumbnail .version-name').text(fileReupVersion.name)
        const checkFile = async _  => {
            if (fileReupVersion?.type.split('/')[0] === 'audio') {
                if(!$('#modal-scene-thumbnail .component-tab-container.segment').hasClass('hide')){
                    $('#modal-scene-thumbnail .component-tab-container.segment').addClass('hide');
                }
                if($('#modal-scene-thumbnail .thumbnail-img-container-1').hasClass('hide')) {
                    $('#modal-scene-thumbnail .thumbnail-img-container-1').removeClass('hide');
                }
                widthReup = 0;
                heightReup = 0;
                let tags = await awaitableJsmediatags(fileReupVersion);
                if(tags) {
                    var image = tags.tags.picture;
                    if (image) {
                        var base64String = '';
                        for (var j = 0; j < image.data.length; j++) {
                            base64String += String.fromCharCode(image.data[j]);
                        }
                        const format = image.format.trim();
                        const base64Temp = window.btoa(base64String).trim();
                        // var base64 = (`data:${image.format};base64,${window.btoa(base64String)}`);
                        var base64 = `data:${format};base64,${base64Temp}`;
                        urlThumbVersion = base64 || '';

                        thumbModal.find('.component-tab-container.segment .tab-pane img').attr('src', urlThumbVersion);
                        thumbModal.find('.thumbnail-img-container-1 img').attr('src', urlThumbVersion);
                    } else {
                        urlThumbVersion = '';
                        thumbModal.find('.component-tab-container.segment .tab-pane img').attr('src', urlThumbVersion);
                        thumbModal.find('.thumbnail-img-container-1 img').attr('src', urlThumbVersion);
                    }
                    URL.revokeObjectURL(thumbModal.find('.component-tab-container.segment .tab-pane #video_thumb source')[0].src)
                } else {
                    urlThumbVersion = ''
                    fileThumbVersion = '';
                    thumbModal.find('.component-tab-container.segment .tab-pane img').attr('src', fileThumbVersion);
                    thumbModal.find('.thumbnail-img-container-1 img').attr('src', fileThumbVersion);
                }

            } else if(fileReupVersion?.type.split('/')[0]==='video'){
                URL.revokeObjectURL(thumbModal.find('.component-tab-container.segment .tab-pane #video_thumb source')[0].src)
                if($('#modal-scene-thumbnail .component-tab-container.segment').hasClass('hide')){
                    $('#modal-scene-thumbnail .component-tab-container.segment').removeClass('hide');
                }
                if(!$('#modal-scene-thumbnail .thumbnail-img-container-1').hasClass('hide')) {
                    $('#modal-scene-thumbnail .thumbnail-img-container-1').addClass('hide');
                }

                $('#modal-scene-thumbnail .thumbnail-video-container').empty();
                const url = (URL || webkitURL).createObjectURL(fileReupVersion);
                $('#modal-scene-thumbnail .thumbnail-video-container').append(`<video id="video_thumb" preload="auto" controls="">
                    <source src="${url}">
                </video>`);

                let _VIDEO_1 = thumbModal.find('.component-tab-container.segment .tab-pane #video_thumb')[0]

                _VIDEO_1.load();
                $(_VIDEO_1).on('canplay', function () {
                    $(_VIDEO_1).off('canplay');
                    heightReup = _VIDEO_1.videoHeight;
                    widthReup = _VIDEO_1.videoWidth;
                    const _canvas_1 = document.createElement("canvas");
                    const _ctx = _canvas_1.getContext('2d');
                    _canvas_1.width = _VIDEO_1.videoWidth;
                    _canvas_1.height = _VIDEO_1.videoHeight;
                    getThumbVideo(_canvas_1, _ctx, _VIDEO_1);
                    fileThumbVersion = _canvas_1.toDataURL();
                    urlThumbVersion = _canvas_1.toDataURL();
                    thumbModal.find('.component-tab-container.segment .tab-pane img').attr('src', urlThumbVersion);
                    thumbModal.find('.thumbnail-img-container-1 img').attr('src', urlThumbVersion);
                    $(_VIDEO_1).on('seeked', function () {
                        getThumbVideo(_canvas_1, _ctx, _VIDEO_1);
                        fileThumbVersion = _canvas_1.toDataURL();
                    });
                });
            } else {
                urlThumbVersion = '';
                if(!$('#modal-scene-thumbnail .component-tab-container.segment').hasClass('hide')){
                    $('#modal-scene-thumbnail .component-tab-container.segment').addClass('hide');
                }
                if($('#modal-scene-thumbnail .thumbnail-img-container-1').hasClass('hide')) {
                    $('#modal-scene-thumbnail .thumbnail-img-container-1').removeClass('hide');
                }
                thumbModal.find('.component-tab-container.segment .tab-pane img').attr('src', urlThumbVersion);
                thumbModal.find('.thumbnail-img-container-1 img').attr('src', urlThumbVersion);
                thumbModal.find('.component-tab-container.segment .tab-pane #video_thumb source')[0].src = '';
                thumbModal.find('.component-tab-container.segment .tab-pane #video_thumb source')[0].type = '';
                URL.revokeObjectURL(thumbModal.find('.component-tab-container.segment .tab-pane #video_thumb source')[0].src);
            }
        }
        checkFile();
    });

    $('#modal-scene-thumbnail').on('hide.bs.modal', function () {
        const action = $('#modal-scene-thumbnail').attr('action-type');
        const scene_id1 = $('#modal-scene-thumbnail').attr('data-scene-id1');
        const scene_id2 = $('#modal-scene-thumbnail').attr('data-scene-id2');
        const defaultNameReup = $('#id_version_name').val();

        if (action === 'create') {
            let tempdefaultVersion = defaultName[scene_id1];
            tempdefaultVersion[scene_id2] = defaultNameReup;
            defaultName[scene_id1] = tempdefaultVersion;
            $(`.version-item[data-scene-id="${scene_id2}"] .content-container .version-real-name`).text(defaultName[scene_id1][scene_id2]);
            if (!$('#modal-scene-thumbnail .component-tab-container.segment').hasClass('hide')) {

                if ($('#modal-scene-thumbnail .nav-item.active .nav-link').attr('data-target') === '#video_select') {
                    let tempThumbVersion = thumbnailFile[scene_id1];
                    tempThumbVersion[scene_id2] = fileThumbVersion;
                    thumbnailFile[scene_id1] = tempThumbVersion;
                    $(`.version-item[data-scene-id=${scene_id2}] img`).attr('src', thumbnailFile[scene_id1][scene_id2]);

                    if (fileReupVersion) {
                        let tempFileVersion = listFile[scene_id1];
                        let tempWidthVersion = listWidthFile[scene_id1];
                        let tempHeightVersion = listHeightFile[scene_id1];
                        let tempNameVersion = listFileName[scene_id1];

                        tempFileVersion[scene_id2] = fileReupVersion;
                        tempWidthVersion[scene_id2] = widthReup;
                        tempHeightVersion[scene_id2] = heightReup;
                        tempNameVersion[scene_id2] = fileReupVersion.name;

                        listFile[scene_id1] = tempFileVersion;
                        listWidthFile[scene_id1] = tempWidthVersion;
                        listHeightFile[scene_id1] = tempHeightVersion;
                        listFileName[scene_id1] = tempNameVersion;

                        $(`.version-item[data-scene-id=${scene_id2}]`).attr('data-type', listFile[scene_id1][scene_id2]?.type);
                        $(`.version-item[data-scene-id=${scene_id2}] .content-container .version-file-name`).text(listFileName[scene_id1][scene_id2]);
                    }
                } else {
                    let tempThumbVersion = thumbnailFile[scene_id1];
                    tempThumbVersion[scene_id2] = urlThumbVersion;
                    thumbnailFile[scene_id1] = tempThumbVersion;
                    $(`.version-item[data-scene-id=${scene_id2}] img`).attr('src', thumbnailFile[scene_id1][scene_id2]);
                    if (fileReupVersion) {
                        let tempFileVersion = listFile[scene_id1];
                        let tempWidthVersion = listWidthFile[scene_id1];
                        let tempHeightVersion = listHeightFile[scene_id1];
                        let tempNameVersion = listFileName[scene_id1];

                        tempFileVersion[scene_id2] = fileReupVersion;
                        tempWidthVersion[scene_id2] = widthReup;
                        tempHeightVersion[scene_id2] = heightReup;
                        tempNameVersion[scene_id2] = fileReupVersion.name;

                        listFile[scene_id1] = tempFileVersion;
                        listWidthFile[scene_id1] = tempWidthVersion;
                        listHeightFile[scene_id1] = tempHeightVersion;
                        listFileName[scene_id1] = tempNameVersion;

                        $(`.version-item[data-scene-id=${scene_id2}]`).attr('data-type', listFile[scene_id1][scene_id2]?.type);
                        $(`.version-item[data-scene-id=${scene_id2}] .content-container .version-file-name`).text(listFileName[scene_id1][scene_id2]);
                    }
                }
            } else {
                let tempThumbVersion = thumbnailFile[scene_id1];
                tempThumbVersion[scene_id2] = urlThumbVersion;
                thumbnailFile[scene_id1] = tempThumbVersion;
                $(`.version-item[data-scene-id=${scene_id2}] img`).attr('src', thumbnailFile[scene_id1][scene_id2]);

                if (fileReupVersion) {
                    let tempFileVersion = listFile[scene_id1];
                    let tempWidthVersion = listWidthFile[scene_id1];
                    let tempHeightVersion = listHeightFile[scene_id1];
                    let tempNameVersion = listFileName[scene_id1];

                    tempFileVersion[scene_id2] = fileReupVersion;
                    tempWidthVersion[scene_id2] = widthReup;
                    tempHeightVersion[scene_id2] = heightReup;
                    tempNameVersion[scene_id2] = fileReupVersion.name;

                    listFile[scene_id1] = tempFileVersion;
                    listWidthFile[scene_id1] = tempWidthVersion;
                    listHeightFile[scene_id1] = tempHeightVersion;
                    listFileName[scene_id1] = tempNameVersion;

                    $(`.version-item[data-scene-id=${scene_id2}]`).attr('data-type', listFile[scene_id1][scene_id2]?.type);
                    $(`.version-item[data-scene-id=${scene_id2}] .content-container .version-file-name`).text(listFileName[scene_id1][scene_id2]);
                }
            }

            $(`.scene-take-container[data-scene-id=${scene_id1}] img`).attr('src', thumbnailFile[scene_id1][0]);

        } else {
            if(!changed) {
                return;
            }
            const data = new FormData();
            const sceneId = thumbModal.attr('data-scene-id');
            const name = $('#id_version_name').val();
            data.append('name', name);
            data.append('scene_id', sceneId);
            if (fileReupVersion) {
                data.append('file_reup', fileReupVersion);
                data.append('reup_width', widthReup);
                data.append('reup_height', heightReup);
            }
            if (!$('#modal-scene-thumbnail .component-tab-container.segment').hasClass('hide')) {
                if ($('#modal-scene-thumbnail .nav-item.active .nav-link').attr('data-target') === '#video_select') {
                    data.append('thumbnail', fileThumbVersion)
                } else {
                    data.append('thumbnail', urlThumbVersion)
                }
            } else {
                data.append('thumbnail', urlThumbVersion)
            }
            const success_message_title = '';
            const success_message = 'アップロード完了';
            const url = '/top/edit_version_detail_scene'
            sendAjax(data, url, success_message, success_message_title, '');
        }
        $('#upload-image-thumbnail').val('');
        $('#reupload-file-version').val('');
    });
}
// End new upload - edit scene