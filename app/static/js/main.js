//01EG89H6SS2141VNGDDEHBMV4Q
/* == jquery mousewheel plugin == Version: 3.1.13, License: MIT License (MIT) */
!function(a){"function"==typeof define&&define.amd?define(["jquery"],a):"object"==typeof exports?module.exports=a:a(jQuery)}(function(a){function b(b){var g=b||window.event,h=i.call(arguments,1),j=0,l=0,m=0,n=0,o=0,p=0;if(b=a.event.fix(g),b.type="mousewheel","detail"in g&&(m=-1*g.detail),"wheelDelta"in g&&(m=g.wheelDelta),"wheelDeltaY"in g&&(m=g.wheelDeltaY),"wheelDeltaX"in g&&(l=-1*g.wheelDeltaX),"axis"in g&&g.axis===g.HORIZONTAL_AXIS&&(l=-1*m,m=0),j=0===m?l:m,"deltaY"in g&&(m=-1*g.deltaY,j=m),"deltaX"in g&&(l=g.deltaX,0===m&&(j=-1*l)),0!==m||0!==l){if(1===g.deltaMode){var q=a.data(this,"mousewheel-line-height");j*=q,m*=q,l*=q}else if(2===g.deltaMode){var r=a.data(this,"mousewheel-page-height");j*=r,m*=r,l*=r}if(n=Math.max(Math.abs(m),Math.abs(l)),(!f||f>n)&&(f=n,d(g,n)&&(f/=40)),d(g,n)&&(j/=40,l/=40,m/=40),j=Math[j>=1?"floor":"ceil"](j/f),l=Math[l>=1?"floor":"ceil"](l/f),m=Math[m>=1?"floor":"ceil"](m/f),k.settings.normalizeOffset&&this.getBoundingClientRect){var s=this.getBoundingClientRect();o=b.clientX-s.left,p=b.clientY-s.top}return b.deltaX=l,b.deltaY=m,b.deltaFactor=f,b.offsetX=o,b.offsetY=p,b.deltaMode=0,h.unshift(b,j,l,m),e&&clearTimeout(e),e=setTimeout(c,200),(a.event.dispatch||a.event.handle).apply(this,h)}}function c(){f=null}function d(a,b){return k.settings.adjustOldDeltas&&"mousewheel"===a.type&&b%120===0}var e,f,g=["wheel","mousewheel","DOMMouseScroll","MozMousePixelScroll"],h="onwheel"in document||document.documentMode>=9?["wheel"]:["mousewheel","DomMouseScroll","MozMousePixelScroll"],i=Array.prototype.slice;if(a.event.fixHooks)for(var j=g.length;j;)a.event.fixHooks[g[--j]]=a.event.mouseHooks;var k=a.event.special.mousewheel={version:"3.1.12",setup:function(){if(this.addEventListener)for(var c=h.length;c;)this.addEventListener(h[--c],b,!1);else this.onmousewheel=b;a.data(this,"mousewheel-line-height",k.getLineHeight(this)),a.data(this,"mousewheel-page-height",k.getPageHeight(this))},teardown:function(){if(this.removeEventListener)for(var c=h.length;c;)this.removeEventListener(h[--c],b,!1);else this.onmousewheel=null;a.removeData(this,"mousewheel-line-height"),a.removeData(this,"mousewheel-page-height")},getLineHeight:function(b){var c=a(b),d=c["offsetParent"in a.fn?"offsetParent":"parent"]();return d.length||(d=a("body")),parseInt(d.css("fontSize"),10)||parseInt(c.css("fontSize"),10)||16},getPageHeight:function(b){return a(b).height()},settings:{adjustOldDeltas:!0,normalizeOffset:!0}};a.fn.extend({mousewheel:function(a){return a?this.bind("mousewheel",a):this.trigger("mousewheel")},unmousewheel:function(a){return this.unbind("mousewheel",a)}})});!function(a){"function"==typeof define&&define.amd?define(["jquery"],a):"object"==typeof exports?module.exports=a:a(jQuery)}(function(a){function b(b){var g=b||window.event,h=i.call(arguments,1),j=0,l=0,m=0,n=0,o=0,p=0;if(b=a.event.fix(g),b.type="mousewheel","detail"in g&&(m=-1*g.detail),"wheelDelta"in g&&(m=g.wheelDelta),"wheelDeltaY"in g&&(m=g.wheelDeltaY),"wheelDeltaX"in g&&(l=-1*g.wheelDeltaX),"axis"in g&&g.axis===g.HORIZONTAL_AXIS&&(l=-1*m,m=0),j=0===m?l:m,"deltaY"in g&&(m=-1*g.deltaY,j=m),"deltaX"in g&&(l=g.deltaX,0===m&&(j=-1*l)),0!==m||0!==l){if(1===g.deltaMode){var q=a.data(this,"mousewheel-line-height");j*=q,m*=q,l*=q}else if(2===g.deltaMode){var r=a.data(this,"mousewheel-page-height");j*=r,m*=r,l*=r}if(n=Math.max(Math.abs(m),Math.abs(l)),(!f||f>n)&&(f=n,d(g,n)&&(f/=40)),d(g,n)&&(j/=40,l/=40,m/=40),j=Math[j>=1?"floor":"ceil"](j/f),l=Math[l>=1?"floor":"ceil"](l/f),m=Math[m>=1?"floor":"ceil"](m/f),k.settings.normalizeOffset&&this.getBoundingClientRect){var s=this.getBoundingClientRect();o=b.clientX-s.left,p=b.clientY-s.top}return b.deltaX=l,b.deltaY=m,b.deltaFactor=f,b.offsetX=o,b.offsetY=p,b.deltaMode=0,h.unshift(b,j,l,m),e&&clearTimeout(e),e=setTimeout(c,200),(a.event.dispatch||a.event.handle).apply(this,h)}}function c(){f=null}function d(a,b){return k.settings.adjustOldDeltas&&"mousewheel"===a.type&&b%120===0}var e,f,g=["wheel","mousewheel","DOMMouseScroll","MozMousePixelScroll"],h="onwheel"in document||document.documentMode>=9?["wheel"]:["mousewheel","DomMouseScroll","MozMousePixelScroll"],i=Array.prototype.slice;if(a.event.fixHooks)for(var j=g.length;j;)a.event.fixHooks[g[--j]]=a.event.mouseHooks;var k=a.event.special.mousewheel={version:"3.1.12",setup:function(){if(this.addEventListener)for(var c=h.length;c;)this.addEventListener(h[--c],b,!1);else this.onmousewheel=b;a.data(this,"mousewheel-line-height",k.getLineHeight(this)),a.data(this,"mousewheel-page-height",k.getPageHeight(this))},teardown:function(){if(this.removeEventListener)for(var c=h.length;c;)this.removeEventListener(h[--c],b,!1);else this.onmousewheel=null;a.removeData(this,"mousewheel-line-height"),a.removeData(this,"mousewheel-page-height")},getLineHeight:function(b){var c=a(b),d=c["offsetParent"in a.fn?"offsetParent":"parent"]();return d.length||(d=a("body")),parseInt(d.css("fontSize"),10)||parseInt(c.css("fontSize"),10)||16},getPageHeight:function(b){return a(b).height()},settings:{adjustOldDeltas:!0,normalizeOffset:!0}};a.fn.extend({mousewheel:function(a){return a?this.bind("mousewheel",a):this.trigger("mousewheel")},unmousewheel:function(a){return this.unbind("mousewheel",a)}})});
/* == malihu jquery custom scrollbar plugin == Version: 3.1.5, License: MIT License (MIT) */
!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"undefined"!=typeof module&&module.exports?module.exports=e:e(jQuery,window,document)}(function(e){!function(t){var o="function"==typeof define&&define.amd,a="undefined"!=typeof module&&module.exports,n="https:"==document.location.protocol?"https:":"http:",i="cdnjs.cloudflare.com/ajax/libs/jquery-mousewheel/3.1.13/jquery.mousewheel.min.js";o||(a?require("jquery-mousewheel")(e):e.event.special.mousewheel||e("head").append(decodeURI("%3Cscript src="+n+"//"+i+"%3E%3C/script%3E"))),t()}(function(){var t,o="mCustomScrollbar",a="mCS",n=".mCustomScrollbar",i={setTop:0,setLeft:0,axis:"y",scrollbarPosition:"inside",scrollInertia:950,autoDraggerLength:!0,alwaysShowScrollbar:0,snapOffset:0,mouseWheel:{enable:!0,scrollAmount:"auto",axis:"y",deltaFactor:"auto",disableOver:["select","option","keygen","datalist","textarea"]},scrollButtons:{scrollType:"stepless",scrollAmount:"auto"},keyboard:{enable:!0,scrollType:"stepless",scrollAmount:"auto"},contentTouchScroll:25,documentTouchScroll:!0,advanced:{autoScrollOnFocus:"input,textarea,select,button,datalist,keygen,a[tabindex],area,object,[contenteditable='true']",updateOnContentResize:!0,updateOnImageLoad:"auto",autoUpdateTimeout:60},theme:"light",callbacks:{onTotalScrollOffset:0,onTotalScrollBackOffset:0,alwaysTriggerOffsets:!0}},r=0,l={},s=window.attachEvent&&!window.addEventListener?1:0,c=!1,d=["mCSB_dragger_onDrag","mCSB_scrollTools_onDrag","mCS_img_loaded","mCS_disabled","mCS_destroyed","mCS_no_scrollbar","mCS-autoHide","mCS-dir-rtl","mCS_no_scrollbar_y","mCS_no_scrollbar_x","mCS_y_hidden","mCS_x_hidden","mCSB_draggerContainer","mCSB_buttonUp","mCSB_buttonDown","mCSB_buttonLeft","mCSB_buttonRight"],u={init:function(t){var t=e.extend(!0,{},i,t),o=f.call(this);if(t.live){var s=t.liveSelector||this.selector||n,c=e(s);if("off"===t.live)return void m(s);l[s]=setTimeout(function(){c.mCustomScrollbar(t),"once"===t.live&&c.length&&m(s)},500)}else m(s);return t.setWidth=t.set_width?t.set_width:t.setWidth,t.setHeight=t.set_height?t.set_height:t.setHeight,t.axis=t.horizontalScroll?"x":p(t.axis),t.scrollInertia=t.scrollInertia>0&&t.scrollInertia<17?17:t.scrollInertia,"object"!=typeof t.mouseWheel&&1==t.mouseWheel&&(t.mouseWheel={enable:!0,scrollAmount:"auto",axis:"y",preventDefault:!1,deltaFactor:"auto",normalizeDelta:!1,invert:!1}),t.mouseWheel.scrollAmount=t.mouseWheelPixels?t.mouseWheelPixels:t.mouseWheel.scrollAmount,t.mouseWheel.normalizeDelta=t.advanced.normalizeMouseWheelDelta?t.advanced.normalizeMouseWheelDelta:t.mouseWheel.normalizeDelta,t.scrollButtons.scrollType=g(t.scrollButtons.scrollType),h(t),e(o).each(function(){var o=e(this);if(!o.data(a)){o.data(a,{idx:++r,opt:t,scrollRatio:{y:null,x:null},overflowed:null,contentReset:{y:null,x:null},bindEvents:!1,tweenRunning:!1,sequential:{},langDir:o.css("direction"),cbOffsets:null,trigger:null,poll:{size:{o:0,n:0},img:{o:0,n:0},change:{o:0,n:0}}});var n=o.data(a),i=n.opt,l=o.data("mcs-axis"),s=o.data("mcs-scrollbar-position"),c=o.data("mcs-theme");l&&(i.axis=l),s&&(i.scrollbarPosition=s),c&&(i.theme=c,h(i)),v.call(this),n&&i.callbacks.onCreate&&"function"==typeof i.callbacks.onCreate&&i.callbacks.onCreate.call(this),e("#mCSB_"+n.idx+"_container img:not(."+d[2]+")").addClass(d[2]),u.update.call(null,o)}})},update:function(t,o){var n=t||f.call(this);return e(n).each(function(){var t=e(this);if(t.data(a)){var n=t.data(a),i=n.opt,r=e("#mCSB_"+n.idx+"_container"),l=e("#mCSB_"+n.idx),s=[e("#mCSB_"+n.idx+"_dragger_vertical"),e("#mCSB_"+n.idx+"_dragger_horizontal")];if(!r.length)return;n.tweenRunning&&Q(t),o&&n&&i.callbacks.onBeforeUpdate&&"function"==typeof i.callbacks.onBeforeUpdate&&i.callbacks.onBeforeUpdate.call(this),t.hasClass(d[3])&&t.removeClass(d[3]),t.hasClass(d[4])&&t.removeClass(d[4]),l.css("max-height","none"),l.height()!==t.height()&&l.css("max-height",t.height()),_.call(this),"y"===i.axis||i.advanced.autoExpandHorizontalScroll||r.css("width",x(r)),n.overflowed=y.call(this),M.call(this),i.autoDraggerLength&&S.call(this),b.call(this),T.call(this);var c=[Math.abs(r[0].offsetTop),Math.abs(r[0].offsetLeft)];"x"!==i.axis&&(n.overflowed[0]?s[0].height()>s[0].parent().height()?B.call(this):(G(t,c[0].toString(),{dir:"y",dur:0,overwrite:"none"}),n.contentReset.y=null):(B.call(this),"y"===i.axis?k.call(this):"yx"===i.axis&&n.overflowed[1]&&G(t,c[1].toString(),{dir:"x",dur:0,overwrite:"none"}))),"y"!==i.axis&&(n.overflowed[1]?s[1].width()>s[1].parent().width()?B.call(this):(G(t,c[1].toString(),{dir:"x",dur:0,overwrite:"none"}),n.contentReset.x=null):(B.call(this),"x"===i.axis?k.call(this):"yx"===i.axis&&n.overflowed[0]&&G(t,c[0].toString(),{dir:"y",dur:0,overwrite:"none"}))),o&&n&&(2===o&&i.callbacks.onImageLoad&&"function"==typeof i.callbacks.onImageLoad?i.callbacks.onImageLoad.call(this):3===o&&i.callbacks.onSelectorChange&&"function"==typeof i.callbacks.onSelectorChange?i.callbacks.onSelectorChange.call(this):i.callbacks.onUpdate&&"function"==typeof i.callbacks.onUpdate&&i.callbacks.onUpdate.call(this)),N.call(this)}})},scrollTo:function(t,o){if("undefined"!=typeof t&&null!=t){var n=f.call(this);return e(n).each(function(){var n=e(this);if(n.data(a)){var i=n.data(a),r=i.opt,l={trigger:"external",scrollInertia:r.scrollInertia,scrollEasing:"mcsEaseInOut",moveDragger:!1,timeout:60,callbacks:!0,onStart:!0,onUpdate:!0,onComplete:!0},s=e.extend(!0,{},l,o),c=Y.call(this,t),d=s.scrollInertia>0&&s.scrollInertia<17?17:s.scrollInertia;c[0]=X.call(this,c[0],"y"),c[1]=X.call(this,c[1],"x"),s.moveDragger&&(c[0]*=i.scrollRatio.y,c[1]*=i.scrollRatio.x),s.dur=ne()?0:d,setTimeout(function(){null!==c[0]&&"undefined"!=typeof c[0]&&"x"!==r.axis&&i.overflowed[0]&&(s.dir="y",s.overwrite="all",G(n,c[0].toString(),s)),null!==c[1]&&"undefined"!=typeof c[1]&&"y"!==r.axis&&i.overflowed[1]&&(s.dir="x",s.overwrite="none",G(n,c[1].toString(),s))},s.timeout)}})}},stop:function(){var t=f.call(this);return e(t).each(function(){var t=e(this);t.data(a)&&Q(t)})},disable:function(t){var o=f.call(this);return e(o).each(function(){var o=e(this);if(o.data(a)){o.data(a);N.call(this,"remove"),k.call(this),t&&B.call(this),M.call(this,!0),o.addClass(d[3])}})},destroy:function(){var t=f.call(this);return e(t).each(function(){var n=e(this);if(n.data(a)){var i=n.data(a),r=i.opt,l=e("#mCSB_"+i.idx),s=e("#mCSB_"+i.idx+"_container"),c=e(".mCSB_"+i.idx+"_scrollbar");r.live&&m(r.liveSelector||e(t).selector),N.call(this,"remove"),k.call(this),B.call(this),n.removeData(a),$(this,"mcs"),c.remove(),s.find("img."+d[2]).removeClass(d[2]),l.replaceWith(s.contents()),n.removeClass(o+" _"+a+"_"+i.idx+" "+d[6]+" "+d[7]+" "+d[5]+" "+d[3]).addClass(d[4])}})}},f=function(){return"object"!=typeof e(this)||e(this).length<1?n:this},h=function(t){var o=["rounded","rounded-dark","rounded-dots","rounded-dots-dark"],a=["rounded-dots","rounded-dots-dark","3d","3d-dark","3d-thick","3d-thick-dark","inset","inset-dark","inset-2","inset-2-dark","inset-3","inset-3-dark"],n=["minimal","minimal-dark"],i=["minimal","minimal-dark"],r=["minimal","minimal-dark"];t.autoDraggerLength=e.inArray(t.theme,o)>-1?!1:t.autoDraggerLength,t.autoExpandScrollbar=e.inArray(t.theme,a)>-1?!1:t.autoExpandScrollbar,t.scrollButtons.enable=e.inArray(t.theme,n)>-1?!1:t.scrollButtons.enable,t.autoHideScrollbar=e.inArray(t.theme,i)>-1?!0:t.autoHideScrollbar,t.scrollbarPosition=e.inArray(t.theme,r)>-1?"outside":t.scrollbarPosition},m=function(e){l[e]&&(clearTimeout(l[e]),$(l,e))},p=function(e){return"yx"===e||"xy"===e||"auto"===e?"yx":"x"===e||"horizontal"===e?"x":"y"},g=function(e){return"stepped"===e||"pixels"===e||"step"===e||"click"===e?"stepped":"stepless"},v=function(){var t=e(this),n=t.data(a),i=n.opt,r=i.autoExpandScrollbar?" "+d[1]+"_expand":"",l=["<div id='mCSB_"+n.idx+"_scrollbar_vertical' class='mCSB_scrollTools mCSB_"+n.idx+"_scrollbar mCS-"+i.theme+" mCSB_scrollTools_vertical"+r+"'><div class='"+d[12]+"'><div id='mCSB_"+n.idx+"_dragger_vertical' class='mCSB_dragger' style='position:absolute;'><div class='mCSB_dragger_bar' /></div><div class='mCSB_draggerRail' /></div></div>","<div id='mCSB_"+n.idx+"_scrollbar_horizontal' class='mCSB_scrollTools mCSB_"+n.idx+"_scrollbar mCS-"+i.theme+" mCSB_scrollTools_horizontal"+r+"'><div class='"+d[12]+"'><div id='mCSB_"+n.idx+"_dragger_horizontal' class='mCSB_dragger' style='position:absolute;'><div class='mCSB_dragger_bar' /></div><div class='mCSB_draggerRail' /></div></div>"],s="yx"===i.axis?"mCSB_vertical_horizontal":"x"===i.axis?"mCSB_horizontal":"mCSB_vertical",c="yx"===i.axis?l[0]+l[1]:"x"===i.axis?l[1]:l[0],u="yx"===i.axis?"<div id='mCSB_"+n.idx+"_container_wrapper' class='mCSB_container_wrapper' />":"",f=i.autoHideScrollbar?" "+d[6]:"",h="x"!==i.axis&&"rtl"===n.langDir?" "+d[7]:"";i.setWidth&&t.css("width",i.setWidth),i.setHeight&&t.css("height",i.setHeight),i.setLeft="y"!==i.axis&&"rtl"===n.langDir?"989999px":i.setLeft,t.addClass(o+" _"+a+"_"+n.idx+f+h).wrapInner("<div id='mCSB_"+n.idx+"' class='mCustomScrollBox mCS-"+i.theme+" "+s+"'><div id='mCSB_"+n.idx+"_container' class='mCSB_container' style='position:relative; top:"+i.setTop+"; left:"+i.setLeft+";' dir='"+n.langDir+"' /></div>");var m=e("#mCSB_"+n.idx),p=e("#mCSB_"+n.idx+"_container");"y"===i.axis||i.advanced.autoExpandHorizontalScroll||p.css("width",x(p)),"outside"===i.scrollbarPosition?("static"===t.css("position")&&t.css("position","relative"),t.css("overflow","visible"),m.addClass("mCSB_outside").after(c)):(m.addClass("mCSB_inside").append(c),p.wrap(u)),w.call(this);var g=[e("#mCSB_"+n.idx+"_dragger_vertical"),e("#mCSB_"+n.idx+"_dragger_horizontal")];g[0].css("min-height",g[0].height()),g[1].css("min-width",g[1].width())},x=function(t){var o=[t[0].scrollWidth,Math.max.apply(Math,t.children().map(function(){return e(this).outerWidth(!0)}).get())],a=t.parent().width();return o[0]>a?o[0]:o[1]>a?o[1]:"100%"},_=function(){var t=e(this),o=t.data(a),n=o.opt,i=e("#mCSB_"+o.idx+"_container");if(n.advanced.autoExpandHorizontalScroll&&"y"!==n.axis){i.css({width:"auto","min-width":0,"overflow-x":"scroll"});var r=Math.ceil(i[0].scrollWidth);3===n.advanced.autoExpandHorizontalScroll||2!==n.advanced.autoExpandHorizontalScroll&&r>i.parent().width()?i.css({width:r,"min-width":"100%","overflow-x":"inherit"}):i.css({"overflow-x":"inherit",position:"absolute"}).wrap("<div class='mCSB_h_wrapper' style='position:relative; left:0; width:999999px;' />").css({width:Math.ceil(i[0].getBoundingClientRect().right+.4)-Math.floor(i[0].getBoundingClientRect().left),"min-width":"100%",position:"relative"}).unwrap()}},w=function(){var t=e(this),o=t.data(a),n=o.opt,i=e(".mCSB_"+o.idx+"_scrollbar:first"),r=oe(n.scrollButtons.tabindex)?"tabindex='"+n.scrollButtons.tabindex+"'":"",l=["<a href='#' class='"+d[13]+"' "+r+" />","<a href='#' class='"+d[14]+"' "+r+" />","<a href='#' class='"+d[15]+"' "+r+" />","<a href='#' class='"+d[16]+"' "+r+" />"],s=["x"===n.axis?l[2]:l[0],"x"===n.axis?l[3]:l[1],l[2],l[3]];n.scrollButtons.enable&&i.prepend(s[0]).append(s[1]).next(".mCSB_scrollTools").prepend(s[2]).append(s[3])},S=function(){var t=e(this),o=t.data(a),n=e("#mCSB_"+o.idx),i=e("#mCSB_"+o.idx+"_container"),r=[e("#mCSB_"+o.idx+"_dragger_vertical"),e("#mCSB_"+o.idx+"_dragger_horizontal")],l=[n.height()/i.outerHeight(!1),n.width()/i.outerWidth(!1)],c=[parseInt(r[0].css("min-height")),Math.round(l[0]*r[0].parent().height()),parseInt(r[1].css("min-width")),Math.round(l[1]*r[1].parent().width())],d=s&&c[1]<c[0]?c[0]:c[1],u=s&&c[3]<c[2]?c[2]:c[3];r[0].css({height:d,"max-height":r[0].parent().height()-10}).find(".mCSB_dragger_bar").css({"line-height":c[0]+"px"}),r[1].css({width:u,"max-width":r[1].parent().width()-10})},b=function(){var t=e(this),o=t.data(a),n=e("#mCSB_"+o.idx),i=e("#mCSB_"+o.idx+"_container"),r=[e("#mCSB_"+o.idx+"_dragger_vertical"),e("#mCSB_"+o.idx+"_dragger_horizontal")],l=[i.outerHeight(!1)-n.height(),i.outerWidth(!1)-n.width()],s=[l[0]/(r[0].parent().height()-r[0].height()),l[1]/(r[1].parent().width()-r[1].width())];o.scrollRatio={y:s[0],x:s[1]}},C=function(e,t,o){var a=o?d[0]+"_expanded":"",n=e.closest(".mCSB_scrollTools");"active"===t?(e.toggleClass(d[0]+" "+a),n.toggleClass(d[1]),e[0]._draggable=e[0]._draggable?0:1):e[0]._draggable||("hide"===t?(e.removeClass(d[0]),n.removeClass(d[1])):(e.addClass(d[0]),n.addClass(d[1])))},y=function(){var t=e(this),o=t.data(a),n=e("#mCSB_"+o.idx),i=e("#mCSB_"+o.idx+"_container"),r=null==o.overflowed?i.height():i.outerHeight(!1),l=null==o.overflowed?i.width():i.outerWidth(!1),s=i[0].scrollHeight,c=i[0].scrollWidth;return s>r&&(r=s),c>l&&(l=c),[r>n.height(),l>n.width()]},B=function(){var t=e(this),o=t.data(a),n=o.opt,i=e("#mCSB_"+o.idx),r=e("#mCSB_"+o.idx+"_container"),l=[e("#mCSB_"+o.idx+"_dragger_vertical"),e("#mCSB_"+o.idx+"_dragger_horizontal")];if(Q(t),("x"!==n.axis&&!o.overflowed[0]||"y"===n.axis&&o.overflowed[0])&&(l[0].add(r).css("top",0),G(t,"_resetY")),"y"!==n.axis&&!o.overflowed[1]||"x"===n.axis&&o.overflowed[1]){var s=dx=0;"rtl"===o.langDir&&(s=i.width()-r.outerWidth(!1),dx=Math.abs(s/o.scrollRatio.x)),r.css("left",s),l[1].css("left",dx),G(t,"_resetX")}},T=function(){function t(){r=setTimeout(function(){e.event.special.mousewheel?(clearTimeout(r),W.call(o[0])):t()},100)}var o=e(this),n=o.data(a),i=n.opt;if(!n.bindEvents){if(I.call(this),i.contentTouchScroll&&D.call(this),E.call(this),i.mouseWheel.enable){var r;t()}P.call(this),U.call(this),i.advanced.autoScrollOnFocus&&H.call(this),i.scrollButtons.enable&&F.call(this),i.keyboard.enable&&q.call(this),n.bindEvents=!0}},k=function(){var t=e(this),o=t.data(a),n=o.opt,i=a+"_"+o.idx,r=".mCSB_"+o.idx+"_scrollbar",l=e("#mCSB_"+o.idx+",#mCSB_"+o.idx+"_container,#mCSB_"+o.idx+"_container_wrapper,"+r+" ."+d[12]+",#mCSB_"+o.idx+"_dragger_vertical,#mCSB_"+o.idx+"_dragger_horizontal,"+r+">a"),s=e("#mCSB_"+o.idx+"_container");n.advanced.releaseDraggableSelectors&&l.add(e(n.advanced.releaseDraggableSelectors)),n.advanced.extraDraggableSelectors&&l.add(e(n.advanced.extraDraggableSelectors)),o.bindEvents&&(e(document).add(e(!A()||top.document)).unbind("."+i),l.each(function(){e(this).unbind("."+i)}),clearTimeout(t[0]._focusTimeout),$(t[0],"_focusTimeout"),clearTimeout(o.sequential.step),$(o.sequential,"step"),clearTimeout(s[0].onCompleteTimeout),$(s[0],"onCompleteTimeout"),o.bindEvents=!1)},M=function(t){var o=e(this),n=o.data(a),i=n.opt,r=e("#mCSB_"+n.idx+"_container_wrapper"),l=r.length?r:e("#mCSB_"+n.idx+"_container"),s=[e("#mCSB_"+n.idx+"_scrollbar_vertical"),e("#mCSB_"+n.idx+"_scrollbar_horizontal")],c=[s[0].find(".mCSB_dragger"),s[1].find(".mCSB_dragger")];"x"!==i.axis&&(n.overflowed[0]&&!t?(s[0].add(c[0]).add(s[0].children("a")).css("display","block"),l.removeClass(d[8]+" "+d[10])):(i.alwaysShowScrollbar?(2!==i.alwaysShowScrollbar&&c[0].css("display","none"),l.removeClass(d[10])):(s[0].css("display","none"),l.addClass(d[10])),l.addClass(d[8]))),"y"!==i.axis&&(n.overflowed[1]&&!t?(s[1].add(c[1]).add(s[1].children("a")).css("display","block"),l.removeClass(d[9]+" "+d[11])):(i.alwaysShowScrollbar?(2!==i.alwaysShowScrollbar&&c[1].css("display","none"),l.removeClass(d[11])):(s[1].css("display","none"),l.addClass(d[11])),l.addClass(d[9]))),n.overflowed[0]||n.overflowed[1]?o.removeClass(d[5]):o.addClass(d[5])},O=function(t){var o=t.type,a=t.target.ownerDocument!==document&&null!==frameElement?[e(frameElement).offset().top,e(frameElement).offset().left]:null,n=A()&&t.target.ownerDocument!==top.document&&null!==frameElement?[e(t.view.frameElement).offset().top,e(t.view.frameElement).offset().left]:[0,0];switch(o){case"pointerdown":case"MSPointerDown":case"pointermove":case"MSPointerMove":case"pointerup":case"MSPointerUp":return a?[t.originalEvent.pageY-a[0]+n[0],t.originalEvent.pageX-a[1]+n[1],!1]:[t.originalEvent.pageY,t.originalEvent.pageX,!1];case"touchstart":case"touchmove":case"touchend":var i=t.originalEvent.touches[0]||t.originalEvent.changedTouches[0],r=t.originalEvent.touches.length||t.originalEvent.changedTouches.length;return t.target.ownerDocument!==document?[i.screenY,i.screenX,r>1]:[i.pageY,i.pageX,r>1];default:return a?[t.pageY-a[0]+n[0],t.pageX-a[1]+n[1],!1]:[t.pageY,t.pageX,!1]}},I=function(){function t(e,t,a,n){if(h[0].idleTimer=d.scrollInertia<233?250:0,o.attr("id")===f[1])var i="x",s=(o[0].offsetLeft-t+n)*l.scrollRatio.x;else var i="y",s=(o[0].offsetTop-e+a)*l.scrollRatio.y;G(r,s.toString(),{dir:i,drag:!0})}var o,n,i,r=e(this),l=r.data(a),d=l.opt,u=a+"_"+l.idx,f=["mCSB_"+l.idx+"_dragger_vertical","mCSB_"+l.idx+"_dragger_horizontal"],h=e("#mCSB_"+l.idx+"_container"),m=e("#"+f[0]+",#"+f[1]),p=d.advanced.releaseDraggableSelectors?m.add(e(d.advanced.releaseDraggableSelectors)):m,g=d.advanced.extraDraggableSelectors?e(!A()||top.document).add(e(d.advanced.extraDraggableSelectors)):e(!A()||top.document);m.bind("contextmenu."+u,function(e){e.preventDefault()}).bind("mousedown."+u+" touchstart."+u+" pointerdown."+u+" MSPointerDown."+u,function(t){if(t.stopImmediatePropagation(),t.preventDefault(),ee(t)){c=!0,s&&(document.onselectstart=function(){return!1}),L.call(h,!1),Q(r),o=e(this);var a=o.offset(),l=O(t)[0]-a.top,u=O(t)[1]-a.left,f=o.height()+a.top,m=o.width()+a.left;f>l&&l>0&&m>u&&u>0&&(n=l,i=u),C(o,"active",d.autoExpandScrollbar)}}).bind("touchmove."+u,function(e){e.stopImmediatePropagation(),e.preventDefault();var a=o.offset(),r=O(e)[0]-a.top,l=O(e)[1]-a.left;t(n,i,r,l)}),e(document).add(g).bind("mousemove."+u+" pointermove."+u+" MSPointerMove."+u,function(e){if(o){var a=o.offset(),r=O(e)[0]-a.top,l=O(e)[1]-a.left;if(n===r&&i===l)return;t(n,i,r,l)}}).add(p).bind("mouseup."+u+" touchend."+u+" pointerup."+u+" MSPointerUp."+u,function(){o&&(C(o,"active",d.autoExpandScrollbar),o=null),c=!1,s&&(document.onselectstart=null),L.call(h,!0)})},D=function(){function o(e){if(!te(e)||c||O(e)[2])return void(t=0);t=1,b=0,C=0,d=1,y.removeClass("mCS_touch_action");var o=I.offset();u=O(e)[0]-o.top,f=O(e)[1]-o.left,z=[O(e)[0],O(e)[1]]}function n(e){if(te(e)&&!c&&!O(e)[2]&&(T.documentTouchScroll||e.preventDefault(),e.stopImmediatePropagation(),(!C||b)&&d)){g=K();var t=M.offset(),o=O(e)[0]-t.top,a=O(e)[1]-t.left,n="mcsLinearOut";if(E.push(o),W.push(a),z[2]=Math.abs(O(e)[0]-z[0]),z[3]=Math.abs(O(e)[1]-z[1]),B.overflowed[0])var i=D[0].parent().height()-D[0].height(),r=u-o>0&&o-u>-(i*B.scrollRatio.y)&&(2*z[3]<z[2]||"yx"===T.axis);if(B.overflowed[1])var l=D[1].parent().width()-D[1].width(),h=f-a>0&&a-f>-(l*B.scrollRatio.x)&&(2*z[2]<z[3]||"yx"===T.axis);r||h?(U||e.preventDefault(),b=1):(C=1,y.addClass("mCS_touch_action")),U&&e.preventDefault(),w="yx"===T.axis?[u-o,f-a]:"x"===T.axis?[null,f-a]:[u-o,null],I[0].idleTimer=250,B.overflowed[0]&&s(w[0],R,n,"y","all",!0),B.overflowed[1]&&s(w[1],R,n,"x",L,!0)}}function i(e){if(!te(e)||c||O(e)[2])return void(t=0);t=1,e.stopImmediatePropagation(),Q(y),p=K();var o=M.offset();h=O(e)[0]-o.top,m=O(e)[1]-o.left,E=[],W=[]}function r(e){if(te(e)&&!c&&!O(e)[2]){d=0,e.stopImmediatePropagation(),b=0,C=0,v=K();var t=M.offset(),o=O(e)[0]-t.top,a=O(e)[1]-t.left;if(!(v-g>30)){_=1e3/(v-p);var n="mcsEaseOut",i=2.5>_,r=i?[E[E.length-2],W[W.length-2]]:[0,0];x=i?[o-r[0],a-r[1]]:[o-h,a-m];var u=[Math.abs(x[0]),Math.abs(x[1])];_=i?[Math.abs(x[0]/4),Math.abs(x[1]/4)]:[_,_];var f=[Math.abs(I[0].offsetTop)-x[0]*l(u[0]/_[0],_[0]),Math.abs(I[0].offsetLeft)-x[1]*l(u[1]/_[1],_[1])];w="yx"===T.axis?[f[0],f[1]]:"x"===T.axis?[null,f[1]]:[f[0],null],S=[4*u[0]+T.scrollInertia,4*u[1]+T.scrollInertia];var y=parseInt(T.contentTouchScroll)||0;w[0]=u[0]>y?w[0]:0,w[1]=u[1]>y?w[1]:0,B.overflowed[0]&&s(w[0],S[0],n,"y",L,!1),B.overflowed[1]&&s(w[1],S[1],n,"x",L,!1)}}}function l(e,t){var o=[1.5*t,2*t,t/1.5,t/2];return e>90?t>4?o[0]:o[3]:e>60?t>3?o[3]:o[2]:e>30?t>8?o[1]:t>6?o[0]:t>4?t:o[2]:t>8?t:o[3]}function s(e,t,o,a,n,i){e&&G(y,e.toString(),{dur:t,scrollEasing:o,dir:a,overwrite:n,drag:i})}var d,u,f,h,m,p,g,v,x,_,w,S,b,C,y=e(this),B=y.data(a),T=B.opt,k=a+"_"+B.idx,M=e("#mCSB_"+B.idx),I=e("#mCSB_"+B.idx+"_container"),D=[e("#mCSB_"+B.idx+"_dragger_vertical"),e("#mCSB_"+B.idx+"_dragger_horizontal")],E=[],W=[],R=0,L="yx"===T.axis?"none":"all",z=[],P=I.find("iframe"),H=["touchstart."+k+" pointerdown."+k+" MSPointerDown."+k,"touchmove."+k+" pointermove."+k+" MSPointerMove."+k,"touchend."+k+" pointerup."+k+" MSPointerUp."+k],U=void 0!==document.body.style.touchAction&&""!==document.body.style.touchAction;I.bind(H[0],function(e){o(e)}).bind(H[1],function(e){n(e)}),M.bind(H[0],function(e){i(e)}).bind(H[2],function(e){r(e)}),P.length&&P.each(function(){e(this).bind("load",function(){A(this)&&e(this.contentDocument||this.contentWindow.document).bind(H[0],function(e){o(e),i(e)}).bind(H[1],function(e){n(e)}).bind(H[2],function(e){r(e)})})})},E=function(){function o(){return window.getSelection?window.getSelection().toString():document.selection&&"Control"!=document.selection.type?document.selection.createRange().text:0}function n(e,t,o){d.type=o&&i?"stepped":"stepless",d.scrollAmount=10,j(r,e,t,"mcsLinearOut",o?60:null)}var i,r=e(this),l=r.data(a),s=l.opt,d=l.sequential,u=a+"_"+l.idx,f=e("#mCSB_"+l.idx+"_container"),h=f.parent();f.bind("mousedown."+u,function(){t||i||(i=1,c=!0)}).add(document).bind("mousemove."+u,function(e){if(!t&&i&&o()){var a=f.offset(),r=O(e)[0]-a.top+f[0].offsetTop,c=O(e)[1]-a.left+f[0].offsetLeft;r>0&&r<h.height()&&c>0&&c<h.width()?d.step&&n("off",null,"stepped"):("x"!==s.axis&&l.overflowed[0]&&(0>r?n("on",38):r>h.height()&&n("on",40)),"y"!==s.axis&&l.overflowed[1]&&(0>c?n("on",37):c>h.width()&&n("on",39)))}}).bind("mouseup."+u+" dragend."+u,function(){t||(i&&(i=0,n("off",null)),c=!1)})},W=function(){function t(t,a){if(Q(o),!z(o,t.target)){var r="auto"!==i.mouseWheel.deltaFactor?parseInt(i.mouseWheel.deltaFactor):s&&t.deltaFactor<100?100:t.deltaFactor||100,d=i.scrollInertia;if("x"===i.axis||"x"===i.mouseWheel.axis)var u="x",f=[Math.round(r*n.scrollRatio.x),parseInt(i.mouseWheel.scrollAmount)],h="auto"!==i.mouseWheel.scrollAmount?f[1]:f[0]>=l.width()?.9*l.width():f[0],m=Math.abs(e("#mCSB_"+n.idx+"_container")[0].offsetLeft),p=c[1][0].offsetLeft,g=c[1].parent().width()-c[1].width(),v="y"===i.mouseWheel.axis?t.deltaY||a:t.deltaX;else var u="y",f=[Math.round(r*n.scrollRatio.y),parseInt(i.mouseWheel.scrollAmount)],h="auto"!==i.mouseWheel.scrollAmount?f[1]:f[0]>=l.height()?.9*l.height():f[0],m=Math.abs(e("#mCSB_"+n.idx+"_container")[0].offsetTop),p=c[0][0].offsetTop,g=c[0].parent().height()-c[0].height(),v=t.deltaY||a;"y"===u&&!n.overflowed[0]||"x"===u&&!n.overflowed[1]||((i.mouseWheel.invert||t.webkitDirectionInvertedFromDevice)&&(v=-v),i.mouseWheel.normalizeDelta&&(v=0>v?-1:1),(v>0&&0!==p||0>v&&p!==g||i.mouseWheel.preventDefault)&&(t.stopImmediatePropagation(),t.preventDefault()),t.deltaFactor<5&&!i.mouseWheel.normalizeDelta&&(h=t.deltaFactor,d=17),G(o,(m-v*h).toString(),{dir:u,dur:d}))}}if(e(this).data(a)){var o=e(this),n=o.data(a),i=n.opt,r=a+"_"+n.idx,l=e("#mCSB_"+n.idx),c=[e("#mCSB_"+n.idx+"_dragger_vertical"),e("#mCSB_"+n.idx+"_dragger_horizontal")],d=e("#mCSB_"+n.idx+"_container").find("iframe");d.length&&d.each(function(){e(this).bind("load",function(){A(this)&&e(this.contentDocument||this.contentWindow.document).bind("mousewheel."+r,function(e,o){t(e,o)})})}),l.bind("mousewheel."+r,function(e,o){t(e,o)})}},R=new Object,A=function(t){var o=!1,a=!1,n=null;if(void 0===t?a="#empty":void 0!==e(t).attr("id")&&(a=e(t).attr("id")),a!==!1&&void 0!==R[a])return R[a];if(t){try{var i=t.contentDocument||t.contentWindow.document;n=i.body.innerHTML}catch(r){}o=null!==n}else{try{var i=top.document;n=i.body.innerHTML}catch(r){}o=null!==n}return a!==!1&&(R[a]=o),o},L=function(e){var t=this.find("iframe");if(t.length){var o=e?"auto":"none";t.css("pointer-events",o)}},z=function(t,o){var n=o.nodeName.toLowerCase(),i=t.data(a).opt.mouseWheel.disableOver,r=["select","textarea"];return e.inArray(n,i)>-1&&!(e.inArray(n,r)>-1&&!e(o).is(":focus"))},P=function(){var t,o=e(this),n=o.data(a),i=a+"_"+n.idx,r=e("#mCSB_"+n.idx+"_container"),l=r.parent(),s=e(".mCSB_"+n.idx+"_scrollbar ."+d[12]);s.bind("mousedown."+i+" touchstart."+i+" pointerdown."+i+" MSPointerDown."+i,function(o){c=!0,e(o.target).hasClass("mCSB_dragger")||(t=1)}).bind("touchend."+i+" pointerup."+i+" MSPointerUp."+i,function(){c=!1}).bind("click."+i,function(a){if(t&&(t=0,e(a.target).hasClass(d[12])||e(a.target).hasClass("mCSB_draggerRail"))){Q(o);var i=e(this),s=i.find(".mCSB_dragger");if(i.parent(".mCSB_scrollTools_horizontal").length>0){if(!n.overflowed[1])return;var c="x",u=a.pageX>s.offset().left?-1:1,f=Math.abs(r[0].offsetLeft)-u*(.9*l.width())}else{if(!n.overflowed[0])return;var c="y",u=a.pageY>s.offset().top?-1:1,f=Math.abs(r[0].offsetTop)-u*(.9*l.height())}G(o,f.toString(),{dir:c,scrollEasing:"mcsEaseInOut"})}})},H=function(){var t=e(this),o=t.data(a),n=o.opt,i=a+"_"+o.idx,r=e("#mCSB_"+o.idx+"_container"),l=r.parent();r.bind("focusin."+i,function(){var o=e(document.activeElement),a=r.find(".mCustomScrollBox").length,i=0;o.is(n.advanced.autoScrollOnFocus)&&(Q(t),clearTimeout(t[0]._focusTimeout),t[0]._focusTimer=a?(i+17)*a:0,t[0]._focusTimeout=setTimeout(function(){var e=[ae(o)[0],ae(o)[1]],a=[r[0].offsetTop,r[0].offsetLeft],s=[a[0]+e[0]>=0&&a[0]+e[0]<l.height()-o.outerHeight(!1),a[1]+e[1]>=0&&a[0]+e[1]<l.width()-o.outerWidth(!1)],c="yx"!==n.axis||s[0]||s[1]?"all":"none";"x"===n.axis||s[0]||G(t,e[0].toString(),{dir:"y",scrollEasing:"mcsEaseInOut",overwrite:c,dur:i}),"y"===n.axis||s[1]||G(t,e[1].toString(),{dir:"x",scrollEasing:"mcsEaseInOut",overwrite:c,dur:i})},t[0]._focusTimer))})},U=function(){var t=e(this),o=t.data(a),n=a+"_"+o.idx,i=e("#mCSB_"+o.idx+"_container").parent();i.bind("scroll."+n,function(){0===i.scrollTop()&&0===i.scrollLeft()||e(".mCSB_"+o.idx+"_scrollbar").css("visibility","hidden")})},F=function(){var t=e(this),o=t.data(a),n=o.opt,i=o.sequential,r=a+"_"+o.idx,l=".mCSB_"+o.idx+"_scrollbar",s=e(l+">a");s.bind("contextmenu."+r,function(e){e.preventDefault()}).bind("mousedown."+r+" touchstart."+r+" pointerdown."+r+" MSPointerDown."+r+" mouseup."+r+" touchend."+r+" pointerup."+r+" MSPointerUp."+r+" mouseout."+r+" pointerout."+r+" MSPointerOut."+r+" click."+r,function(a){function r(e,o){i.scrollAmount=n.scrollButtons.scrollAmount,j(t,e,o)}if(a.preventDefault(),ee(a)){var l=e(this).attr("class");switch(i.type=n.scrollButtons.scrollType,a.type){case"mousedown":case"touchstart":case"pointerdown":case"MSPointerDown":if("stepped"===i.type)return;c=!0,o.tweenRunning=!1,r("on",l);break;case"mouseup":case"touchend":case"pointerup":case"MSPointerUp":case"mouseout":case"pointerout":case"MSPointerOut":if("stepped"===i.type)return;c=!1,i.dir&&r("off",l);break;case"click":if("stepped"!==i.type||o.tweenRunning)return;r("on",l)}}})},q=function(){function t(t){function a(e,t){r.type=i.keyboard.scrollType,r.scrollAmount=i.keyboard.scrollAmount,"stepped"===r.type&&n.tweenRunning||j(o,e,t)}switch(t.type){case"blur":n.tweenRunning&&r.dir&&a("off",null);break;case"keydown":case"keyup":var l=t.keyCode?t.keyCode:t.which,s="on";if("x"!==i.axis&&(38===l||40===l)||"y"!==i.axis&&(37===l||39===l)){if((38===l||40===l)&&!n.overflowed[0]||(37===l||39===l)&&!n.overflowed[1])return;"keyup"===t.type&&(s="off"),e(document.activeElement).is(u)||(t.preventDefault(),t.stopImmediatePropagation(),a(s,l))}else if(33===l||34===l){if((n.overflowed[0]||n.overflowed[1])&&(t.preventDefault(),t.stopImmediatePropagation()),"keyup"===t.type){Q(o);var f=34===l?-1:1;if("x"===i.axis||"yx"===i.axis&&n.overflowed[1]&&!n.overflowed[0])var h="x",m=Math.abs(c[0].offsetLeft)-f*(.9*d.width());else var h="y",m=Math.abs(c[0].offsetTop)-f*(.9*d.height());G(o,m.toString(),{dir:h,scrollEasing:"mcsEaseInOut"})}}else if((35===l||36===l)&&!e(document.activeElement).is(u)&&((n.overflowed[0]||n.overflowed[1])&&(t.preventDefault(),t.stopImmediatePropagation()),"keyup"===t.type)){if("x"===i.axis||"yx"===i.axis&&n.overflowed[1]&&!n.overflowed[0])var h="x",m=35===l?Math.abs(d.width()-c.outerWidth(!1)):0;else var h="y",m=35===l?Math.abs(d.height()-c.outerHeight(!1)):0;G(o,m.toString(),{dir:h,scrollEasing:"mcsEaseInOut"})}}}var o=e(this),n=o.data(a),i=n.opt,r=n.sequential,l=a+"_"+n.idx,s=e("#mCSB_"+n.idx),c=e("#mCSB_"+n.idx+"_container"),d=c.parent(),u="input,textarea,select,datalist,keygen,[contenteditable='true']",f=c.find("iframe"),h=["blur."+l+" keydown."+l+" keyup."+l];f.length&&f.each(function(){e(this).bind("load",function(){A(this)&&e(this.contentDocument||this.contentWindow.document).bind(h[0],function(e){t(e)})})}),s.attr("tabindex","0").bind(h[0],function(e){t(e)})},j=function(t,o,n,i,r){function l(e){u.snapAmount&&(f.scrollAmount=u.snapAmount instanceof Array?"x"===f.dir[0]?u.snapAmount[1]:u.snapAmount[0]:u.snapAmount);var o="stepped"!==f.type,a=r?r:e?o?p/1.5:g:1e3/60,n=e?o?7.5:40:2.5,s=[Math.abs(h[0].offsetTop),Math.abs(h[0].offsetLeft)],d=[c.scrollRatio.y>10?10:c.scrollRatio.y,c.scrollRatio.x>10?10:c.scrollRatio.x],m="x"===f.dir[0]?s[1]+f.dir[1]*(d[1]*n):s[0]+f.dir[1]*(d[0]*n),v="x"===f.dir[0]?s[1]+f.dir[1]*parseInt(f.scrollAmount):s[0]+f.dir[1]*parseInt(f.scrollAmount),x="auto"!==f.scrollAmount?v:m,_=i?i:e?o?"mcsLinearOut":"mcsEaseInOut":"mcsLinear",w=!!e;return e&&17>a&&(x="x"===f.dir[0]?s[1]:s[0]),G(t,x.toString(),{dir:f.dir[0],scrollEasing:_,dur:a,onComplete:w}),e?void(f.dir=!1):(clearTimeout(f.step),void(f.step=setTimeout(function(){l()},a)))}function s(){clearTimeout(f.step),$(f,"step"),Q(t)}var c=t.data(a),u=c.opt,f=c.sequential,h=e("#mCSB_"+c.idx+"_container"),m="stepped"===f.type,p=u.scrollInertia<26?26:u.scrollInertia,g=u.scrollInertia<1?17:u.scrollInertia;switch(o){case"on":if(f.dir=[n===d[16]||n===d[15]||39===n||37===n?"x":"y",n===d[13]||n===d[15]||38===n||37===n?-1:1],Q(t),oe(n)&&"stepped"===f.type)return;l(m);break;case"off":s(),(m||c.tweenRunning&&f.dir)&&l(!0)}},Y=function(t){var o=e(this).data(a).opt,n=[];return"function"==typeof t&&(t=t()),t instanceof Array?n=t.length>1?[t[0],t[1]]:"x"===o.axis?[null,t[0]]:[t[0],null]:(n[0]=t.y?t.y:t.x||"x"===o.axis?null:t,n[1]=t.x?t.x:t.y||"y"===o.axis?null:t),"function"==typeof n[0]&&(n[0]=n[0]()),"function"==typeof n[1]&&(n[1]=n[1]()),n},X=function(t,o){if(null!=t&&"undefined"!=typeof t){var n=e(this),i=n.data(a),r=i.opt,l=e("#mCSB_"+i.idx+"_container"),s=l.parent(),c=typeof t;o||(o="x"===r.axis?"x":"y");var d="x"===o?l.outerWidth(!1)-s.width():l.outerHeight(!1)-s.height(),f="x"===o?l[0].offsetLeft:l[0].offsetTop,h="x"===o?"left":"top";switch(c){case"function":return t();case"object":var m=t.jquery?t:e(t);if(!m.length)return;return"x"===o?ae(m)[1]:ae(m)[0];case"string":case"number":if(oe(t))return Math.abs(t);if(-1!==t.indexOf("%"))return Math.abs(d*parseInt(t)/100);if(-1!==t.indexOf("-="))return Math.abs(f-parseInt(t.split("-=")[1]));if(-1!==t.indexOf("+=")){var p=f+parseInt(t.split("+=")[1]);return p>=0?0:Math.abs(p)}if(-1!==t.indexOf("px")&&oe(t.split("px")[0]))return Math.abs(t.split("px")[0]);if("top"===t||"left"===t)return 0;if("bottom"===t)return Math.abs(s.height()-l.outerHeight(!1));if("right"===t)return Math.abs(s.width()-l.outerWidth(!1));if("first"===t||"last"===t){var m=l.find(":"+t);return"x"===o?ae(m)[1]:ae(m)[0]}return e(t).length?"x"===o?ae(e(t))[1]:ae(e(t))[0]:(l.css(h,t),void u.update.call(null,n[0]))}}},N=function(t){function o(){return clearTimeout(f[0].autoUpdate),0===l.parents("html").length?void(l=null):void(f[0].autoUpdate=setTimeout(function(){return c.advanced.updateOnSelectorChange&&(s.poll.change.n=i(),s.poll.change.n!==s.poll.change.o)?(s.poll.change.o=s.poll.change.n,void r(3)):c.advanced.updateOnContentResize&&(s.poll.size.n=l[0].scrollHeight+l[0].scrollWidth+f[0].offsetHeight+l[0].offsetHeight+l[0].offsetWidth,s.poll.size.n!==s.poll.size.o)?(s.poll.size.o=s.poll.size.n,void r(1)):!c.advanced.updateOnImageLoad||"auto"===c.advanced.updateOnImageLoad&&"y"===c.axis||(s.poll.img.n=f.find("img").length,s.poll.img.n===s.poll.img.o)?void((c.advanced.updateOnSelectorChange||c.advanced.updateOnContentResize||c.advanced.updateOnImageLoad)&&o()):(s.poll.img.o=s.poll.img.n,void f.find("img").each(function(){n(this)}))},c.advanced.autoUpdateTimeout))}function n(t){function o(e,t){return function(){
return t.apply(e,arguments)}}function a(){this.onload=null,e(t).addClass(d[2]),r(2)}if(e(t).hasClass(d[2]))return void r();var n=new Image;n.onload=o(n,a),n.src=t.src}function i(){c.advanced.updateOnSelectorChange===!0&&(c.advanced.updateOnSelectorChange="*");var e=0,t=f.find(c.advanced.updateOnSelectorChange);return c.advanced.updateOnSelectorChange&&t.length>0&&t.each(function(){e+=this.offsetHeight+this.offsetWidth}),e}function r(e){clearTimeout(f[0].autoUpdate),u.update.call(null,l[0],e)}var l=e(this),s=l.data(a),c=s.opt,f=e("#mCSB_"+s.idx+"_container");return t?(clearTimeout(f[0].autoUpdate),void $(f[0],"autoUpdate")):void o()},V=function(e,t,o){return Math.round(e/t)*t-o},Q=function(t){var o=t.data(a),n=e("#mCSB_"+o.idx+"_container,#mCSB_"+o.idx+"_container_wrapper,#mCSB_"+o.idx+"_dragger_vertical,#mCSB_"+o.idx+"_dragger_horizontal");n.each(function(){Z.call(this)})},G=function(t,o,n){function i(e){return s&&c.callbacks[e]&&"function"==typeof c.callbacks[e]}function r(){return[c.callbacks.alwaysTriggerOffsets||w>=S[0]+y,c.callbacks.alwaysTriggerOffsets||-B>=w]}function l(){var e=[h[0].offsetTop,h[0].offsetLeft],o=[x[0].offsetTop,x[0].offsetLeft],a=[h.outerHeight(!1),h.outerWidth(!1)],i=[f.height(),f.width()];t[0].mcs={content:h,top:e[0],left:e[1],draggerTop:o[0],draggerLeft:o[1],topPct:Math.round(100*Math.abs(e[0])/(Math.abs(a[0])-i[0])),leftPct:Math.round(100*Math.abs(e[1])/(Math.abs(a[1])-i[1])),direction:n.dir}}var s=t.data(a),c=s.opt,d={trigger:"internal",dir:"y",scrollEasing:"mcsEaseOut",drag:!1,dur:c.scrollInertia,overwrite:"all",callbacks:!0,onStart:!0,onUpdate:!0,onComplete:!0},n=e.extend(d,n),u=[n.dur,n.drag?0:n.dur],f=e("#mCSB_"+s.idx),h=e("#mCSB_"+s.idx+"_container"),m=h.parent(),p=c.callbacks.onTotalScrollOffset?Y.call(t,c.callbacks.onTotalScrollOffset):[0,0],g=c.callbacks.onTotalScrollBackOffset?Y.call(t,c.callbacks.onTotalScrollBackOffset):[0,0];if(s.trigger=n.trigger,0===m.scrollTop()&&0===m.scrollLeft()||(e(".mCSB_"+s.idx+"_scrollbar").css("visibility","visible"),m.scrollTop(0).scrollLeft(0)),"_resetY"!==o||s.contentReset.y||(i("onOverflowYNone")&&c.callbacks.onOverflowYNone.call(t[0]),s.contentReset.y=1),"_resetX"!==o||s.contentReset.x||(i("onOverflowXNone")&&c.callbacks.onOverflowXNone.call(t[0]),s.contentReset.x=1),"_resetY"!==o&&"_resetX"!==o){if(!s.contentReset.y&&t[0].mcs||!s.overflowed[0]||(i("onOverflowY")&&c.callbacks.onOverflowY.call(t[0]),s.contentReset.x=null),!s.contentReset.x&&t[0].mcs||!s.overflowed[1]||(i("onOverflowX")&&c.callbacks.onOverflowX.call(t[0]),s.contentReset.x=null),c.snapAmount){var v=c.snapAmount instanceof Array?"x"===n.dir?c.snapAmount[1]:c.snapAmount[0]:c.snapAmount;o=V(o,v,c.snapOffset)}switch(n.dir){case"x":var x=e("#mCSB_"+s.idx+"_dragger_horizontal"),_="left",w=h[0].offsetLeft,S=[f.width()-h.outerWidth(!1),x.parent().width()-x.width()],b=[o,0===o?0:o/s.scrollRatio.x],y=p[1],B=g[1],T=y>0?y/s.scrollRatio.x:0,k=B>0?B/s.scrollRatio.x:0;break;case"y":var x=e("#mCSB_"+s.idx+"_dragger_vertical"),_="top",w=h[0].offsetTop,S=[f.height()-h.outerHeight(!1),x.parent().height()-x.height()],b=[o,0===o?0:o/s.scrollRatio.y],y=p[0],B=g[0],T=y>0?y/s.scrollRatio.y:0,k=B>0?B/s.scrollRatio.y:0}b[1]<0||0===b[0]&&0===b[1]?b=[0,0]:b[1]>=S[1]?b=[S[0],S[1]]:b[0]=-b[0],t[0].mcs||(l(),i("onInit")&&c.callbacks.onInit.call(t[0])),clearTimeout(h[0].onCompleteTimeout),J(x[0],_,Math.round(b[1]),u[1],n.scrollEasing),!s.tweenRunning&&(0===w&&b[0]>=0||w===S[0]&&b[0]<=S[0])||J(h[0],_,Math.round(b[0]),u[0],n.scrollEasing,n.overwrite,{onStart:function(){n.callbacks&&n.onStart&&!s.tweenRunning&&(i("onScrollStart")&&(l(),c.callbacks.onScrollStart.call(t[0])),s.tweenRunning=!0,C(x),s.cbOffsets=r())},onUpdate:function(){n.callbacks&&n.onUpdate&&i("whileScrolling")&&(l(),c.callbacks.whileScrolling.call(t[0]))},onComplete:function(){if(n.callbacks&&n.onComplete){"yx"===c.axis&&clearTimeout(h[0].onCompleteTimeout);var e=h[0].idleTimer||0;h[0].onCompleteTimeout=setTimeout(function(){i("onScroll")&&(l(),c.callbacks.onScroll.call(t[0])),i("onTotalScroll")&&b[1]>=S[1]-T&&s.cbOffsets[0]&&(l(),c.callbacks.onTotalScroll.call(t[0])),i("onTotalScrollBack")&&b[1]<=k&&s.cbOffsets[1]&&(l(),c.callbacks.onTotalScrollBack.call(t[0])),s.tweenRunning=!1,h[0].idleTimer=0,C(x,"hide")},e)}}})}},J=function(e,t,o,a,n,i,r){function l(){S.stop||(x||m.call(),x=K()-v,s(),x>=S.time&&(S.time=x>S.time?x+f-(x-S.time):x+f-1,S.time<x+1&&(S.time=x+1)),S.time<a?S.id=h(l):g.call())}function s(){a>0?(S.currVal=u(S.time,_,b,a,n),w[t]=Math.round(S.currVal)+"px"):w[t]=o+"px",p.call()}function c(){f=1e3/60,S.time=x+f,h=window.requestAnimationFrame?window.requestAnimationFrame:function(e){return s(),setTimeout(e,.01)},S.id=h(l)}function d(){null!=S.id&&(window.requestAnimationFrame?window.cancelAnimationFrame(S.id):clearTimeout(S.id),S.id=null)}function u(e,t,o,a,n){switch(n){case"linear":case"mcsLinear":return o*e/a+t;case"mcsLinearOut":return e/=a,e--,o*Math.sqrt(1-e*e)+t;case"easeInOutSmooth":return e/=a/2,1>e?o/2*e*e+t:(e--,-o/2*(e*(e-2)-1)+t);case"easeInOutStrong":return e/=a/2,1>e?o/2*Math.pow(2,10*(e-1))+t:(e--,o/2*(-Math.pow(2,-10*e)+2)+t);case"easeInOut":case"mcsEaseInOut":return e/=a/2,1>e?o/2*e*e*e+t:(e-=2,o/2*(e*e*e+2)+t);case"easeOutSmooth":return e/=a,e--,-o*(e*e*e*e-1)+t;case"easeOutStrong":return o*(-Math.pow(2,-10*e/a)+1)+t;case"easeOut":case"mcsEaseOut":default:var i=(e/=a)*e,r=i*e;return t+o*(.499999999999997*r*i+-2.5*i*i+5.5*r+-6.5*i+4*e)}}e._mTween||(e._mTween={top:{},left:{}});var f,h,r=r||{},m=r.onStart||function(){},p=r.onUpdate||function(){},g=r.onComplete||function(){},v=K(),x=0,_=e.offsetTop,w=e.style,S=e._mTween[t];"left"===t&&(_=e.offsetLeft);var b=o-_;S.stop=0,"none"!==i&&d(),c()},K=function(){return window.performance&&window.performance.now?window.performance.now():window.performance&&window.performance.webkitNow?window.performance.webkitNow():Date.now?Date.now():(new Date).getTime()},Z=function(){var e=this;e._mTween||(e._mTween={top:{},left:{}});for(var t=["top","left"],o=0;o<t.length;o++){var a=t[o];e._mTween[a].id&&(window.requestAnimationFrame?window.cancelAnimationFrame(e._mTween[a].id):clearTimeout(e._mTween[a].id),e._mTween[a].id=null,e._mTween[a].stop=1)}},$=function(e,t){try{delete e[t]}catch(o){e[t]=null}},ee=function(e){return!(e.which&&1!==e.which)},te=function(e){var t=e.originalEvent.pointerType;return!(t&&"touch"!==t&&2!==t)},oe=function(e){return!isNaN(parseFloat(e))&&isFinite(e)},ae=function(e){var t=e.parents(".mCSB_container");return[e.offset().top-t.offset().top,e.offset().left-t.offset().left]},ne=function(){function e(){var e=["webkit","moz","ms","o"];if("hidden"in document)return"hidden";for(var t=0;t<e.length;t++)if(e[t]+"Hidden"in document)return e[t]+"Hidden";return null}var t=e();return t?document[t]:!1};e.fn[o]=function(t){return u[t]?u[t].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof t&&t?void e.error("Method "+t+" does not exist"):u.init.apply(this,arguments)},e[o]=function(t){return u[t]?u[t].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof t&&t?void e.error("Method "+t+" does not exist"):u.init.apply(this,arguments)},e[o].defaults=i,window[o]=!0,e(window).bind("load",function(){e(n)[o](),e.extend(e.expr[":"],{mcsInView:e.expr[":"].mcsInView||function(t){var o,a,n=e(t),i=n.parents(".mCSB_container");if(i.length)return o=i.parent(),a=[i[0].offsetTop,i[0].offsetLeft],a[0]+ae(n)[0]>=0&&a[0]+ae(n)[0]<o.height()-n.outerHeight(!1)&&a[1]+ae(n)[1]>=0&&a[1]+ae(n)[1]<o.width()-n.outerWidth(!1)},mcsInSight:e.expr[":"].mcsInSight||function(t,o,a){var n,i,r,l,s=e(t),c=s.parents(".mCSB_container"),d="exact"===a[3]?[[1,0],[1,0]]:[[.9,.1],[.6,.4]];if(c.length)return n=[s.outerHeight(!1),s.outerWidth(!1)],r=[c[0].offsetTop+ae(s)[0],c[0].offsetLeft+ae(s)[1]],i=[c.parent()[0].offsetHeight,c.parent()[0].offsetWidth],l=[n[0]<i[0]?d[0]:d[1],n[1]<i[1]?d[0]:d[1]],r[0]-i[0]*l[0][0]<0&&r[0]+n[0]-i[0]*l[0][1]>=0&&r[1]-i[1]*l[1][0]<0&&r[1]+n[1]-i[1]*l[1][1]>=0},mcsOverflow:e.expr[":"].mcsOverflow||function(t){var o=e(t).data(a);if(o)return o.overflowed[0]||o.overflowed[1]}})})})});
!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).SoundCloudAudio=t()}}(function(){return function o(s,a,l){function u(e,t){if(!a[e]){if(!s[e]){var i="function"==typeof require&&require;if(!t&&i)return i(e,!0);if(p)return p(e,!0);var r=new Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}var n=a[e]={exports:{}};s[e][0].call(n.exports,function(t){return u(s[e][1][t]||t)},n,n.exports,o,s,a,l)}return a[e].exports}for(var p="function"==typeof require&&require,t=0;t<l.length;t++)u(l[t]);return u}({1:[function(t,e,i){"use strict";var o,r="https://api.soundcloud.com",s="protocol hostname host pathname port search hash href".split(" ");function a(t){o||(o=document.createElement("a"));var e={};o.href=t||"";for(var i=0,r=s.length;i<r;i++){var n=s[i];e[n]=o[n]}return e}function l(t,e,i){var r=a(t),n=/\?(?:.*)$/.test(r.search)?"&":"?";return r.protocol+"//"+r.host+r.port+r.pathname+r.search+n+e+"="+i+r.hash}function n(t,e){if(!(this instanceof n))return new n(t,e);t||e||console.info("SoundCloud API requires clientId or custom apiUrl"),this._events={},this._clientId=t,this._baseUrl=e||r,this.playing=!1,this.duration=0,this.audio=document.createElement("audio")}n.prototype.resolve=function(i,r){var t=this._baseUrl+"/resolve.json?url="+encodeURIComponent(i);this._clientId&&(t=l(t,"client_id",this._clientId)),this._json(t,function(t){if(this.cleanData(),Array.isArray(t)&&(t={tracks:t}),t.tracks)t.tracks=t.tracks.map(this._transformTrack.bind(this)),this._playlist=t;else{this._track=this._transformTrack(t);var e=a(i);this._track.stream_url+=e.hash}this.duration=t.duration&&!isNaN(t.duration)?t.duration/1e3:0,r(t)}.bind(this))},n.prototype._jsonp=function(t,e){var i=document.getElementsByTagName("script")[0]||document.head,r=document.createElement("script"),n="jsonp_callback_"+(new Date).valueOf()+Math.floor(1e3*Math.random());window[n]=function(t){r.parentNode&&r.parentNode.removeChild(r),window[n]=function(){},e(t)},r.src=l(t,"callback",n),i.parentNode.insertBefore(r,i)},n.prototype._json=function(t,e){var i=new XMLHttpRequest;i.open("GET",t),i.onreadystatechange=function(){if(4===i.readyState&&200===i.status){var t={};try{t=JSON.parse(i.responseText)}catch(t){}e(t)}},i.send(null)},n.prototype._transformTrack=function(t){return this._baseUrl!==r&&(t.original_stream_url=t.stream_url,t.stream_url=t.stream_url.replace(r,this._baseUrl)),t},n.prototype.on=function(t,e){this._events[t]=e,this.audio.addEventListener(t,e,!1)},n.prototype.off=function(t,e){this._events[t]=null,this.audio.removeEventListener(t,e)},n.prototype.unbindAll=function(){for(var t in this._events){var e=this._events[t];e&&this.off(t,e)}},n.prototype.preload=function(t,e){this._track={stream_url:t},e&&(this.audio.preload=e),this.audio.src=this._clientId?l(t,"client_id",this._clientId):t},n.prototype.play=function(t){var e;if((t=t||{}).streamUrl)e=t.streamUrl;else if(this._playlist){var i=this._playlist.tracks.length;if(i){if(void 0===t.playlistIndex?this._playlistIndex=this._playlistIndex||0:this._playlistIndex=t.playlistIndex,this._playlistIndex>=i||this._playlistIndex<0)return void(this._playlistIndex=0);e=this._playlist.tracks[this._playlistIndex].stream_url}}else this._track&&(e=this._track.stream_url);if(!e)throw new Error("There is no tracks to play, use `streamUrl` option or `load` method");return this._clientId&&(e=l(e,"client_id",this._clientId)),e!==this.audio.src&&(this.audio.src=e),this.playing=e,this.audio.play()},n.prototype.pause=function(){this.audio.pause(),this.playing=!1},n.prototype.stop=function(){this.audio.pause(),this.audio.currentTime=0,this.playing=!1},n.prototype.next=function(t){t=t||{};var e=this._playlist.tracks.length;if(this._playlistIndex>=e-1){if(!t.loop)return;this._playlistIndex=-1}if(this._playlist&&e)return this.play({playlistIndex:++this._playlistIndex})},n.prototype.previous=function(){if(!(this._playlistIndex<=0))return this._playlist&&this._playlist.tracks.length?this.play({playlistIndex:--this._playlistIndex}):void 0},n.prototype.seek=function(t){if(!this.audio.readyState)return!1;var e=t.offsetX/t.target.offsetWidth||(t.layerX-t.target.offsetLeft)/t.target.offsetWidth;this.audio.currentTime=e*(this.audio.duration||0)},n.prototype.cleanData=function(){this._track=void 0,this._playlist=void 0},n.prototype.setVolume=function(t){this.audio.readyState&&(this.audio.volume=t)},n.prototype.setTime=function(t){this.audio.readyState&&(this.audio.currentTime=t)},e.exports=n},{}]},{},[1])(1)});

var errorRequire = 'このフィールドは必須です。';

var SoundcheckProject = {
  Init: function () {
    console.log('init project');
    SoundcheckProject.sumoSelect();
    SoundcheckProject.waveForm();
    SoundcheckProject.orderChart();
    SoundcheckProject.orderSort();
    SoundcheckProject.formSelectRole();
    SoundcheckProject.saleContentPrice();
    SoundcheckProject.inputHour();
  },

  inputHour: function() {
    $('.notification-time').each(function() {
      var $self = $(this);
      var step = $(this).data('step');
      var min = $(this).data('min');
      var max = $(this).data('max');

      $self.find('.input-button').on('click', function(){
        var val = $self.find('.input-text').val();
        var time = val.split(':');

        if ( $(this).hasClass('minus') ) {
          var new_val = parseInt(time[0]) - step;
          if ( new_val < min ) {
            return;
          } else {
            $self.find('.input-text').val(new_val.toString() + ':' + time[1].toString());
          }
        }
        if ( $(this).hasClass('plus') ) {
          var new_val = parseInt(time[0]) + step;
          if ( new_val > max ) {
            return;
          } else {
            $self.find('.input-text').val(new_val.toString() + ':' + time[1].toString());
          }
        }
      });
    });
  },

  selectAddDate: function() {
    $('.js-select-add-date').on('change', function(e) {
      if (e.target.value == 2) {
        $('.select-delivery-date').addClass('hidden')
        $('.select-demonstration-period').addClass('show')
      } else {
        $('.select-delivery-date').removeClass('hidden')
        $('.select-demonstration-period').removeClass('show')
      }
    })
  },

  onClickChangeIcon: function() {
    $('.edit-offer__general-icon--coppyright').click(function() {
      $(this).toggleClass('change-icon');
    })
  },

  editPriceOffer: function() {
    var iconEdit = $('.edit-offer__edit-price-icon--edit');
    var iconSave = $('.edit-offer__edit-price-icon--save');
    var iconCancel = $('.edit-offer__edit-price-icon--cancel');
    var editPrice = $('.edit-offer__edit-price');

    iconEdit.click(function() {
      $(this).addClass('hidden');
      editPrice.attr('disabled', false).addClass('forcus-edit');
      iconSave.addClass('show');
      iconCancel.addClass('show');
    });

    iconCancel.click(function() {
      iconEdit.removeClass('hidden');
      editPrice.attr('disabled', true).removeClass('forcus-edit');
      iconSave.removeClass('show');
      $(this).removeClass('show');
    })
  },

  stepForm: function () {
    const btnNextStep = document.getElementsByClassName('js-next-step');
    const btnPrevStep = document.getElementsByClassName('js-prev-step');
    const stepForms = document.getElementsByClassName('directer-offer__form');

    if ( stepForms.length > 0 ) {
      for (let i = 0; i < btnNextStep.length; i++) {
        btnNextStep[i].addEventListener('click', function() {
          const currentItem = $(this).closest('.step-content__item');
          const nextItem = currentItem.next();
          currentItem.removeClass('active animated slideInRight slideInLeft');
          nextItem.addClass('active animated slideInRight');

          currentItem.css({
            'margin-left': '-100%',
          })
        })
      }

      for (let i = 0; i < btnPrevStep.length; i++) {
        btnPrevStep[i].addEventListener('click', function() {

          const currentItem = $(this).closest('.step-content__item');
          const prevItem = currentItem.prev();
          currentItem.removeClass('active animated slideInLeft slideInRight');
          prevItem.addClass('active animated slideInLeft');
        })
      }
    }
  },

  onLoad: function () {
    console.log('onload project');
  },

  reSize: function () {
    console.log('resize project');
  },

  mCustomScrollbar: function() {
    $('.custom-scrollbar').mCustomScrollbar({
      theme: 'minimal-dark',
      callbacks: {
        onTotalScrollBack: function() {
            userScrollTop();
        }
      }
    });

    $('.custom-scrollbar-horizontal').mCustomScrollbar({
      theme: 'minimal-dark',
      axis: 'x'
    });

    $('.custom-scrollbar--bottom').mCustomScrollbar( 'scrollTo', 'bottom',{
      scrollInertia: 0,
      scrollEasing: 'easeOut'
    });

    // Scrollbar TextArea
    var hiddenDiv = $(document.createElement('div')),
      content = null;
      hiddenDiv.addClass('hiddendiv');
      $('body').prepend(hiddenDiv);

    $('.cs-textarea').each(function() {
      var textArea = $(this);
      textArea.wrap('<div class="cs-textarea-wrapper" />');
      var textAreaWrapper=textArea.parent('.cs-textarea-wrapper');
      textAreaWrapper.mCustomScrollbar({
        scrollInertia: 0,
        advanced: {
          autoScrollOnFocus:false
        }
      });

      textArea.bind('keyup',function(e){
        content = $(this).val();
        var clength = content.length;
        var cursorPosition = textArea.getCursorPosition();
        content = '<span>'+content.substr(0,cursorPosition)+'</span>'+content.substr(cursorPosition,content.length);
        content = content.replace(/\n/g,'<br />');
        hiddenDiv.html(content+'<br />');
        $(this).css('height',hiddenDiv.height());
        textAreaWrapper.mCustomScrollbar('update');
        var hiddenDivSpan = hiddenDiv.children('span'),
          hiddenDivSpanOffset = 0,
          viewLimitBottom = (parseInt(hiddenDiv.css('min-height')))-hiddenDivSpanOffset,
          viewLimitTop = hiddenDivSpanOffset,
          viewRatio = Math.round(hiddenDivSpan.height()+textAreaWrapper.find('.mCSB_container').position().top);
        if ( viewRatio>viewLimitBottom || viewRatio<viewLimitTop ){
          if((hiddenDivSpan.height()-hiddenDivSpanOffset)>0){
            textAreaWrapper.mCustomScrollbar('scrollTo',hiddenDivSpan.height()-hiddenDivSpanOffset);
          } else {
            textAreaWrapper.mCustomScrollbar('scrollTo','top');
          }
        }
      });
    });

    $.fn.getCursorPosition=function(){
      var el=$(this).get(0),
        pos=0;
      if ('selectionStart' in el) {
        pos=el.selectionStart;
      } else if ('selection' in document){
        el.focus();
        var sel=document.selection.createRange(),
          selLength=document.selection.createRange().text.length;
        sel.moveStart('character',-el.value.length);
        pos=sel.text.length-selLength;
      }
      return pos;
    }
  },

  sumoSelect: function() {
    $('.sumo-select select').each(function () {
      $(this).SumoSelect();
    });
  },

  waveForm: function() {
    if ( $('.audio-waveform').length > 0 ) {
      var wavesurfer_arr = [];

      $('.audio-waveform').each(function(i, item) {
        var $player = $(this).parents('.audio-player');

        var wavesurfer = WaveSurfer.create({
          container: item,
          waveColor: '#a7a8a9',
          progressColor: '#36aac4',
          cursorColor: 'rgba(0,157,196,0.29)',
          barWidth: 3,
          barRadius: 3,
          cursorWidth: 3,
          barGap: 3,
          mediaControls: false,
          height: 80,
          responsive: true,
          hideScrollbar: true
        });

        wavesurfer_arr[i] = wavesurfer;

        // The playlist links
        var links = $player.find('.audio-list__items a');
        var currentTrack = 0;

        // Load a track by index and highlight the corresponding link
        var setCurrentSong = function(index) {
          links[currentTrack].classList.remove('active');
          currentTrack = index;
          links[currentTrack].classList.add('active');
          let dataTitle = links[currentTrack].getAttribute('data-title')
          if(dataTitle.length >=  10){
             $('.audio-list__title').prop("title", dataTitle)
             dataTitle = dataTitle.substring(0, 10).concat('...');
          }
          $player.find('.audio-list__title').text(dataTitle);
          wavesurfer.load(links[currentTrack].href);
        };

        // Load the track on click
        Array.prototype.forEach.call(links, function(link, index) {
          link.addEventListener('click', function(e) {
            e.preventDefault();
            setCurrentSong(index);
          });
        });

        // Play on audio load
        wavesurfer.on('ready', function() {
          $player.find('.audio-remain').text( formatTime(wavesurfer.getDuration()) );
          // wavesurfer.play(); // Autoplay
          $player.find('.audio-control__playpause').off().on('click', function(){
            wavesurfer.playPause();

            // Pause all other instances
            $.each(wavesurfer_arr, function(index, item) {
              if ( item !== wavesurfer ) {
                if ( item.isPlaying() ) {
                  item.pause();
                }
              }
            });
          });
          if ( currentTrack > 0 ) {
            $player.find('.audio-control__prev').removeClass('hide').css('display', 'block');
            $player.find('.audio-control__prev').off().on('click', function(){
              setCurrentSong((currentTrack - 1) % links.length);
            });
          } else {
            $player.find('.audio-control__prev').addClass('hide');
          }

          if ( currentTrack < (links.length - 1) ) {
            $player.find('.audio-control__next').removeClass('hide');
            $player.find('.audio-control__next').off().on('click', function(){
              setCurrentSong((currentTrack + 1) % links.length);
            });
          } else {
            $player.find('.audio-control__next').addClass('hide');
          }
        });

        // Check Playpause button
        wavesurfer.on('pause', function () {
          $player.find('.audio-control__playpause').removeClass('active');
        });

        wavesurfer.on('play', function () {
          $player.find('.audio-control__playpause').addClass('active');
        });

        // Display player time
        wavesurfer.on('audioprocess', function () {
          if (wavesurfer.getCurrentTime() > 0 ) {
            $player.find('.audio-remain').text(formatTime(wavesurfer.getCurrentTime()) );
          } else {
            $player.find('.audio-remain').text('0:00');
          }
        });

        var formatTime = function (time) {
          return [
            Math.floor((time % 3600) / 60), // minutes
            ('00' + Math.floor(time % 60)).slice(-2) // seconds
          ].join(':');
        };

        wavesurfer.on('error', function(e) {
          console.warn(e);
        });

        // Go to the next track on finish


        // Load the first track
        if ($('.audio-list__item').length) {
          setCurrentSong(currentTrack);
        }
      });
    }
  },

  orderChart: function() {
    if ( $('.profile__order-chart').length > 0 ) {
      var ctx = $('#order-chart');
      var labels = $('#order-chart').attr('data-labels');
      var values = $('#order-chart').attr('data-values');
      var color = $('#order-chart').attr('data-color');
      labels = labels.split(',');
      values = values.split(',');

      var myChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [{
            label: false,
            data: values,
            backgroundColor: [
              color,
              color,
              color,
              color,
              color,
              color,
              color,
              color,
              color,
              color,
              color,
              color
            ],
            borderColor: [
              color,
              color,
              color,
              color,
              color,
              color,
              color,
              color,
              color,
              color,
              color,
              color
            ],
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          // maintainAspectRatio: false,
          legend: {
            display: false
          },
          tooltips: {
              callbacks: {
                label: function(tooltipItem) {
                  return tooltipItem.yLabel;
                }
              }
          },
          scales: {
            xAxes: [{
              gridLines: {
                display:false
              },
              barPercentage: 0.5,
              ticks: {
                beginAtZero: true
              }
            }],
            yAxes: [{
              display: false,
              gridLines: {
                display:false
              },
              ticks: {
                beginAtZero: true
              }
            }]
          }
        },
        plugins: [{
          beforeInit: function(chart) {
            chart.data.labels.forEach(function(e, i, a) {
              if (/\n/.test(e)) {
                a[i] = e.split(/\n/);
              }
            });
          }
      }]
      });
    }
  },

  orderSort: function() {
    $('.profile__order-arrow').off().on('click', function() {
      $(this).toggleClass('active');
    });
  },

  formSelectRole: function() {
    $('.join-form--select-role').each(function() {
      var $this = $(this);

      if ( $('.join-form__role-item.active', $this).length > 0 ) {
        $('.join-form__action', $this).find('.button').removeClass('disabled');
      } else {
        $('.join-form__action', $this).find('.button').addClass('disabled');
      }

      $('.join-form__role-item').each(function() {
        if ( $(this).hasClass('active') ) {
          $(this).find('.join-form__role-item-info').slideDown(300);
        } else {
          $(this).find('.join-form__role-item-info').hide(300);
        }
      });

      $('.join-form__role-item-title', $this).off().on('click', function() {
        if ( $(this).parent().hasClass('active') ) {
          $(this).parent().removeClass('active');
          $(this).parent().find('.join-form__role-item-info').slideUp(300);
          $('.join-form__action', $this).find('.button').addClass('disabled');
        } else {
          $(this).parent().addClass('active').siblings('.join-form__role-item', $this).removeClass('active');
          $(this).parent().find('.join-form__role-item-info').slideDown(300);
          $(this).parent().siblings('.join-form__role-item', $this).find('.join-form__role-item-info').slideUp(300);
          $('.join-form__action', $this).find('.button').removeClass('disabled');
        }
      });
    });
  },

  saleContentPrice: function() {
    $('.sale-content__price').each(function() {
      var $self = $(this);
      // First Load
      var value = $self.find('select option:selected').val();
      var commision = Math.floor((value * 0.235)/1.1);
      var profit = value - commision;

      $self.find('.sale-content__commission-number').text(formatMoney(commision, ".", ","));
      $self.find('.sale-content__profit-number').text(formatMoney(profit, ".", ","));

      // On change
      $self.find('select').on('change', function() {
        var new_val = $(this).val();
        var new_commision = Math.floor((new_val * 0.235)/1.1);
        var new_profit = new_val - new_commision;

        $self.find('.sale-content__commission-number').text(formatMoney(new_commision, ".", ","));
        $self.find('.sale-content__profit-number').text(formatMoney(new_profit, ".", ","));
      });
    });

    function formatMoney(number, decPlaces, decSep, thouSep) {
      decPlaces = isNaN(decPlaces = Math.abs(decPlaces)) ? 0 : decPlaces,
      decSep = typeof decSep === "undefined" ? "." : decSep;
      thouSep = typeof thouSep === "undefined" ? "," : thouSep;
      var sign = number < 0 ? "-" : "";
      var i = String(parseInt(number = Math.abs(Number(number) || 0).toFixed(decPlaces)));
      var j = (j = i.length) > 3 ? j % 3 : 0;

      return sign +
        (j ? i.substr(0, j) + thouSep : "") +
        i.substr(j).replace(/(\decSep{3})(?=\decSep)/g, "$1" + thouSep) +
        (decPlaces ? decSep + Math.abs(number - i).toFixed(decPlaces).slice(2) : "");
    }
  },

  videoModal: function() {
    SoundcheckProject.modalScrollbar();

    // Drag Order
    SoundcheckProject.treeOrder();

    // Chapter Name double click
    // SoundcheckProject.chapterNameClick();

    // Scene name double click
    SoundcheckProject.sceneNameClick();

    // Variation name double click
    SoundcheckProject.variationNameClick();

    // Click on Version Label
    SoundcheckProject.labelClick();

    // Click on Tree Variation
    SoundcheckProject.treeVariationClick();

    // Click upload button
    SoundcheckProject.uploadButtonClick();
    SoundcheckProject.reUploadScene();

    // Bullets action
    SoundcheckProject.bulletClick();

    // Next action
    SoundcheckProject.nextVideo();

    // Prev action
    SoundcheckProject.prevVideo();

    //seekToChangeThumbnail
    SoundcheckProject.seekToChangeThumbnail();
  },

  modalScrollbar: function() {
    $('.modal-custom-scrollbar').mCustomScrollbar({
      theme: 'minimal-dark'
    });
  },

  treeOrder: function() {
    $('.modal-tree').sortable({
      helper: 'clone',
      forcePlaceholderSize: true,
      start: function(event, ui) {
        var start_pos = ui.item.index();
        ui.item.data('start_pos', start_pos);
      },
      change: function(event, ui) {
        // Do something
      },
      update: function(event, ui) {
        var start_pos = ui.item.data('start_pos');
        var index = ui.item.index();

        // Re Order variation
        var variation_order = $('.modal-tree').attr('data-variation-order').split(',');
        var variation_order_new = SoundcheckProject.arrayMove(variation_order, start_pos, index);
        var variation_order_update = variation_order_new.join();

        $('.modal-tree').attr('data-variation-order', variation_order_update);

        // Re Order bullets
        var bullets_arr = [];
        $('.video-modal .video-item-bullet-list .video-item-bullet').each(function(i) {
          bullets_arr.push($(this));
        });
        var bullets_arr_new = SoundcheckProject.arrayMove(bullets_arr, start_pos, index);
        $('.video-modal .video-item-bullet-list').empty().append(bullets_arr_new);

        // To make next, prev disable work
        $('.modal-tree').removeClass('first-load');

        // Re Bullet click
        SoundcheckProject.bulletClick();

        // Re Next
        SoundcheckProject.nextVideo();
        // Re Prev
        SoundcheckProject.prevVideo();
      }
    });

    $('.modal-tree').draggable();

    // $('.modal-tree').disableSelection();
  },

  arrayMove: function ( arr, old_index, new_index ) {
    if ( new_index >= arr.length ) {
      var k = new_index - arr.length + 1;
      while ( k-- ) {
        arr.push(undefined);
      }
    }
    arr.splice(new_index, 0, arr.splice(old_index, 1)[0]);

    return arr; // for testing
  },

  bulletClick: function() {
    $video_modal = $('.video-modal__videos');
    var min_variation = 0;
    var show_items = 7;
    var bullet_width = 27;
    var max_variation = $video_modal.find('.video-item-bullet').length - 1;
    var translate = 0;

    $video_modal.find('.video-item-bullet').off().on('click', function() {
      //stop all video
      stop_video_audio();

      var current_variation = parseInt($video_modal.find('.video-item-bullet-prev').attr('data-current_variation'));// Use this for translate
      var bullet_variation = $(this).data('variation');

      $(this).addClass('active').siblings('.video-item-bullet').removeClass('active');

      // Display variation video
      $video_modal.find('.video-item-component').removeClass('active');
      $video_modal.find('.video-item-component[data-variation="' + bullet_variation + '"]').addClass('active');

      var max_version = parseInt($video_modal.find('.video-item-component[data-variation="' + bullet_variation + '"]').find('.video-modal-version-item').attr('data-max-version'));

      // Update on video version preview
      $video_modal.find('.video-item-component').find('.video-modal-version-item').removeClass('version-active');
      $video_modal.find('.video-item-component[data-variation="' + bullet_variation + '"]').find('.video-modal-version-item[data-index="' + (max_version - 1) + '"]').addClass('version-active');

      // Update on tree
      $('.modal-tree').find('.video-variation').removeClass('variation-active');
      $('.modal-tree').find('.video-variation').find('.video-version').removeClass('version-active');
      $('.modal-tree').find('.video-variation[data-variation="' + bullet_variation + '"]').addClass('variation-active');
      $('.modal-tree').find('.video-variation[data-variation="' + bullet_variation + '"]').find('.video-version[data-index="' + (max_version - 1) + '"]').addClass('version-active');

      var variation_order = $('.modal-tree').attr('data-variation-order').split(',');
      // Prev Button Data Update
      $video_modal.find('.video-item-bullet-prev').attr('data-current_variation', bullet_variation);
      if ( bullet_variation == variation_order[0] ) {
        $video_modal.find('.video-item-bullet-prev').addClass('disable');
      } else {
        $video_modal.find('.video-item-bullet-prev').removeClass('disable');
      }
      // Next Button Data Update
      $video_modal.find('.video-item-bullet-next').attr('data-current_variation', bullet_variation);
      if ( bullet_variation == variation_order[variation_order.length - 1] ) {
        $video_modal.find('.video-item-bullet-next').addClass('disable');
      } else {
        $video_modal.find('.video-item-bullet-next').removeClass('disable');
      }

      // Translate bullet only when > show_items
      if ( $video_modal.find('.video-item-bullet').length > show_items ) {
        var variation_position = variation_order.indexOf(current_variation.toString());
        var new_variation_position = variation_order.indexOf(bullet_variation.toString());

        if ( (new_variation_position > (max_variation - show_items)) && (new_variation_position > variation_position) ) {
          translate =  -(max_variation - show_items + 1) * bullet_width; // Change 15 to other value if CSS change
          $video_modal.find('.video-item-bullet').css('transform', 'translateX(' + translate + 'px)');
        }

        if ( (new_variation_position < show_items) && (new_variation_position < variation_position) ) {
          translate =  0; // @TODO
          $video_modal.find('.video-item-bullet').css('transform', 'translateX(' + translate + 'px)');
        }
      }
    });
  },

  nextVideo: function() {
    var variation_order = $('.modal-tree').attr('data-variation-order').split(',');

    $video_modal = $('.video-modal__videos');
    var min_variation = 0;
    var show_items = 7;
    var bullet_width = 27;
    var max_variation = $video_modal.find('.video-item-bullet').length - 1;
    var translate = 0;

    // Run only when reorder
    if ( $('.modal-tree').hasClass('first-load') ) {
      if ( $('.modal-tree').find('.video-variation:last').hasClass('variation-active') ) {
        $video_modal.find('.video-item-bullet-next').addClass('disable');
      } else {
        $video_modal.find('.video-item-bullet-next').removeClass('disable');
      }
    }

    $video_modal.find('.video-item-bullet-next').off().on('click', function() {
      //stop all video
      stop_video_audio();

      var current_variation = parseInt($(this).attr('data-current_variation'));

      if ( current_variation == parseInt(variation_order[variation_order.length - 2]) ) {
        $(this).addClass('disable');
      }

      var variation_position = variation_order.indexOf(current_variation.toString());
      var new_variation = variation_order[variation_position + 1];
      $(this).attr('data-current_variation', new_variation);
      // Display variation videos
      $video_modal.find('.video-item-component').removeClass('active');
      $video_modal.find('.video-item-component[data-variation="' + new_variation + '"]').addClass('active');

      var max_version = parseInt($video_modal.find('.video-item-component[data-variation="' + new_variation + '"]').find('.video-modal-version-item').attr('data-max-version'));

      // Update on video version preview
      $video_modal.find('.video-item-component').find('.video-modal-version-item').removeClass('version-active');
      $video_modal.find('.video-item-component[data-variation="' + new_variation + '"]').find('.video-modal-version-item[data-index="' + (max_version - 1) + '"]').addClass('version-active');

      // Update on tree
      $('.modal-tree').find('.video-variation').removeClass('variation-active');
      $('.modal-tree').find('.video-variation').find('.video-version').removeClass('version-active');
      $('.modal-tree').find('.video-variation[data-variation="' + new_variation + '"]').addClass('variation-active');
      $('.modal-tree').find('.video-variation[data-variation="' + new_variation + '"]').find('.video-version[data-index="' + (max_version - 1) + '"]').addClass('version-active');

      // Update Bullets
      $video_modal.find('.video-item-bullet').removeClass('active');
      $video_modal.find('.video-item-bullet[data-variation="' + new_variation + '"]').addClass('active');

      // Translate only when > show_items
      if ( $video_modal.find('.video-item-bullet').length > show_items ) {
        var variation_position = variation_order.indexOf(current_variation.toString());
        var new_variation_position = variation_order.indexOf(new_variation.toString());
        var offset_variation = max_variation - variation_position;

        if ( new_variation_position <= (max_variation - show_items) ) {
          translate = 0;
          $video_modal.find('.video-item-bullet').css('transform', 'translateX(' + translate + 'px)');
        } else {
          translate =  -(max_variation - show_items + 1) * bullet_width; // Change 15 to other value if CSS change
          $video_modal.find('.video-item-bullet').css('transform', 'translateX(' + translate + 'px)');
        }
      }

      // Update Prev
      $video_modal.find('.video-item-bullet-prev').removeClass('disable');
      $video_modal.find('.video-item-bullet-prev').attr('data-current_variation', new_variation);
    });
  },

  prevVideo: function() {
    var variation_order = $('.modal-tree').attr('data-variation-order').split(',');
    $video_modal = $('.video-modal__videos');
    var min_variation = 0;
    var show_items = 7;
    var bullet_width = 27;
    var max_variation = $video_modal.find('.video-item-bullet').length - 1;
    var translate = 0;

    // Run only when reorder
    if ( ! $('.modal-tree').hasClass('first-load') ) {
      if ( ! $('.modal-tree').find('.video-variation:first').hasClass('variation-active') ) {
        $video_modal.find('.video-item-bullet-prev').removeClass('disable');
      } else {
        $video_modal.find('.video-item-bullet-prev').addClass('disable');
      }
    }

    $video_modal.find('.video-item-bullet-prev').off().on('click', function() {
      //stop all video
      stop_video_audio();

      var current_variation = parseInt($(this).attr('data-current_variation'));

      if ( current_variation == parseInt(variation_order[1]) ) {
        $(this).addClass('disable');
      }

      var variation_position = variation_order.indexOf(current_variation.toString());
      var new_variation = variation_order[variation_position - 1];
      $(this).attr('data-current_variation', new_variation);
      // Display variation videos
      $video_modal.find('.video-item-component').removeClass('active');
      $video_modal.find('.video-item-component[data-variation="' + new_variation + '"]').addClass('active');

      var max_version = parseInt($video_modal.find('.video-item-component[data-variation="' + new_variation + '"]').find('.video-modal-version-item').attr('data-max-version'));

      // Update on video version preview
      $video_modal.find('.video-item-component').find('.video-modal-version-item').removeClass('version-active');
      $video_modal.find('.video-item-component[data-variation="' + new_variation + '"]').find('.video-modal-version-item[data-index="' + (max_version - 1) + '"]').addClass('version-active');

      // Update on tree
      $('.modal-tree').find('.video-variation').removeClass('variation-active');
      $('.modal-tree').find('.video-variation').find('.video-version').removeClass('version-active');
      $('.modal-tree').find('.video-variation[data-variation="' + new_variation + '"]').addClass('variation-active');
      $('.modal-tree').find('.video-variation[data-variation="' + new_variation + '"]').find('.video-version[data-index="' + (max_version - 1) + '"]').addClass('version-active');

      // Update Bullets
      $video_modal.find('.video-item-bullet').removeClass('active');
      $video_modal.find('.video-item-bullet[data-variation="' + new_variation + '"]').addClass('active');

      // Translate only when > show_items
      if ( $video_modal.find('.video-item-bullet').length > show_items ) {
        var variation_position = variation_order.indexOf(current_variation.toString());
        var new_variation_position = variation_order.indexOf(new_variation.toString());
        var offset_variation = max_variation - variation_position;

        if ( (new_variation_position >= show_items) && (new_variation_position >= (max_variation - show_items)) ) {
          translate =  -(max_variation - show_items + 1) * bullet_width; // Change 15 to other value if CSS change
          $video_modal.find('.video-item-bullet').css('transform', 'translateX(' + translate + 'px)');
        } else {
          translate = 0;
          $video_modal.find('.video-item-bullet').css('transform', 'translateX(' + translate + 'px)');
        }
      }

      // Update Next
      $video_modal.find('.video-item-bullet-next').removeClass('disable');
      $video_modal.find('.video-item-bullet-next').attr('data-current_variation', new_variation);
    });
  },

  labelClick: function() {
    $('.video-modal-component-content-video').each(function() {
      $(this).find('.video-modal-version').on('click', function() {
        var version_current_item = $(this).parents('.video-modal-version-item').removeClass('version-active');
        var version_current_index = parseInt(version_current_item.attr('data-index'));
        var max_version = parseInt(version_current_item.attr('data-max-version'));

        if ( version_current_index > 0 ) {
          var version_next_index = version_current_index - 1;
        } else {
          var version_next_index = max_version - 1;
        }

        $(this).parents('.video-modal-component-content-video').find('.video-modal-version-item[data-index="' + version_next_index + '"]').addClass('version-active');

        // Update on tree
        var variation_current = parseInt(version_current_item.attr('data-variation'));
        var variation_current_tree = $('.modal-tree').find('.video-variation[data-variation="' + variation_current + '"]');

        variation_current_tree.find('.video-version').removeClass('version-active');
        variation_current_tree.find('.video-version[data-index="' + version_next_index + '"]').addClass('version-active');
      });
    });
  },

  treeVariationClick: function() {
    $('.video-variation').each(function() {
      $(this).find('.video-version span').on('click', function() {
        //stop all video
        stop_video_audio();
        // Click on other variation
        $(this).parents('.modal-tree').find('.video-variation').removeClass('variation-active');
        $(this).parents('.modal-tree').find('.video-version').removeClass('version-active');
        $(this).parents('.video-variation').addClass('variation-active');

        // Click on current variation
        $(this).parents('.video-variation').find('.video-version').removeClass('version-active');
        $(this).parents('.video-version').addClass('version-active');

        // Update on Preview
        var variation_tree = $(this).parents('.video-version');
        var variation_tree_id = parseInt(variation_tree.attr('data-variation'));
        var index_tree = parseInt(variation_tree.attr('data-index'));
        $('.video-modal__videos .video-item-list').find('.video-item-component').removeClass('active');
        $('.video-modal__videos .video-item-list').find('.video-item-component[data-variation="' + variation_tree_id + '"]').addClass('active');
        $('.video-modal__videos .video-item-list').find('.video-item-component').find('.video-modal-version-item').removeClass('version-active');
        $('.video-modal__videos .video-item-list').find('.video-item-component[data-variation="' + variation_tree_id + '"]').find('.video-modal-version-item[data-index="' + index_tree + '"]').addClass('version-active');

        // Update bullets
        var variation_bullet_id = parseInt($('.video-modal__videos .video-item-bullet-list').find('.video-item-bullet.active').attr('data-variation'));
        $('.video-modal__videos .video-item-bullet-list').find('.video-item-bullet').removeClass('active');
        $('.video-modal__videos .video-item-bullet-list').find('.video-item-bullet[data-variation="' + variation_tree_id + '"]').addClass('active');

        if ( variation_bullet_id != variation_tree_id ) {
          $('.video-modal__videos .video-item-bullet-list').find('.video-item-bullet[data-variation="' + variation_tree_id + '"]').trigger('click');
        }

        //stop all video
        $('video').each(function (){
          $(this).get(0).pause();
        });

        //clear thumbnail has been set
        $('.canvas #id_scene_id, .canvas #id_thumbnail_base64').val('');

        // Check Prev Next (after add variation and order)
        SoundcheckProject.nextVideo();
        SoundcheckProject.prevVideo();
      });
    });
  },

  chapterNameClick: function() {
    $('.chapter-name span').on('dblclick', function() {
      var $this = $(this);
      var old_val = $(this).text();
      $this.html('<input type="text" class="new-chapter-name" value="' + old_val + '">');
      // Focus to last character
      var ChapterInput = $this.find('.new-chapter-name');
      ChapterInput.val(ChapterInput.val());
      var strLength= ChapterInput.val().length;
      ChapterInput[0].setSelectionRange(strLength, strLength);
      ChapterInput.focus();

      $('.new-chapter-name').focus(function() {
        // Do something
      }).blur(function() {
        var new_val = $('.new-chapter-name').val().trim();
        if (!new_val) {
          $this.text(old_val);
        } else {
          $this.text(new_val);
        }

        $('.video-modal-chapter').text($this.text());
      });

      // Process click on tree doesn't blur
      $('.video-modal__tree').on('click', function() {
        $('.new-chapter-name').trigger('blur');
      });

      //check when input

      $this.on('input', 'input', function (e) {
        e.stopPropagation();
        let chapter_val = $(this).val().trim();
        let error_message_els = $(this).parents('.chapter-name').find('.input-field-error');

        if (!chapter_val.trim()) {
          error_message_els.first().text('このフィールドは必須項目です。');
          error_message_els.first().removeClass('hide');
          $(this).focus();
        } else {
          error_message_els.first().addClass('hide');
          $(this).focus();
        }


      });

      $this.off('change', 'input').on('change', 'input', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let chapter_text = $(this).val().trim();
        let product_scene_id = $(this).parents('.chapter-name').first().attr('data-product-scene-id').trim();
        let error_message_els = $(this).parents('.chapter-name').find('.input-field-error');
        if (!chapter_text) {
          $(this).val(old_val)
          error_message_els.addClass('hide');
          return
        }

        $.ajax({
          url: '/top/update_name_for_productscene',
          type: 'POST',
          data: {
            'product_scene_id': product_scene_id,
            'title': chapter_text
          },
          beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            // toastr.success(response.message);
            $('.video-modal-chapter').text(chapter_text);
          },
          error: function (response) {
            toastr.error(response.responseJSON.message);
          }
        });

      });
    });

    // Mobile
    $('.chapter-edit').on('click', function(e) {
      e.stopPropagation();

      var $this = $(this).parents('.chapter-name').find('>span');
      var old_val = $this.text();
      $this.html('<input type="text" class="new-chapter-name" value="' + old_val + '">');

       $this.on('input', 'input', function (e) {
        e.stopPropagation();
        let chapter_val = $(this).val().trim();
        let error_message_els = $(this).parents('.chapter-name').find('.input-field-error');

        if (!chapter_val.trim()) {
          error_message_els.first().text('このフィールドは必須項目です。');
          error_message_els.first().removeClass('hide');
          $(this).focus();
        } else {
          error_message_els.first().addClass('hide');
          $(this).focus();
        }


      });

      $this.off('change', 'input').on('change', 'input', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let chapter_text = $(this).val().trim();
        let product_scene_id = $(this).parents('.chapter-name').first().attr('data-product-scene-id').trim();
        let error_message_els = $(this).parents('.chapter-name').find('.input-field-error');
        if (!chapter_text) {
          $(this).val(old_val)
          error_message_els.addClass('hide');
          return
        }

        $.ajax({
          url: '/top/update_name_for_productscene',
          type: 'POST',
          data: {
            'product_scene_id': product_scene_id,
            'title': chapter_text
          },
          beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            // toastr.success(response.message);
            $('.video-modal-chapter').text(chapter_text);
          },
          error: function (response) {
            toastr.error(response.responseJSON.message);
          }
        });

      });

      // Focus to last character
      var ChapterInput = $this.find('.new-chapter-name');
      ChapterInput.val(ChapterInput.val());
      var strLength= ChapterInput.val().length;
      ChapterInput[0].setSelectionRange(strLength, strLength);
      ChapterInput.focus();

      $('.new-chapter-name').focus(function() {
        // Do something
      }).blur(function() {
        var new_val = $('.new-chapter-name').val();
        $this.text(new_val);

        $('.video-modal-chapter').text(new_val);
      });

      // Process click on tree doesn't blur
      $('.video-modal__tree').on('click', function() {
        $('.new-chapter-name').trigger('blur');
      });


    });
  },

  sceneNameClick: function() {
    $('.scene-name span').on('dblclick', function() {
      var $this = $(this);
      var old_val = $(this).text();
      $this.html('<input type="text" class="new-scene-name" value="' + old_val + '">');
      //get exists titles
      let exists_title_el = $(this).parents('.scene-name').find('#exists-title-vals .title');
      var exists_title_vals = [];
        exists_title_el.each(function (index, item) {
            exists_title_vals.push($(item).text());
        });
      //check when input
      $(this).on('input', 'input', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let title_val = $(this).val().trim();
        let error_message_els = $(this).parents('.scene-name').find('.input-field-error');
        if (exists_title_vals.includes(title_val)) {
          error_message_els.first().text('このタイトルは存在しました');
          error_message_els.first().removeClass('hide');
          $(this).focus();
        } else {
          if (!title_val){
            error_message_els.text('このフィールドは必須項目です。');
            error_message_els.removeClass('hide');
            $(this).focus();
          }
          else{
            error_message_els.addClass('hide');
            $(this).focus();
          }
        }
      });
      //update when change
      $(this).off('change', 'input').on('change', 'input', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let scene_title_text = $(this).val().trim();
        let scene_title_id = $(this).parents('.scene-name').first().attr('data-scene-title-id').trim();
        let error_message_els = $(this).parents('.scene-name').find('.input-field-error');
        if (exists_title_vals.includes(scene_title_text) || !scene_title_text) {
          $(this).val(old_val)
          error_message_els.addClass('hide');
          return
        }

        $.ajax({
          url: '/top/update_title_for_scenetitle',
          type: 'POST',
          data: {
            'scenetitle_id': scene_title_id,
            'title': scene_title_text
          },
          beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            // toastr.success(response.message);
            $('.video-modal-scene').text(scene_title_text);
          },
          error: function (response) {
            toastr.error(response.responseJSON.message);
          }
        });
      });
      // Focus to last character
      var ChapterInput = $this.find('.new-scene-name');
      ChapterInput.val(ChapterInput.val());
      var strLength= ChapterInput.val().length;
      ChapterInput[0].setSelectionRange(strLength, strLength);
      ChapterInput.focus();

      $('.new-scene-name').focus(function() {
        // Do something
      }).blur(function() {
        var new_val = $('.new-scene-name').val();
        $this.text(new_val);
      });

      // Process click on tree doesn't blur
      $('.video-modal__tree').on('click', function() {
        $('.new-scene-name').trigger('blur');
      });
    });

    // Mobile
    $('.scene-edit').on('click', function(e) {
      e.stopPropagation();

      var $this = $(this).parents('.scene-name').find('>span');
      var old_val = $this.text();
      $this.html('<input type="text" class="new-scene-name" value="' + old_val + '">');

      //get exists titles
      let exists_title_el = $(this).parents('.scene-name').find('#exists-title-vals .title');
      var exists_title_vals = [];
      exists_title_el.each(function (index, item) {
        exists_title_vals.push($(item).text());
      });
      //check when input
      $this.on('input', 'input', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let title_val = $(this).val().trim();
        let error_message_els = $(this).parents('.scene-name').find('.input-field-error');
        if (exists_title_vals.includes(title_val)) {
          error_message_els.first().text('このタイトルは存在しました');
          error_message_els.first().removeClass('hide');
          $(this).focus();
        } else {
          if (!title_val) {
            error_message_els.text('このフィールドは必須項目です。');
            error_message_els.removeClass('hide');
            $(this).focus();
          } else {
            error_message_els.addClass('hide');
            $(this).focus();
          }
        }
      });
      //update when change
      $this.off('change', 'input').on('change', 'input', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let scene_title_text = $(this).val().trim();
        let scene_title_id = $(this).parents('.scene-name').first().attr('data-scene-title-id').trim();
        let error_message_els = $(this).parents('.scene-name').find('.input-field-error');
        if (exists_title_vals.includes(scene_title_text) || !scene_title_text) {
          $(this).val(old_val)
          error_message_els.addClass('hide');
          return
        }

        $.ajax({
          url: '/top/update_title_for_scenetitle',
          type: 'POST',
          data: {
            'scenetitle_id': scene_title_id,
            'title': scene_title_text
          },
          beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            // toastr.success(response.message);
            $('.video-modal-scene').text(scene_title_text);
          },
          error: function (response) {
            toastr.error(response.responseJSON.message);
          }
        });
      });

      // Focus to last character
      var ChapterInput = $('.new-scene-name');
      ChapterInput.val(ChapterInput.val());
      var strLength= ChapterInput.val().length;
      ChapterInput[0].setSelectionRange(strLength, strLength);
      ChapterInput.focus();

      $('.new-scene-name').focus(function() {
        // Do something
      }).blur(function() {
        var new_val = $('.new-scene-name').val().trim();
        if (!new_val) {
          $this.text(old_val);
        } else {
          $this.text(new_val);
        }
      });

      // Process click on tree doesn't blur
      $('.video-modal__tree').on('click', function() {
        $('.new-scene-name').trigger('blur');
      });
    });
  },

  variationNameClick: function(){
    // Process click on tree doesn't blur
    $('.video-version').on('click', function (e) {
      e.preventDefault();
      e.stopPropagation();
      $('.new-variation-name').trigger('blur');
    });

    $('.video-version').off('click', 'button#edit-comment').on('click', 'button#edit-comment', function(e){
      e.stopPropagation();
      let $this = $(this).parents('.video-version').find('span').first();
      $this.trigger('click');
      let old_val = $this.text();
      $this.html('<input type="text" class="new-variation-name" value="' + old_val + '">');

      // Focus to last character
      let VariationInput = $this.find('.new-variation-name');
      VariationInput.val(VariationInput.val());
      let strLength = VariationInput.val().length;
      VariationInput[0].setSelectionRange(strLength, strLength);
      VariationInput.focus();

      VariationInput.focus(function () {
        // Do something
      }).blur(function () {
        let new_val = VariationInput.val().trim();
        if (!new_val) {
          $this.text(old_val);
        } else {
          $this.text(new_val);
        }
      });
      //check when input
      $this.on('input', 'input', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let title_val = $(this).val().trim();
        let error_message_els = $(this).parents('.video-version').find('.input-field-error');

        if (!title_val.trim()) {
          error_message_els.first().text('このフィールドは必須項目です。');
          error_message_els.first().removeClass('hide');
          $(this).focus();
        } else {
          error_message_els.first().addClass('hide');
          $(this).focus();
        }
      });

      //send data to server
      $this.off('change', 'input').on('change', 'input', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let version_text_span = $(this).parent();
        let scene_text = $(this).val().trim();
        let scene_id = $(this).parents('.video-version').first().attr('data-scene-id').trim();
        let error_message_els = $(this).parents('.video-version').find('.input-field-error');
        if (!scene_text) {
          $(this).val(old_val);
          error_message_els.first().addClass('hide');
          return
        }

        $.ajax({
          url: '/top/update_name_for_variation',
          type: 'POST',
          data: {
            'scene_id': scene_id,
            'name': scene_text
          },
          beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            // toastr.success(response.message);
            let version_item_el = version_text_span.parents('.modal-body').find('.video-modal-version-item[data-scene-id="' + scene_id + '"]');
            if (version_item_el.length > 0){
              version_item_el.find('.video-modal-info-detail .video-modal-version-text').text(scene_text);
            }
          },
          error: function (response) {
            toastr.error(response.responseJSON.message);
            version_text_span.text(old_val);
          }
        });
      });
    });

    $('.video-version').off('click', 'button#delete-comment').on('click', 'button#delete-comment', function(e){
      e.stopPropagation();
      e.preventDefault();
      let $span_el = $(this).parents('.video-version').find('span').first();
      let scene_id = $(this).parents('.video-version').attr('data-scene-id');
      $span_el.trigger('click');
      deleteSceneOnVideoModal($(this));
    });
  },

  uploadButtonClick: function() {

    let resizeImg = function (width, height) {

      let targetWidth = 480;
      if (width <= targetWidth) {
        return {width: width, height: height}
      }

      let ratio = targetWidth / width;

      return {
        width: parseInt(width * ratio),
        height: parseInt(height * ratio)
      }
    };

    let getThumbnail = function (_CANVAS_RESIZE, _CTX_RESIZE, _CTX, _VIDEO) {
      let imgResize = resizeImg(_VIDEO.videoWidth, _VIDEO.videoHeight);
      _CANVAS_RESIZE.width = imgResize.width;
      _CANVAS_RESIZE.height = imgResize.height;
      _CTX_RESIZE.drawImage(_VIDEO, 0, 0, imgResize.width, imgResize.height);

      _CTX.drawImage(_VIDEO, 0, 0, _VIDEO.videoWidth, _VIDEO.videoHeight);
    }

    let thumbnail_base64 = null;
    // Check duplicate file select
    var file_temp = [];
    var url_temp = [];
    let filename_temp = [];
    let video_modal_el = $('#video-modal')
    if ( $('.variation-upload').length > 0 ) {
      video_modal_el.off('click', '.variation-upload__link').on('click', '.variation-upload__link', function (e) {
        e.preventDefault();
        e.stopPropagation();

        var variation_upload = $(this).parents('.variation-upload');
        var current_video_variation = $(this).parents('.video-variation');
        variation_upload.find('.variation-upload__file-input:hidden').trigger('click');

        //action on change file
        variation_upload.find('.variation-upload__file-input:hidden').off().on('change', function(e){
          e.preventDefault();
          e.stopPropagation();

          //disable save button
          $(this).parents('.modal-content').find('.video-modal-btn.video-save-btn').removeClass('active').addClass('hide');

          //value for upload
          var current_variation_id = $(this).parents('.variation-upload').data('variation-id');
          let file_input_element = this;
          let scene_title_id = $(this).parents('.video-modal__left').find('.scene-name').first().data('scene-title-id');
          // Order variation
          var variation_order = $('.modal-tree').attr('data-variation-order').split(',');
          var variation_id = parseInt(variation_upload.attr('data-variation'));
          var total_variation = variation_order.length;

          let filename_uploaded = '';
          let video_upload_url = '';
          //signature
          let signature = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);

          // Check duplicate file select
          if ( file_temp.indexOf(this.files[0]) !== -1 ) {
            let file_index = file_temp.indexOf(this.files[0]);
            video_upload_url = url_temp[file_index];
            filename_uploaded = file_temp[file_index]
          } else {
            file_temp.push(this.files[0]);
            video_upload_url = URL.createObjectURL(this.files[0]);
            url_temp.push(video_upload_url);
            let array_name = this.files[0].name.split('.');
            if (array_name.length > 2) {
                array_name.splice(-1);
                filename_uploaded = array_name.join('.');
            } else {
                filename_uploaded = array_name[0];
            }
            filename_temp.push(filename_uploaded);
          }

          if ( variation_upload.hasClass('variation-upload--after') || variation_upload.hasClass('variation-upload--before') ) {
            // Add variation
            var new_variation = `<li class="video-variation ui-sortable-handle" data-variation="${total_variation}" data-signature="${signature}">
                <div class="variation-upload" data-variation="${total_variation}">
                <input class="variation-upload__file-input" type="file" accept=".mp4, .x-m4v, .webm, video/*, .pdf, .mp3, .wav">
                <a class="button button--icon button--icon-plus button--round variation-upload__link" href="#" role="button"></a>
                </div>
                <ul>
                <li class="video-version left" data-variation="${total_variation}" data-index="0" data-max-version="1" data-signature="${signature}">
                <span>${filename_uploaded}</span>
                <div class="comment-edit-button-group">
                                <button id="edit-comment">
                                    <img src="/static/images/edit.svg" alt="">
                                </button>
                                <button id="delete-comment" data-scene-id="">
                                    <img src="/static/images/delete.svg" alt="">
                                </button>
                            </div>
                </li>
                </ul>
                <div class="variation-upload variation-upload--after" data-variation="${total_variation}">
                <input class="variation-upload__file-input" type="file" accept=".mp4, .x-m4v, .webm, video/*, .pdf, .mp3, ,wav">
                <a class="button button--icon button--icon-plus button--round variation-upload__link" href="#" role="button"></a>
                </div>
                </li>`;
            if (isNaN(variation_id)) {
              // Add to first
              $('.modal-tree').prepend(new_variation);
            } else {
              $('.modal-tree').find('>li[data-variation="' + variation_id + '"]').after(new_variation);
            }

            $('.modal-tree').sortable('refresh');
            // Add bullet
            if ( new_variation ) {
              var new_bullet = '<div class="video-item-bullet" data-variation="' + total_variation + '"></div>';
              if ( isNaN(variation_id) ) {
                // Add to first
                $('.video-item-bullets').find('.video-item-bullet-list').prepend(new_bullet);
              } else {
                $('.video-item-bullets').find('.video-item-bullet[data-variation="' + variation_id + '"]').after(new_bullet);
              }

              // Update tree arr
              if ( isNaN(variation_id) ) {
                // Add to first
                variation_order.unshift(total_variation.toString());
                // Check Prev Next (after add variation and order)
                $('.modal-tree').removeClass('first-load');
              } else {
                var variation_id_index = variation_order.indexOf(variation_id.toString());
                variation_order.splice((variation_id_index + 1), 0, total_variation.toString());
              }

              var variation_order_new = variation_order.join();
              $('.modal-tree').attr('data-variation-order', variation_order_new);
            }

            // Add video content
            if ( new_variation ) {
                var video_component;
                if (this.files[0].name.match(/\.(pdf)$/)) {
                    video_component = ` <div class="video-item-component" data-variation="${total_variation}" data-signature="${signature}">
                                      <div class="video-modal-component-content">
                                        <div class="video-modal-component-content-video">
                                          <div class="video-modal-version-item" data-variation="${total_variation}" data-index="0" data-max-version="1" data-signature="${signature}">
                                            <div class="video-modal-info-detail">
                                              <div class="video-modal-breadcrumbs">
                                                <span class="video-modal-chapter">${$('.chapter-name span').text()}</span>
                                                <span>&gt;</span>
                                                <span class="video-modal-scene">${$('.scene-name >span').text()}</span>
                                              </div>
                                              <div class="video-modal-version-text">${filename_uploaded}</div>
                                            </div>
                                            <div class="video-modal-version">1</div>
                                            <iframe width="100%" height="600px" src="${video_upload_url}" >
                                            </iframe>
                                          </div>
                                        </div>
                                      </div>
                                    </div>`;
                } else {
                    video_component =` <div class="video-item-component" data-variation="${total_variation}" data-signature="${signature}">
                                      <div class="video-modal-component-content">
                                        <div class="video-modal-component-content-video">
                                          <div class="video-modal-version-item" data-variation="${total_variation}" data-index="0" data-max-version="1" data-signature="${signature}">
                                            <div class="video-modal-info-detail">
                                              <div class="video-modal-breadcrumbs">
                                                <span class="video-modal-chapter">${$('.chapter-name span').text()}</span>
                                                <span>&gt;</span>
                                                <span class="video-modal-scene">${$('.scene-name >span').text()}</span>
                                              </div>
                                              <div class="video-modal-version-text">${filename_uploaded}</div>
                                            </div>
                                            <div class="video-modal-version">1</div>
                                            <video width="auto" height="90%" preload="auto" controls >
                                              <source src="${video_upload_url}" type="video/mp4">
                                            </video>
                                          </div>
                                        </div>
                                      </div>
                                    </div>`;
                }

              $('.video-modal .video-item-list').append(video_component);
            }

            SoundcheckProject.treeOrder();
            SoundcheckProject.bulletClick();
            SoundcheckProject.nextVideo();
            SoundcheckProject.prevVideo();
            SoundcheckProject.labelClick();

            // Click on Tree Variation
            SoundcheckProject.treeVariationClick();
          } else {
            var new_index = parseInt(current_video_variation.find('.video-version').attr('data-max-version'));
            // Add version
            var new_version = `<li class="video-version left" data-variation="${variation_id}" data-index="${new_index}" data-max-version="${new_index + 1}" data-signature="${signature}">
                <span>${filename_uploaded}</span>
                <div class="comment-edit-button-group">
                                <button id="edit-comment">
                                    <img src="/static/images/edit.svg" alt="">
                                </button>
                                <button id="delete-comment" data-scene-id="">
                                    <img src="/static/images/delete.svg" alt="">
                                </button>
                            </div>
                </li>`;
            current_video_variation.find('> ul').prepend(new_version);

            // Add video content
            if ( new_version ) {
                var video_version = '';
                if (this.files[0].name.match(/\.(pdf)$/)) {
                    video_version = `<div class="video-modal-version-item" data-variation="${variation_id}" data-index="${new_index}" data-max-version="${new_index + 1}" data-signature="${signature}">
                                    <div class="video-modal-info-detail">
                                      <div class="video-modal-breadcrumbs">
                                        <span class="video-modal-chapter">${$('.chapter-name span').text()}</span>
                                        <span>&gt;</span>
                                        <span class="video-modal-scene">${$('.scene-name >span').text()}</span>
                                      </div>
                                      <div class="video-modal-version-text">${filename_uploaded}</div>
                                    </div>
                                    <div class="video-modal-version">${new_index + 1}</div>
                                    <iframe width="100%" height="600px" src="${video_upload_url}">
                                    </iframe>
                                  </div>`;
                } else {
                    video_version = `<div class="video-modal-version-item" data-variation="${variation_id}" data-index="${new_index}" data-max-version="${new_index + 1}" data-signature="${signature}">
                                    <div class="video-modal-info-detail">
                                      <div class="video-modal-breadcrumbs">
                                        <span class="video-modal-chapter">${$('.chapter-name span').text()}</span>
                                        <span>&gt;</span>
                                        <span class="video-modal-scene">${$('.scene-name >span').text()}</span>
                                      </div>
                                      <div class="video-modal-version-text">${filename_uploaded}</div>
                                    </div>
                                    <div class="video-modal-version">${new_index + 1}</div>
                                    <video width="auto" height="90%" preload="auto" controls>
                                      <source src="${video_upload_url}" type="video/mp4">
                                    </video>
                                  </div>`;
                }

              $('.video-modal .video-item-component[data-variation=' + variation_id + ']').find('.video-modal-component-content-video').prepend(video_version);

              // Update data-max-version
              current_video_variation.find('.video-version').attr('data-max-version', (new_index + 1).toString());
              $('.video-modal .video-item-component[data-variation=' + variation_id + ']').find('.video-modal-version-item').attr('data-max-version', (new_index + 1).toString());
            }

            SoundcheckProject.labelClick();
            // Click on Tree Variation
            SoundcheckProject.treeVariationClick();
            // SoundcheckProject.uploadButtonClick();
          }
          SoundcheckProject.variationNameClick();

          //canvas to get default thumbnail
          $(this).parent().append(`<div class="canvas hide">
                    <canvas id="video-canvas-upload"></canvas>
                    <canvas id="video-canvas-resize-upload" class="hide"></canvas>
                </div>`);

          let variation_upload_el = $(this);
          let _VIDEO = $('.video-modal .video-item-list').find('.video-modal-version-item[data-signature=' + signature + '] video').get(0);
            if (_VIDEO) {
                $(_VIDEO).on('canplay', function () {
                    $(_VIDEO).off('canplay');
                    let _CANVAS_DOM = variation_upload_el.parent().find('.canvas')
                    let _CANVAS = _CANVAS_DOM.find("canvas#video-canvas-upload").get(0);
                    let _CANVAS_RESIZE = _CANVAS_DOM.find("canvas#video-canvas-resize-upload").get(0);
                    let _CTX = _CANVAS.getContext("2d");
                    let _CTX_RESIZE = _CANVAS_RESIZE.getContext("2d");
                    // Set canvas dimensions same as video dimensions
                    _CANVAS.width = _VIDEO.videoWidth;
                    _CANVAS.height = _VIDEO.videoHeight;
                    let timeout = 100;
                    getThumbnail(_CANVAS_RESIZE, _CTX_RESIZE, _CTX, _VIDEO);
                    thumbnail_base64 = _CANVAS_RESIZE.toDataURL();
                    //Upload file to sever
                    if (variation_upload.hasClass('variation-upload--after') || variation_upload.hasClass('variation-upload--before')) {
                        SoundcheckProject.uploadDataToServer('/top/upload_scene_for_scenetitle', file_input_element, thumbnail_base64, scene_title_id, signature);
                    } else {
                        SoundcheckProject.uploadDataToServer('/top/upload_scene_for_scenetitle', file_input_element, thumbnail_base64, scene_title_id, signature, current_variation_id);
                    }
                });

                setTimeout(function () {
                    if (_VIDEO.readyState !== 4) {
                        $(_VIDEO).off('canplay');
                        thumbnail_base64 = '';
                        if (variation_upload.hasClass('variation-upload--after') || variation_upload.hasClass('variation-upload--before')) {
                            SoundcheckProject.uploadDataToServer('/top/upload_scene_for_scenetitle', file_input_element, thumbnail_base64, scene_title_id, signature);
                        } else {
                            SoundcheckProject.uploadDataToServer('/top/upload_scene_for_scenetitle', file_input_element, thumbnail_base64, scene_title_id, signature, current_variation_id);
                        }
                    }

                }, 2000)
            } else {
                thumbnail_base64 = '';
                if (variation_upload.hasClass('variation-upload--after') || variation_upload.hasClass('variation-upload--before')) {
                    SoundcheckProject.uploadDataToServer('/top/upload_scene_for_scenetitle', file_input_element, thumbnail_base64, scene_title_id, signature);
                } else {
                    SoundcheckProject.uploadDataToServer('/top/upload_scene_for_scenetitle', file_input_element, thumbnail_base64, scene_title_id, signature, current_variation_id);
                }
            }

        });
      });
    }
  },


    reUploadScene: function () {
        let resizeImg = function (width, height) {

            let targetWidth = 480;
            if (width <= targetWidth) {
                return {width: width, height: height}
            }

            let ratio = targetWidth / width;

            return {
                width: parseInt(width * ratio),
                height: parseInt(height * ratio)
            }
        };

        let getThumbnail = function (_CANVAS_RESIZE, _CTX_RESIZE, _CTX, _VIDEO) {
            let imgResize = resizeImg(_VIDEO.videoWidth, _VIDEO.videoHeight);
            _CANVAS_RESIZE.width = imgResize.width;
            _CANVAS_RESIZE.height = imgResize.height;
            _CTX_RESIZE.drawImage(_VIDEO, 0, 0, imgResize.width, imgResize.height);

            _CTX.drawImage(_VIDEO, 0, 0, _VIDEO.videoWidth, _VIDEO.videoHeight);
        }

        let thumbnail_base64 = null;
        let video_modal_el = $('#video-modal');
        let file_temp = [];
        let url_temp = [];
        let filename_temp = [];
        //action on change file reupload scene
        video_modal_el.find('.re-upload__file-input').off().on('change', function (e) {

            e.preventDefault();
            e.stopPropagation();

            //disable save button
            $(this).parents('.modal-content').find('.video-modal-btn.video-save-btn').removeClass('active').addClass('hide');


            let filename_uploaded = '';
            let video_upload_url = '';
            let current_variation_id;
            //signature
            let signature = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
            let file_input_element = this;

            // Check duplicate file select
            if (file_temp.indexOf(this.files[0]) !== -1) {
                let file_index = file_temp.indexOf(this.files[0]);
                video_upload_url = url_temp[file_index];
                filename_uploaded = file_temp[file_index]
            } else {
                file_temp.push(this.files[0]);
                video_upload_url = URL.createObjectURL(this.files[0]);
                url_temp.push(video_upload_url);
                filename_uploaded = this.files[0].name.split('.')[0];
                filename_temp.push(filename_uploaded);
            }
            $('.selected_file').html(filename_uploaded);

            if (filename_uploaded && video_upload_url) {
                let scene_dom;

                if (this.files[0].name.match(/\.(pdf)$/)) {
                    scene_dom = `<iframe width="100%" height="600px" src="${video_upload_url}" ></iframe>`
                } else {
                    scene_dom = `<video class="new_scene" width="auto" height="90%" crossorigin="anonymous" preload="auto" controls>
                                        <source src="${video_upload_url}" type="video/mp4">
                                  </video>`;
                }

                let variation_active_dom = $('.video-item-component.active');
                let version_active_dom = variation_active_dom.find('.video-modal-version-item.version-active');
                let scene_active_dom = version_active_dom.find('video, iframe');
                //version_active_dom.find('.video-modal-version-text').html(filename_uploaded);
                current_variation_id = version_active_dom.attr('data-scene-id');

                scene_active_dom.remove();
                version_active_dom.append(scene_dom);
                let title_dom = $('li.video-version.version-active');
                //title_dom.find('span').html(filename_uploaded);

                SoundcheckProject.treeOrder();
                SoundcheckProject.prevVideo();

                // Click on Tree Variation
                SoundcheckProject.treeVariationClick();
            }

            //canvas to get default thumbnail
            $(this).parent().append(`<div class="canvas hide">
                    <canvas id="video-canvas-upload"></canvas>
                    <canvas id="video-canvas-resize-upload" class="hide"></canvas>
                </div>`);

            let variation_upload_el = $(this);
            let _VIDEO = $('.video-modal .video-item-list').find('video.new_scene').get(0);
            if (_VIDEO) {
                $(_VIDEO).on('canplay', function () {
                    $(_VIDEO).off('canplay');
                    let _CANVAS_DOM = variation_upload_el.parent().find('.canvas');
                    let _CANVAS = _CANVAS_DOM.find("canvas#video-canvas-upload").get(0);
                    let _CANVAS_RESIZE = _CANVAS_DOM.find("canvas#video-canvas-resize-upload").get(0);
                    let _CTX = _CANVAS.getContext("2d");
                    let _CTX_RESIZE = _CANVAS_RESIZE.getContext("2d");
                    // Set canvas dimensions same as video dimensions
                    _CANVAS.width = _VIDEO.videoWidth;
                    _CANVAS.height = _VIDEO.videoHeight;
                    getThumbnail(_CANVAS_RESIZE, _CTX_RESIZE, _CTX, _VIDEO);
                    thumbnail_base64 = _CANVAS_RESIZE.toDataURL();
                    //Upload file to sever
                    SoundcheckProject.reuploadDataToServer('/top/re_upload_scene', file_input_element, thumbnail_base64, current_variation_id);
                });
                setTimeout(function () {
                    if (_VIDEO.readyState !== 4) {
                        $(_VIDEO).off('canplay');
                        thumbnail_base64 = '';
                        SoundcheckProject.reuploadDataToServer('/top/re_upload_scene', file_input_element, thumbnail_base64, current_variation_id);
                    }

                }, 2000)
            } else {
                thumbnail_base64 = '';
                SoundcheckProject.reuploadDataToServer('/top/re_upload_scene', file_input_element, thumbnail_base64, current_variation_id);
            }
        });
    },


    reuploadDataToServer: function (url, file_input_element, thumbnail_base64 = null, variation_id) {
        let formData = new FormData();
        if (variation_id) {
            formData.append('variation_id', variation_id);
            if (thumbnail_base64) {
                formData.append('thumbnail_base64', thumbnail_base64);
            }
            formData.append('file', file_input_element.files[0]);
            $.ajax({
                url: url,
                type: "POST",
                data: formData,
                cache: false,
                processData: false,
                contentType: false,
                beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                    $('.video-modal .upload-button-wrapper').css('display', 'flex');
                    $('.video-modal .upload-button-wrapper').addClass('clicked');
                    $('.video-modal .upload-button-wrapper .fill .process').css('width', '2%');
                },
                xhr: function () {
                    var xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener("progress", function (evt) {
                        if (evt.lengthComputable) {
                            let percentComplete = (evt.loaded / evt.total) * 70;
                            $('.video-modal .upload-button-wrapper .fill .process').css('width', percentComplete + '%');
                        }
                    }, false);
                    return xhr;
                },
                beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                    let target = $('.pd-scene-title-detail');
                    $('.pd-scene').empty();
                    $('.pd-scene').append(response.html);
                    setActionForProjectItem(target);
                    newWavesurferInit();
                    projectScene();
                    actionBottomVideo();
                    $('.selected_file').html('');
                    $('.new_scene').removeClass('new_scene');
                    $('.tfile-infor--scene[data-scene-id^=' + response.scene_id + ']').find('.scene-file--name').html(response.real_name);
                    $('#video-modal .video-modal-btn.video-save-btn').removeClass('hide').addClass('active');
                    $('.video-modal .upload-button-wrapper .fill .process').css('width', '100%');
                    setTimeout(function () {
                        // toastr.success(response.message);
                        $('.video-modal .upload-button-wrapper').removeClass('clicked').addClass('success')
                    }, 1000);
                    setTimeout(function () {
                        $('.video-modal .upload-button-wrapper').removeClass('success').css('display', 'none');
                        $('.video-modal .upload-button-wrapper .fill .process').css('width', '0');
                    }, 2000);

                    SoundcheckProject.seekToChangeThumbnail();
                },
                error: function (response) {
                    $('.selected_file').html('');
                    $('.new_scene').removeClass('new_scene');
                    $('.video-modal .upload-button-wrapper').removeClass('clicked');
                    $('.video-modal .upload-button-wrapper').css('display', 'none');
                    $('.video-modal .upload-button-wrapper .fill .process').css('width', '0');
                    toastr.error(response.responseJSON.message);
                },
                complete: function (response) {
                    $(".loader").hide();
                }
            });
        }
    },


  uploadDataToServer: function (url, file_input_element, thumbnail_base64=null, scene_title_id, signature=null, variation_id=null) {
    let formData = new FormData();
    if( scene_title_id){
      formData.append('scene_title_id', scene_title_id);
      formData.append('signature', signature);
      if (variation_id) {
        formData.append('variation_id', variation_id);
      }
      if (thumbnail_base64) {
        formData.append('thumbnail_base64', thumbnail_base64);
      }
      formData.append('file', file_input_element.files[0]);
      $.ajax({
        url: url,
        type: "POST",
        data: formData,
        cache: false,
        processData: false,
        contentType: false,
        beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
          $('.video-modal .upload-button-wrapper').css('display', 'flex');
          $('.video-modal .upload-button-wrapper').addClass('clicked');
          $('.video-modal .upload-button-wrapper .fill .process').css('width', '2%');
        },
        xhr: function () {
          var xhr = new window.XMLHttpRequest();
          xhr.upload.addEventListener("progress", function (evt) {
            if (evt.lengthComputable) {
              let percentComplete = (evt.loaded / evt.total) * 70;
              $('.video-modal .upload-button-wrapper .fill .process').css('width', percentComplete + '%');
            }
          }, false);
          return xhr;
        },
        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            let target = $('.pd-scene-title-detail');
            $('.pd-scene').empty();
            $('.pd-scene').append(response.html);
            setActionForProjectItem(target);
            newWavesurferInit();
            projectScene();
            actionBottomVideo();

          let new_variation_el = $(file_input_element).parents('.video-modal__tree').find('.video-variation[data-signature=' + response.signature + ']');
          let new_version_el = $(file_input_element).parents('.video-modal__tree').find('.video-version[data-signature=' + response.signature + ']');
          if (new_variation_el.length > 0) {
            new_variation_el.attr('data-variation-id', response.scene_id);
            new_variation_el.find('.variation-upload').attr('data-variation-id', response.scene_id);
          }
          if (new_version_el.length > 0) {
            new_version_el.attr('data-scene-id', response.scene_id);
            //set scene_id for delete button
            new_version_el.find('#delete-comment').attr('data-scene-id', response.scene_id);
          }

          let new_version_item_el = $(file_input_element).parents('.modal-body').find('.video-modal-version-item[data-signature=' + response.signature + ']');
          if(new_version_item_el.length > 0){
            new_version_item_el.attr('data-scene-id', response.scene_id)
            new_version_item_el.parents('.video-item-component').find('.video-modal-version-item .video-modal-version').addClass('gray-background');
            new_version_item_el.find('.video-modal-version').removeClass('gray-background');
          }

          let new_video_item_component_el = $(file_input_element).parents('.modal-body').find('.video-item-component[data-signature=' + response.signature + ']');
          if (new_video_item_component_el.length > 0){
            new_video_item_component_el.attr('data-variation-id', response.scene_id);
          }

          $('#video-modal li[data-signature="' + response.signature + '"] span').trigger('click');
          $('#video-modal .video-modal-btn.video-save-btn').removeClass('hide').addClass('active');
          $('.video-modal .upload-button-wrapper .fill .process').css('width', '100%');
          setTimeout(function() {
            toastr.success(response.message);
            $('.video-modal .upload-button-wrapper').removeClass('clicked').addClass('success')
          }, 1000);
          setTimeout(function() {
            $('.video-modal .upload-button-wrapper').removeClass('success').css('display', 'none')
            $('.video-modal .upload-button-wrapper .fill .process').css('width', '0');
          }, 2000);

          SoundcheckProject.seekToChangeThumbnail();
        },
        error: function (response) {
          $('.video-modal .upload-button-wrapper').removeClass('clicked');
          $('.video-modal .upload-button-wrapper').css('display', 'none');
          $('.video-modal .upload-button-wrapper .fill .process').css('width', '0');
          toastr.error(response.responseJSON.message);
        },
        complete: function (response) {
          $(".loader").hide();
        }
      });
    }
  },

  seekToChangeThumbnail: function () {

    let resizeImg = function (width, height) {

      let targetWidth = 480;
      if (width <= targetWidth) {
        return {width: width, height: height}
      }

      let ratio = targetWidth / width;

      return {
        width: parseInt(width * ratio),
        height: parseInt(height * ratio)
      }
    };

    let getThumbnail = function (_CANVAS_RESIZE, _CTX_RESIZE, _CTX, _VIDEO) {
      let imgResize = resizeImg(_VIDEO.videoWidth, _VIDEO.videoHeight);
      _CANVAS_RESIZE.width = imgResize.width;
      _CANVAS_RESIZE.height = imgResize.height;
      _CTX_RESIZE.drawImage(_VIDEO, 0, 0, imgResize.width, imgResize.height);

      _CTX.drawImage(_VIDEO, 0, 0, _VIDEO.videoWidth, _VIDEO.videoHeight);
    }

    let $video_modal = $('.video-modal#video-modal');
    let $video_modal_right = $video_modal.find('.video-modal__right');
    $video_modal_right.append(`<div class="canvas hide">
                    <input type="hidden" id="id_scene_id">
                    <input type="hidden" id="id_thumbnail_base64">
                    <canvas id="video-canvas-update"></canvas>
                    <canvas id="video-canvas-resize-update" class="hide"></canvas>
    `);

    let _CANVAS_DOM = $video_modal_right.find('.canvas').first();
    let _CANVAS = _CANVAS_DOM.find("#video-canvas-update").get(0);
    let _CANVAS_RESIZE = _CANVAS_DOM.find("#video-canvas-resize-update").get(0);
    let _CTX = _CANVAS.getContext("2d");
    let _CTX_RESIZE = _CANVAS_RESIZE.getContext("2d");
    let $active_video = $video_modal.find('.video-modal-version-item video');
    let thumbnail_base64_el = _CANVAS_DOM.find('#id_thumbnail_base64');
    let scene_info_el = _CANVAS_DOM.find('#id_scene_id');

    $active_video.off('canplay').on('canplay', function (e) {
      $(this).off('canplay');
      let thumbnail_base64 = null;
      let _VIDEO = this;
      let _scene_id = $(_VIDEO).parents('.video-modal-version-item').first().attr('data-scene-id');
      // Set canvas dimensions same as video dimensions
      _CANVAS.width = _VIDEO.videoWidth;
      _CANVAS.height = _VIDEO.videoHeight;
      $(_VIDEO).on('seeked', function () {
        getThumbnail(_CANVAS_RESIZE, _CTX_RESIZE, _CTX, _VIDEO);
        thumbnail_base64 = _CANVAS_RESIZE.toDataURL();
        thumbnail_base64_el.val(thumbnail_base64);
        scene_info_el.val(_scene_id);
      });
    });

  }
};

function chr(number) {
  if (number < 26) {
    return String.fromCharCode(number + 97);
  }
  return chr(Math.floor(number / 26) - 1) + chr(number % 26);
}

$.ajaxSetup({
  headers: {
    'X-CSRFToken': $('meta[name="csrf-token"]').attr('content')
  }
});

function isEmptyOrSpaces(str){
  return str === null || str.match(/^ *$/) !== null;
}

//Edit Product Scene
$(document).on('click', '.edit_scene', function () {
  let title = $(this).parents().siblings('h3.product_scene_title');
  let old_title = title.siblings('h3');
  let input = title.children();

  input.val(old_title[0].innerText)
  old_title.addClass('hide');
  title.removeClass('hide');
  $(this).addClass('hide');
  $(this).siblings('.delete_scene').addClass('hide');
  $(this).siblings('.edit_scene_confirm, .edit_scene_cancel').removeClass('hide');
});

$(document).on('click', '.edit_scene_cancel', function () {
    let title = $(this).parents().siblings('h3.product_scene_title');
    let old_title = title.siblings('h3');
    title.addClass('hide');
    old_title.removeClass('hide');
    $(this).siblings('.edit_scene, .delete_scene').removeClass('hide');
    $(this).siblings('.edit_scene_confirm').addClass('hide');
    $(this).addClass('hide');
});

$(document).on('click', '.edit_scene_confirm', function () {
  let input = $(this).parents().siblings('h3.product_scene_title').children('input');
  let new_name = input.val();
  if(isEmptyOrSpaces(new_name)) {
    alert("入力したテキストは正しくありません。")
  } else {
    $.ajax({
      url: "/ajax/edit_product_scene_name/",
      type: "POST",
      dataType: 'json',
      data: {
        product_scene_id: $(this).parents().siblings('ul')[0].classList[2],
        name: new_name
      },
      success: function (result) {
        location.reload();
      },
      fail: function () {
        alert("入力したテキストは正しくありません。")
      }
    });
  }
});

//Delete Product Scene
$(document).on('click', '.delete_scene', function () {
  let message = $(this).parent().siblings('.delete_confirm_message');
  message.removeClass('hide');
  message.children('.delete_confirm_message_accept').click(function () {
    $.ajax({
      url: "/ajax/delete_product_scene/",
      type: "POST",
      dataType: 'json',
      data: {
        product_scene_id: $(this).parents().siblings('ul')[0].classList[2],
        product_id: $('.scenes__sort').data('product'),
      },
      success: function (result) {
        location.reload();
      },
      fail: function () {
        alert("削除する際に、エラーが発生しました")
      }
    });
  });

  message.children('.delete_confirm_message_cancel').click(function () {
    message.addClass('hide');
  });
});

//Edit Scene
$(document).on('click', '.edit_scene_video', function () {
  let scene_name = $(this).parents().siblings('.scenes__item-info').children('.scenes__item-title');
  let scene_name_input = scene_name.next('input');
  let scene_name_button = scene_name_input.next();
  let scene_delete = scene_name_button.next('.delete_scene_video_button');
  scene_name.removeClass('hide');
  scene_name_input.val(scene_name[0].innerText)
  scene_name_button.removeClass('hide');
  scene_name.addClass('hide');
  scene_name_input.removeClass('hide');
  scene_delete.addClass('hide');
});

$(document).on('click', '.edit_scene_video_name_cancel', function () {
  let button = $(this).parent();
  let input = button.prev();
  let title = input.prev();

  button.addClass('hide');
  input.addClass('hide');
  title.removeClass('hide');
});

$(document).on('click', '.edit_scene_video_name_accept', function () {
  let button = $(this).parent();
  let input = button.prev();
  let new_title = input.val();
  let title = input.prev();

  if(isEmptyOrSpaces(new_title)) {
    alert("入力したテキストは正しくありません。")
  } else {
    title[0].innerHTML = input.val();

    $.ajax({
      url: "/ajax/edit_scene_video_name/",
      type: "POST",
      dataType: 'json',
      data: {
        title_id: $(this).parent()[0].classList[1],
        name: new_title,
      },
      success: function (result) {
        location.reload();
      },
      fail: function () {
        alert("入力したテキストは正しくありません。")
      }
    });
  }

  button.addClass('hide');
  input.addClass('hide');
  title.removeClass('hide');
});

//Delete Scene Video
$(document).on('click', '.delete_scene_video', function () {
  let scene_name = $(this).parents().siblings('.scenes__item-info').children('.scenes__item-title');
  let button = $(this).parents().siblings('.scenes__item-info').children('.delete_scene_video_button');
  let title_input = scene_name.next('input');
  let input_button = title_input.next('.edit_scene_video_name_button');
  scene_name.addClass('hide');
  button.removeClass('hide');
  title_input.addClass('hide');
  input_button.addClass('hide');
});

$(document).on('click', '.delete_scene_video_accept', function () {
    let title = $(this).parent()[0].classList[1];
    $.ajax({
        url: "/ajax/delete_scene_video/",
        type: "POST",
        dataType: "json",
        data: {
            title_id: title,
        },
        success: function (result) {
            location.reload();
        },
        fail: function () {
            location.reload();
            alert("エラーが発生しました削除できませんでした。")
        }
    })
});

$(document).on('click', '.delete_scene_video_cancel', function () {
  let button = $(this).parent();
  let title = button.prevAll('.scenes__item-title');

  button.addClass('hide');
  title.removeClass('hide');
});

function checkAFKUser() {
  setInterval(() => {
      $.ajax({
          type: "POST",
          contentType: false,
          processData: false,
          cache: false,
          url: "/message/check_AFK_user",
          data: '',
          success: function (data) {
          },
          fail: function (data) {
          },
          error: function(xhr, status, error) {
          },
          complete: function () {
          }
      });
  }, 180000);
}

$(document).ready(function () {
  if(is_logged_in === 'True') {
    checkAFKUser();
  }
  let url_string = window.location.href;
  if (url_string.includes('/top') && !url_string.includes('/topic/')  && !url_string.includes('/top/')) {
    $(".sheader-link[data-show^='project']").each(function () {
        $(this).addClass('current');
    })
  }
  else if (url_string.includes('/direct')) {
    $(".sheader-link[data-show^='messenger']").each(function () {
        $(this).addClass('current');
    })
  } else if (url_string.includes('/accounts/list') || url_string.includes('/accounts/curator/curator_setting')) {
    $(".sheader-link[data-show^='account']").each(function () {
        $(this).addClass('current');
    })
  } else if (url_string.includes('/creators') || url_string.includes('/gallery') && !url_string.includes('/gallery/')) {
    $(".sheader-link[data-show^='gallery']").each(function () {
        $(this).addClass('current');
    })
  }  else if (url_string.includes('/about')) {
    $(".sheader-link[data-show^='about']").each(function () {
        $(this).addClass('current');
    })
  } else if (url_string.includes('/updateinfo')) {
    $(".sheader-link[data-show^='updateinfo']").each(function () {
        $(this).addClass('current');
    })
  } else if (url_string.includes('/collection')) {
    $(".sheader-link[data-show^='collection']").each(function () {
        $(this).addClass('current');
    })
  } else if (url_string.includes('/mileages')) {
    $(".sheader-link[data-show^='mileage']").each(function () {
        $(this).addClass('current');
    })
  }

  $(document).on('click', '.mileage-popup', function(event) {
    event.stopPropagation(); // 親のaタグへの伝播を止める
    event.preventDefault();   // aタグのデフォルト動作も止める
    
    console.log('mileage-popupクリック発火'); // ←ここを追加
    if (!$('body').is('.no-backdrop')) {
      console.log('モーダルを表示します'); // ←ここを追加
      $('body').addClass('no-backdrop');
      $('#modal_mileage_popup').modal('show');
      ajaxGetMileageRankForArtist();
    } else {
      console.log('モーダルを非表示にします'); // ←ここを追加
      $('body').removeClass('no-backdrop');
      $('#modal_mileage_popup').modal('hide')
    }
  })

  $(document).on('click', '.no-backdrop .modal-backdrop, .no-backdrop .sheader, .no-backdrop .bdropdown-toggle.dropdown-toggle', function(event) {
    // .mileage-popup要素やその子要素がクリックされた場合は無視
    if ($(event.target).closest('.mileage-popup').length > 0) {
      return;
    }
    
    $('body').removeClass('no-backdrop');
    $('#modal_mileage_popup .loading').removeClass('hide');
    $('#modal_mileage_popup .mileage-popup-container:not(.loading)').remove();
    $('#modal_mileage_popup').modal('hide');
  })

  SoundcheckProject.Init();

  $('.scenes__sort-down').on('click', function(event) {
    event.preventDefault();
    var self = $(this).closest('.scenes__sort-box');
    self.find('.scenes__sort-down').addClass('hidden');
    self.find('.scenes__sort-up').removeClass('hidden');
  });

  $('.scenes__sort-up').on('click', function(event) {
    event.preventDefault();
    var self = $(this).closest('.scenes__sort-box');
    self.find('.scenes__sort-down').removeClass('hidden');
    self.find('.scenes__sort-up').addClass('hidden');
  });

  // Menu Click
  $('.nav-toggle').click(function () {
    $('.menu-sp').toggleClass('open');
  });

  $('.comment__reply-link').click(function () {
    $(this).siblings().toggleClass('edit-show');
  });

  // $('.setting-config').click(function () {
  //   $('.massenger__config').toggleClass('show-item');
  //   $('.setting-config').toggleClass('active');
  // });

  $('.menu-dropdown__item').click(function () {
    $(this).toggleClass('menu-open');
    $('.menu-dropdown__content').toggle('slide');
  });

  $('.comment__textarea-box').keyup(function () {
    var el = this;
    setTimeout(function () {
      el.style.cssText = 'height: auto';
      el.style.cssText = 'height:' + el.scrollHeight + 'px';
    }, 0);

    if ($(el).val()) {
      $('.submit-link').addClass('notEmpty');
    } else {
      $('.submit-link').removeClass('notEmpty');
    }
  });

  $('.setting-edit__links').click(function () {
    $(this).siblings().toggleClass('open');
  });

  $('.comment__reply-edit').on('click', function(event) {
    event.preventDefault();
    var self = $(this).closest('.comment__reply');
    self.find('.comment__textarea-edit').addClass('open');
    self.find('.comment__reply-info').addClass('none');
    self.find('.comment__textarea-edit').removeClass('none');
  });

  $('.comment__reply-remove').on('click', function(event) {
    event.preventDefault();
    var self = $(this).closest('.comment__reply');
    self.find('.comment__textarea-edit').removeClass('open');
    self.find('.comment__textarea-edit').addClass('none');
    self.find('.comment__reply-info').removeClass('none');
  });

  $('.user-info__project-icon').click(function () {
    $('.user-info__project-content').toggleClass('show-item');
  });

  $('.comment__textarea-emoticon').on('click', function(event) {
    event.preventDefault();
    var self = $(this).closest('.comment__textarea');
    self.find('.comment__textarea-emoticoninfo').toggleClass('show-icon');
  });

  $('.user-info__project-icon').click(function () {
    $('.user-info__project-content').toggleClass('show-item');
  });

  $('.up').on('click', function(e) {
    var sortup = $(this).closest('.scenes__title')
    sortup.insertBefore(sortup.prev())
  })

  $('.down').on('click', function(e) {
    var sortdown = $(this).closest('.scenes__title')
    sortdown.insertAfter(sortdown.next())
  })

  $('.project-list__sort').click(function () {
    $('.project-list__sort').toggleClass('show-item');
  });
});

function addDate(check) {
  var count = $('.js-add-datepicker').length + 1;
  if (check == count-2) {
    var datepickerLast = $('.js-add-datepicker:last').clone();
    datepickerLast.addClass('date-last');
    $(datepickerLast).find('.form-control').attr('id', 'data-date'+count).val('');
    $('.js-add-datepicker:last').after(datepickerLast);
    $('#data-date'+count).datepicker({
      autoclose: true,
      format: 'yyyy年mm月dd日'
    }).on('changeDate', function (ev) {
      addDate(count-1);
    });
    $(datepickerLast).find('.form-control').attr('placeholder','Add another option');
    $('#data-date'+count).parent('.js-add-datepicker').children('.close').click(function() {
      $('#data-date'+count).parent('.js-add-datepicker').remove();
    });
  }
}

$(window).on('load', function () {
  SoundcheckProject.onLoad();
});

$(window).on('resize', function () {
  SoundcheckProject.reSize();
});


function highlightURLInText(targetDOM) {
  let regex = /(?:(?:https?|http|ftp):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:;,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!;:,.]*\)|[A-Z0-9+&@#\/%=~_|$])/igm;
  targetDOM.innerHTML = targetDOM.innerHTML.replace(regex, "<a target='_blank' href=$&>$&</a>");
}

function formatTimeCallback(seconds, pxPerSec) {
    seconds = Number(seconds);

    if (seconds >= 3600) {
        return [seconds].map(time =>
            [   Math.floor((time / 3600)), // hour
                ('00' + Math.floor((time % 3600) / 60)).slice(-2),// minutes
                ('00' + Math.floor(time % 60)).slice(-2), // seconds
            ].join(':')
        );
    } else {
        return [seconds].map(time =>
            [
                Math.floor((time % 3600) / 60), // minutes
                ('00' + Math.floor(time % 60)).slice(-2), // seconds
            ].join(':')
        );
    }
}


function checkValidateBlank(arrayField) {
    let countError = 0;
    arrayField.map((x) => {
        if (!$(x).val() || $(x).val().trim() === '') {
            if (countError === 0) {
                $(x).focus();
            }
            countError++;
            $(x).addClass('error-border');
            $(`<ul class="errorlist"><li>${errorRequire}</li></ul>`).insertAfter($(x));
        } else {
            $(x).removeClass('error-border');
        }
    });
    return countError > 0
}

function escapeHtml(unsafe) {
    return $('<div>').text(unsafe).html();
}


function ajaxUpdateListWattingsFeedback(projectItem, projectId, loadPage) {
    $.ajax({
        type: "GET",
        datatype: "json",
        url: !window.localStorage.getItem("performance") 
                ? "/api/video/get-project-finished-video" 
                : "/top/get_project_wattings_feedback",
        data: {
            'project_id': projectId,
        },
        beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
            if ($('.project-tab-progress.active').length) {
                $('.loader').show();
            }
        },
        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            let target = projectItem.find('.tab--video-watting_checkback');
            target.empty();
            target.prepend(response.html);
            projectRating();
            dragScrollHorizontal($('.pd-section__video.mscrollbar'))
          if (response.html && response.html !== "\n\n\n\n" && $('.todo-list-item').length < 1){
            // tab--video-watting_checkback processing-list-item chapter-block d-none-chapter
            let delivery_block = $('.tab--video-watting_checkback.processing-list-item')
            if (delivery_block.length < 1){
              delivery_block = $('.pd-section.pd-section--delivery-video')
            }
            if (delivery_block.length > 0) {
                delivery_block.removeClass('d-none-chapter')
                delivery_chapter = true
            }
            $('.item-project-delivery').addClass('item-chapter-active')
          }
          console.log('todolist_chapter: ', todolist_chapter)
          console.log('delivery_chapter: ', delivery_chapter)
          try{
            if(loadPage){
              triggerShowTodoListAndProgressList(true);
              startLoadPdfThumbnail();
            } else {
              $('.tab--video-watting_checkback').addClass("d-none-chapter")
            }
          }catch(e){

          }
        },
        complete: function () {
            $(".loader").hide();
            // $('.filter-item-project').removeClass('item-disabled')
        }
    });
}

var isScrolling = false;

function dragScrollHorizontal(el) {
  let isDragging = false;
  let startX;
  el.on('scroll', function () {
    isScrolling = true;
  });
  el.on('mousedown', function (e) {
    isDragging = true;
    startX = -e.pageX - $(this).scrollLeft()
    $('body').css('user-select', 'none');
  });

  el.on('mousemove', function (e) {
    if (!isDragging) return;
    let scrollX = -e.pageX - startX;
    if (scrollX > 0) {
      el.scrollLeft(Math.abs(scrollX));
    } else {
      el.scrollLeft(0);
    }
  });

  el.on('mouseup', function () {
    isDragging = false;
    $('body').css('user-select', 'auto');
    if (isScrolling) {
      setTimeout(() => {
        isScrolling = false;
      }, 300)
    }
  });

  el.on('mouseleave', function (e) {
    isDragging = false;
    $('body').css('user-select', 'auto');
  });
}

function getWeekday() {
    let arrdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    let d = new Date();
    return arrdays[d.getDay()];
}

function formatDate(date) {
    var d;
    if (date) {
        d = new Date(date);
    } else {
        d = new Date();
    }
    let month = '' + (d.getMonth() + 1);
    let day = '' + d.getDate();
    let year = d.getFullYear();

    if (month.length < 2) month = '0' + month;
    if (day.length < 2) day = '0' + day;

    return [year, month, day].join('/');
}

function newPassFnc(newPass, insertAfter) {
  if(newPass !== '') {
      if(!newPass.match(/^(?=.*[A-Za-z])[A-Za-z\d.@+-^]{8,20}$/)) {
          if(!!!$('.error-pass1').get(0)) {
              $('<div class="errorlist error-pass1">' +
                  gettext('パスワードは8～20文字で設定してください。') + '</div>' +
                  '<div class="errorlist error-pass1">' +
                      gettext('パスワードには、数字だけではなく文字も含めてください。') +
                  '</div>').insertAfter(insertAfter);
              }
          return false;
      } else {
          $('.error-pass1').remove();
          return true;
      }
  } else {
      $('.error-pass1').remove();
      return false;
  }
}

function confirmPassFnc(passConfirm, newPass, insertAfter) {
  if(passConfirm !== '') {
      if(passConfirm !== newPass) {
          if(!!!$('.error-pass2').get(0)) {
              $('<div class="errorlist error-pass2">' +
          gettext('入力されたパスワードが一致していません。') +
          '</div>').insertAfter(insertAfter);
          }
          return false;
      } else {
          $('.error-pass2').remove();
          return true;
      }
  } else {
      $('.error-pass2').remove();
      return false;
  }
}

function emailFnc(email, insertAfter) {
  console.log("test", gettext('Only during production'));
  var regex = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
  if(email !== '') {
      if(!regex.test(email)) {
          if(!!!$('.error-email').get(0)) {
              $('<div class="errorlist error-email">' +
          gettext('メールアドレスが無効です。') +
          '</div>').insertAfter(insertAfter)
          }
          return false;
      } else {
          $('.error-email').remove();
          return true;
      }
  } else {
      $('.error-email').remove();
      return false;
  }
}


function activeProgress() {
    let progressDom = $('.upload-button-wrapper');
    progressDom.css('display', 'flex');
    progressDom.addClass('clicked');
    progressDom.find('.fill .process').css('width', '2%');
}

function removeProgress() {
    let progressDom = $('.upload-button-wrapper');
    progressDom.css('display', 'None');
    progressDom.removeClass('clicked success');
    progressDom.find('.fill .process').css('width', '0');
}


function ajaxGetMileageRankForArtist() {
    $.ajax({
        type: "GET",
        data: {},
        url: '/mileages/get_mileage_rank_for_artist',
        beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
        },
        success: function (response) {
            $('#modal_mileage_popup .mileage-popup-container:not(.loading)').remove();
            $('#modal_mileage_popup .mileage-popup-container.loading').addClass('hide');
            $('#modal_mileage_popup .popup-body').append(response.html);
            setTimeout(() => {
              $('#modal_mileage_popup .popup-body .card-image-container__lg').css('opacity', '1');
            }, 100)
        },
        error: function (xhr, status, error) {
        }
    });
}

function changeTitlePage() {

}

function getScrollbarWidth() {

  // Creating invisible container
  const outer = document.createElement('div');
  outer.style.visibility = 'hidden';
  outer.style.overflow = 'scroll'; // forcing scrollbar to appear
  outer.style.msOverflowStyle = 'scrollbar'; // needed for WinJS apps
  document.body.appendChild(outer);

  // Creating inner element and placing it in the container
  const inner = document.createElement('div');
  outer.appendChild(inner);

  // Calculating difference between container's full width and the child width
  const scrollbarWidth = (outer.offsetWidth - inner.offsetWidth);

  // Removing temporary elements from the DOM
  outer.parentNode.removeChild(outer);

  return scrollbarWidth;

}

function removeOverLayModal() {
  $(document).on('show.bs.modal', '#modal-document-popup, #modal-video-popup, #modal-image-popup, #modal-ACR-check-popup', function (e) {
      $('body').addClass('modal-no-overlay');
      if($(this).is('#modal-ACR-check-popup')) {
        $('body').css('padding-right', `${getScrollbarWidth() - 17 >=0 ? getScrollbarWidth() - 17 : 0}px`);
        $('.sheader').css('padding-right', `${getScrollbarWidth() >=0 ? getScrollbarWidth() : 0}px`);
      }
  })

  $(document).on('hidden.bs.modal', '#modal-document-popup, #modal-video-popup, #modal-image-popup,#modal-ACR-check-popup', function (e) {
      $('body').removeClass('modal-no-overlay');
      if($(this).is('#modal-ACR-check-popup')) {
        console.log($('.modal-open'));
        $('body').css('padding-right', 'auto');
        $('.sheader').css('padding-right', `0`);
      }
  })
}

if (!localStorage.getItem('first_login')) {
  localStorage.setItem('first_login', 'true');
}

$('.btn-logout').on('click', function () {
  if ($(this).hasClass('is-admin')) {
    localStorage.setItem('first_login', 'true');
  }
})
