let wavesurfer_arr = [];
let go_back = 0;
let isTriggerClickVariation = false;
const maxWidthSP = 767;
$(document).ready(function () {
    $('.pbanner-tab[data-show="progress"]').addClass('active');

    let active_offer = $('.pd-scene-title-detail');
    active_offer.attr('data-scene-title-id', scene_title_id);
    active_offer.removeClass('hide');
    $('.mcomment-top').hide()
    $('.pd-file-heading').trigger('click');
    $('.loader').hide();
    splideListVariant(0);
    actionVariationButton();
    actionHoverVariation();

    $.ajax({
        type: "GET",
        url: !window.localStorage.getItem("performance") 
            ? "/api/scene/get-scene-by-id" 
            : "/top/get_scenes_of_title_by_scene",
        data: {
            "scene_id": scene_id
        },
        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            const element = document.querySelector('.owner-top');
            if (element) {
                element.classList.add("close-edit")
            }
            let $project_video_item = $('.pd-scene-title-detail');

            $project_video_item.find('.pd-scene').append(response.html);
            actionBottomVideo($project_video_item);
            projectRating();
            setActionForProjectItem($project_video_item, scene_id);

            newWavesurferInit();
            projectScene();
            // setTimeout(function () {
                // goToSceneActive(scene_id, 'reload');
            // }, 2000);
            let blockScene = $('.pd-scene.block-scene-video');
            blockScene.attr('data-height-viewport', $(window).height());
            blockScene.attr('data-width-viewport', $(window).width());
            if ($(document).width() > maxWidthSP) {
                blockScene.css({
                    'display': 'inline-block'
                })
                $('.cscene--video--new').css({
                    'display': 'inline-flex'
                })
            }
            if (!$('.has-variations').length > 0) {
                if ($(document).width() > maxWidthSP) {
                    $('.cscene--video--new').addClass('pbt-0')
                    removePaddingTakePC()
                    resizeScenePC()
                } else {
                    resizeScene()
                }
            } else {
                if ($(document).width() > maxWidthSP) {
                    $('.cscene--video--new').removeClass('pbt-0')

                }
            }
            tooltipShow();
        }
    });

    //Tooltip-------------------------
    function tooltipShow() {
        let allTooltip = $('.all-tooltip');
        let background = $('.tutorial-container-background');
        let tooltips = $(
            '.all-tooltip .tooltip-tutorial'
        ).get();
        let allIndexTooltip = [];
        let sceneOption = $('.cscene__options').get(0);
        let fullDiv = $('main').get(0);
        let pins = [
            !!$('.project-chapter-video-undone').get(0)
            ? $('.project-chapter-video-undone').get(0)
            : $('.project-chapter-video-done').get(0),
            $('.star-3').get(0),
            $('.icon--sicon-social-share').get(0),
            !!$('.icon--sicon-bookmark-o').get(0)
            ? $('.icon--sicon-bookmark-o').get(0)
            : $('.icon--sicon-bookmark').get(0),
            $('.icon--sicon-pin').get(0),
        ].filter((value, key) => {
            if(!!value || value !== undefined)
                allIndexTooltip.push(key);
                return value;
        });

        let pinHeights = [
            sceneOption,
            sceneOption,
            !!$('.icon--sicon-social-share').get(0)
            ?sceneOption: null,
            sceneOption,
            $('.mcomment-bottom').get(0),
        ].filter((value) => {
            if(!!value || value !== undefined)
                return value
        });
        window.addEventListener('resize', contentPosition, false);
        // $(window).scroll(contentPosition)
        // window.addEventListener('orientationchange', contentPosition, false);


        function contentPosition() {
            allTooltip = $('.all-tooltip');
            background = $('.tutorial-container-background');
            tooltips = $(
                '.all-tooltip .tooltip-tutorial'
            ).get();
            allIndexTooltip = [];
            sceneOption = $('.cscene__options').get(0);
            fullDiv = $('main').get(0);
            pins = [
                !!$('.project-chapter-video-undone').get(0)
                ? $('.project-chapter-video-undone').get(0)
                : $('.project-chapter-video-done').get(0),
                $('.star-3').get(0),
                $('.icon--sicon-social-share').get(0),
                !!$('.icon--sicon-bookmark-o').get(0)
                ? $('.icon--sicon-bookmark-o').get(0)
                : $('.icon--sicon-bookmark').get(1),
                !$('.pd-section-file').hasClass('pd-section-file-active')
                ?$('.icon--sicon-pin').get(0) : null,
            ].filter((value, key) => {
                if(!!value || value !== undefined)
                    allIndexTooltip.push(key);
                    return value;
            });

            pinHeights = [
                sceneOption,
                sceneOption,
                !!$('.icon--sicon-social-share').get(0)
                ?sceneOption: null,
                sceneOption,
                $('.mcomment-bottom').get(0),
            ].filter((value) => {
                if(!!value || value !== undefined)
                    return value
            });

            pins.forEach((pin, key) => {
            let tooltip = tooltips[allIndexTooltip[key]];
            let pinHeight = pinHeights[key];
            let content = tooltip.querySelector('.tooltip-content');
            let arrow = content.querySelector('.arrow');

            if (
                getOffset(pin).left + content.offsetWidth / 2 >
                fullDiv.offsetWidth
            ) {
                const extraLeft =
                fullDiv.offsetWidth -
                (getOffset(pin).left + content.offsetWidth / 2);
                content.style.left =
                getOffset(pin).left - content.offsetWidth / 2 + extraLeft + 'px';
            } else if (
                getOffset(pin).left + fullDiv.offsetLeft <
                content.offsetWidth / 2
            ) {
                content.style.left = -fullDiv.offsetLeft;
            } else {
                if(key===tooltips.length-1) {
                    content.style.left =
                    getOffset(pin).left - content.offsetWidth / 2 + pin.offsetWidth + 'px';
                } else {
                    content.style.left =
                    getOffset(pin).left - content.offsetWidth / 2 + 'px';
                }
            }

            content.style.top =
                pinHeight.getBoundingClientRect().top -
                content.offsetHeight -
                pinHeight.offsetHeight -
                arrow.offsetHeight * 2.5 +
                'px';
            if(key === tooltips.length-1) {
                arrow.style.left =
                pin.getBoundingClientRect().left -
                content.offsetLeft +
                pin.offsetWidth / 2 +
                'px';
            } else {
                arrow.style.left =
                pin.getBoundingClientRect().left -
                content.offsetLeft -
                pin.offsetWidth / 2 +
                'px';
            }
            });
        }
        let indexTooltip = 0;

        $(document).on('click', '.btn-tutorial-sp, .btn-tutorial-pc', function (e) {
            indexTooltip = 0
            $('html, body').animate(
            {
                scrollTop: 0,
            },
            500
            );
            $('.srm3').css({"overflow": "hidden"})
            setTimeout(() => {
            contentPosition();
            allTooltip.addClass('active-tooltip');
            background.addClass('active-background');
            tooltips[allIndexTooltip[indexTooltip]]
                .querySelector('.tooltip-content')
                .classList.add('active-tooltip');
            $(`#count-number-toturial${allIndexTooltip[indexTooltip] + 1}`).html(indexTooltip + 1);
            $(`#total-number-toturial${allIndexTooltip[indexTooltip] + 1}`).html(pins.length);
            }, 500);
        });

        $(document).on('click', '.btn-next-tutorial', function (e) {
            contentPosition();
            if (indexTooltip < pins.length - 1) {
                if (indexTooltip < tooltips.length - 2) {
                    $('html, body').animate(
                        {
                        scrollTop: 0,
                        },
                        100
                    );
                    setTimeout(() => {
                        contentPosition();
                        tooltips[allIndexTooltip[indexTooltip]]
                        .querySelector('.tooltip-content')
                        .classList.remove('active-tooltip');
                        tooltips[allIndexTooltip[indexTooltip + 1]]
                        .querySelector('.tooltip-content')
                        .classList.add('active-tooltip');
                        indexTooltip++;
                        $(`#count-number-toturial${allIndexTooltip[indexTooltip] + 1}`).html(indexTooltip + 1);
                        $(`#total-number-toturial${allIndexTooltip[indexTooltip] + 1}`).html(pins.length);
                    }, 105)
                } else {
                    contentPosition();
                    if(!!pins[indexTooltip]) {
                        pins[indexTooltip].scrollIntoView({ behavior: 'smooth' });
                        tooltips[indexTooltip]
                        .querySelector('.tooltip-content')
                        .classList.remove('active-tooltip');
                        tooltips[indexTooltip + 1]
                        .querySelector('.tooltip-content')
                        .classList.add('active-tooltip');
                        indexTooltip++;
                        $(`#count-number-toturial${allIndexTooltip[indexTooltip] + 1}`).html(indexTooltip + 1);
                        $(`#total-number-toturial${allIndexTooltip[indexTooltip] + 1}`).html(pins.length);
                    }
                }
            } else {
            $('html, body').animate(
                {
                scrollTop: 0,
                },
                500
            );
            $('.srm3').css({"overflow": "auto"})
            allTooltip.removeClass('active-tooltip');
            background.removeClass('active-background');
            tooltips[allIndexTooltip[indexTooltip]]
                .querySelector('.tooltip-content')
                .classList.remove('active-tooltip');
            indexTooltip = 0;
            }
        });
        $(document).on('click', '.btn-prev-tutorial', function (e) {
            $('html, body').animate(
                {
                scrollTop: 0,
                },
                100
            );
            setTimeout(() => {
                contentPosition()
                tooltips[allIndexTooltip[indexTooltip]]
                .querySelector('.tooltip-content')
                .classList.remove('active-tooltip');
                tooltips[allIndexTooltip[indexTooltip - 1]]
                .querySelector('.tooltip-content')
                .classList.add('active-tooltip');
                indexTooltip--;
            }, 100)
        });
    }
    //Tooltip-------------------------

    let message = active_offer.find('.s-text, .s-audio-text, .s-filetext');
    $.each(message, function (i, v) {
        if (!$(v).is('.align-center')) {
            let regex = /(?:(?:https?|http|ftp):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:;,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!;:,.]*\)|[A-Z0-9+&@#\/%=~_|$])/igm;
            v.innerHTML = v.innerHTML.replace(regex, "<a target='_blank' href=$&>$&</a>");
        }
    });

    $(document).on('click', '.mcomment-bottom', () => {
        setTimeout(() => {
            if($('.btn-tutorial-sp').css('bottom') === `${2 * Math.max(document.documentElement.clientWidth, window.innerWidth || 0)/100}px` ) {
                $('.btn-tutorial-sp').css('bottom', `${$('.btn-tutorial-sp').height() + $('.maction').height()}px`)
            }
        }, 200);
    })

    let buttonTutorialPC = $('.btn-tutorial-pc')
    $(document).on('click', () => {
        setTimeout(() => {
            if($('.pd-file-toggle').hasClass('active')){
                buttonTutorialPC.css({
                    'right': `${$('.pd-section-file').outerWidth()/buttonTutorialPC.width()}px`,
                    'bottom': `${$('.pd-section-file').outerWidth()/buttonTutorialPC.width()}px`,
                    'left': ''
                })
            } else {
                buttonTutorialPC.css({
                    'left': `${($('.pd-section-file').outerWidth() - buttonTutorialPC.width()) / 2}px`,
                    'bottom': `${($('.pd-section-file').outerWidth() - buttonTutorialPC.width()) / 2}px`,
                    'right': ''
                })
            }
        }, 200);
    })

    commentInput();
    previewFile();
    createMessage(active_offer);
    editMessage(active_offer);
    resolveComment(active_offer);
    projectToggleResolved();
    seenComment('scene');
    active_offer.on('click', '.mcomment-input-text, .mcomment-bottom', function () {
        seenComment('scene');
    });

    newWavesurferInit();
    active_offer.on('click', '.video-pin-time:not(.pin-time-audio), .video-comment-audio-title', function (e) {
        //e.stopPropagation();
        let scene_id = $(this).parents('.s-audio').data('scene-id');
        if (scene_id !== 'None') {
            let pin_time = $(this).parent().find('.video-pin-start');
            let target_pin = $(this).parent().find('.s-audio-control');
            let start_time = TimeToSeconds(pin_time.text());

            if (isNaN(start_time)) {
                start_time = 0;
            }
            if(!$(this).closest('.s-audio-control').hasClass('active')){
                const buttonDom = $(`.variation-button-container[data-scene-id=${scene_id}]`);
                const listDom = buttonDom.closest('.list-variation-container');
                if(listDom.hasClass('hide')){
                    listDom.removeClass('hide');
                    $(`.list-variation-container`).each(function(index, item){
                        if($(this).hasClass('hide')) {
                            $($('.take-overlay-container')[index]).find('input').removeAttr('checked');
                        } else {
                            $($('.take-overlay-container')[index]).find('input').attr('checked', true);
                        }
                    });
                }
                buttonDom.trigger('click');
            }
            if($(".variation-button-container.active").length){
                $(".variation-button-container.active").get(0).scrollIntoView({behavior: 'smooth'});
            }
            goToSceneActive(scene_id);

            let video_item_list = $(this).parents('.pd-scene-title-detail').find('.cscene__variation');

            let video_item_component = video_item_list.find('.cscene__version[data-scene-id=' + scene_id + ']').eq(0);

            let video = video_item_component.find('.ccscene__thumb video').get(0);
            let wave;
            if (!video) {
                wave = video_item_component.find('.cscene__version-horizontal .s-audio--audio-wave');
            }
            if ($(this).siblings(".s-audio-source").length !== 0) {
                let wave_index = $(this).parents('.s-audio').data('wavesurfer');
                let scene_id = $(this).parents('.s-audio').data('scene-id');
                let pin_time = $(this).parent().find('.video-pin-start');
                let target_pin = pin_time.parents().find('.s-audio-control');

                let start_time = TimeToSeconds(pin_time.text());

                if (isNaN(start_time)) {
                    start_time = 0;
                }
                if (video) {
                    playPinWavesurfer(wavesurfer_arr[wave_index], video, start_time, target_pin);
                } else {
                    let audio = video_item_component.find('.s-audio--audio-wave');
                    if (audio.length > 0) {
                        let scene_pin = video_item_component.find('.video-item-component-content .video-item-component-content-video .pin-time-audio');
                        let scene_index = parseInt(video_item_component.find('.s-audio').attr('data-wavesurfer'));
                        let scene_wavesurfer = wavesurfer_arr[scene_index];
                        playWavesurferWavesurfer(scene_wavesurfer, wavesurfer_arr[wave_index], start_time, target_pin, scene_pin);
                    } else {
                        let is_play = false;
                        for (let i = 0; i < wavesurfer_arr.length; i++) {
                            if (wavesurfer_arr[i]) {
                                if (wavesurfer_arr[i].isPlaying()) {
                                    if (wavesurfer_arr[i] === wavesurfer_arr[wave_index]) {
                                        is_play = true;
                                    }
                                    wavesurfer_arr[i].playPause();
                                }
                            }
                        }
                        if (!is_play) {
                            wavesurfer_arr[wave_index].playPause();
                        }
                    }
                }

            } else {
                if (video) {
                    if (target_pin.hasClass('active')) {
                        stopVideo(video)
                    } else {
                        setTimeout(function () {
                            target_pin.addClass('active');
                            target_pin.closest('.messenger-content').find('.s-audio').addClass('active');
                            target_pin.closest('.mmessage-content').find('.s-audio').addClass('active');
                        }, 300);

                        playVideo(video, start_time);
                        $(video).on('pause', function () {
                            target_pin.removeClass('active');
                            target_pin.closest('.messenger-content').find('.s-audio').removeClass('active');
                            target_pin.closest('.mmessage-content').find('.s-audio').removeClass('active');
                        })
                    }
                } else if (wave) {
                    let scene_index = wave.attr('data-wavesurfer');
                    let wavesurfer = wavesurfer_arr[scene_index];
                    if (target_pin.hasClass('active')) {
                        stop_video_audio();
                    } else {
                        setTimeout(function () {
                            target_pin.addClass('active');
                            target_pin.closest('.messenger-content').find('.s-audio').addClass('active');
                            target_pin.closest('.mmessage-content').find('.s-audio').addClass('active');
                        }, 300);
                        if (!wavesurfer.loaded) {
                            let src = $(wavesurfer.mediaContainer).siblings('.s-audio-source');
                            let link = src.attr('data-link');
                            wavesurfer.load(link);
                        }
                        playWavesurfer(wavesurfer, start_time);
                        target_pin.addClass('active');
                        target_pin.closest('.messenger-content').find('.s-audio').addClass('active');
                        target_pin.closest('.mmessage-content').find('.s-audio').addClass('active');
                        if($(".variation-button-container.active").length){
                            $(".variation-button-container.active").get(0).scrollIntoView({behavior: 'smooth'});
                        }
                        wavesurfer.on('pause', function () {
                            target_pin.removeClass('active');
                            target_pin.closest('.messenger-content').find('.s-audio').removeClass('active');
                            target_pin.closest('.mmessage-content').find('.s-audio').removeClass('active');
                        })
                    }
                }
            }
        }
    });
    sScrollbarBottom();

    $(document).on('click', '.pbanner-info .pbanner-tab', function (e) {
        window.location = window.location.href.split('/scene/')[0]
    });
    setOpenVideoModalFor(active_offer, true);
    copyURL();
    backToProject();

    // back to project detail
    window.onpopstate = function () {
        let current_url = window.location.toString();
        if (current_url.includes('/scene/')) {
            window.history.go(go_back*(-1))
        }
    };
});

function backToProject() {
    $(document).on('click', '.cscene-heading__product', function () {
        let product_scene_id = $(this).attr('data-product-scene-id');
        window.location = window.location.href.split('/scene/')[0] + '?tab=progress&chapter=' + product_scene_id;
    })
}

function getOffset(el) {
    const rect = el.getBoundingClientRect();
    return {
    left: rect.left + window.scrollX,
    top: rect.y + window.scrollY,
    };
}

let list_splide = [];
function splideListVariant(i){
    let index = i;
    if(!$('.list-variation-container').length && index < 100) {
        if (!index) {
            index = 1;
        } else {
            index++;
        }
        return setTimeout(() => {
            splideListVariant(index);
        }, 200);
    }
    // $('.list-variation-container').each(function(index, params) {
    //     list_splide[index] = new Splide(`.splide.take-${index}` , {
    //         type: 'slide',
    //         drag: 'free',
    //         easing: 'linear',
    //         isNavigation: true,
    //         rewind: true,
    //         arrows: false,
    //         pagination: false,
    //     }).mount();
    // });

    actionVariationButton();
    initActionVariation();
    actionChooseTake();
    // setTimeout(() => {
    //     list_splide[0].go(3)
    // }, 3000);
    // setTimeout(() => {
    //     list_splide[0].go(1)
    // }, 6000);
}

function actionChooseTake() {
    $('.take-overlay-container .check-mark').on('click', function(){
        const inputDom = $(this).parent().find('input');
        if(inputDom.attr('checked') && $('.take-overlay-container input[checked]').length > 1) {
            inputDom.removeAttr('checked');
        } else if(!inputDom.attr('checked')) {
            inputDom.attr('checked', true);
        }
    })
}

function initActionVariation(){
    isTriggerClickVariation = true;
    const targetId = window.location.href.split('/scene/')[1];
    const selected_take = $('.list-variation').data('selected-take');
    let listIndexTake = [];
    let listIndexVariant = [];
    if(selected_take){
        $('.list-variation-container').each(function() {
            // const list = $(this).attr('data-list-variant-id').split(', ');
            const sceneId = $(this).data('scene-id');
            if(selected_take.includes(sceneId)) {
                listIndexTake.push(parseInt($(this).attr('data-take')));
                listIndexVariant.push($(this).find(`.variation-button-container[data-scene-id=${sceneId}]`).attr('data-variant'));
            }
            // $(`.variation-button-container[data-scene-id=${targetId}]`).addClass('active');
        });
        $.each(listIndexTake, function(index, indexTake) {
            $(`.take-overlay-container[data-index=${indexTake}]`).find('input').attr('checked', true);
            $(`.list-variation-container[data-take=${indexTake}]`).removeClass('hide');
        });
        $(`.variation-button-container[data-variant=${listIndexVariant[listIndexVariant.length -1]}][data-take=${listIndexTake[listIndexTake.length-1]}]`).trigger('click');
    } else {
        let indexTake = 0;
        let indexVariant = 0;
        $('.list-variation-container').each(function() {
            const list = $(this).attr('data-list-variant-id').split(', ');
            if(list.includes(targetId)) {
                indexTake = parseInt($(this).attr('data-take'));
                // indexVariant = $(this).find(`.variation-button-container[data-scene-id=${targetId}]`).attr('data-variant');
                return;
            }
            // $(`.variation-button-container[data-scene-id=${targetId}]`).addClass('active');
        });
        // listIndexTake.push(indexTake);
        // listIndexVariant.push(indexVariant);
        $(`.take-overlay-container[data-index=${indexTake}]`).find('input').attr('checked', true);
        $(`.list-variation-container[data-take=${indexTake}]`).removeClass('hide');
        $(`.variation-button-container[data-variant=${indexVariant}][data-take=${indexTake}]`).trigger('click');
    }


    // $('.take-overlay-container').each(function() {
    //     const list = $(this).attr('data-list-variant-id').split(', ');
    //     if(list.includes(targetId)) {
    //         $(this).find('input').attr('checked', true);
    //         return;
    //     }
    // });
    // $.each(listIndexTake, function(index, indexTake) {
    //     $(`.take-overlay-container[data-index=${indexTake}]`).find('input').attr('checked', true);
    //     $(`.list-variation-container[data-take=${indexTake}]`).removeClass('hide');
    //     $.each(listIndexVariant, function(index, indexVariant){
    //         $(`.variation-button-container[data-variant=${indexVariant}][data-take=${indexTake}]`).trigger('click');
    //     })
    // });
    // $(`#sliderHorizontal0`).slick('slickGoTo', indexTake);
    // $(`#sliderVertical${indexTake}`).slick('slickGoTo', indexVariant);
    // list_splide[parseInt(indexTake)].go(parseInt(indexVariant))
    if ($(document).width() > maxWidthIpadDevice) {
        resizeScenePC();
        setPositionTooltipVariationScrollPC();
    } else if ($(document).width() <= maxWidthDeviceSP) {
        resizeScene();
        setPositionTooltipVariationScroll();
    } else {
        resizeScenePC();
        setPositionTooltipVariationScroll();
    }
    removePaddingTakePC();
    applyStyleParentSlide();
}

function actionVariationButton() {
    $('.take-detail-container').on('click', function(el) {
        const pdSectionContent = document.querySelector('.pd-section__content');
        const pdSectionContentPdScene = document.querySelector('.pd-section__content .pd-scene');
        const pdSectionContentPdComment = document.querySelector('.pd-section__content .pd-comment');
        const headerPage = document.querySelector('.pd-section__content .header-page');
        const listTakeSelect = document.querySelector('.pd-section__content .choose-take-overlay');
        const backListTake = document.querySelector('.pd-section__content .select-take-back');

        let maxHeightOverlay = 0;
        let listTakeSelectCssBottom = $('.list-variation-container').last().css('margin-bottom');
        let listTakeSelectCssLeft = 0;
        let listTakeSelectCssMaxHeight = 0;

        listTakeSelectCssLeft = ($(el.currentTarget).position().left - listTakeSelect.offsetWidth) +'px';
        if (pdSectionContent.offsetWidth === pdSectionContentPdScene.offsetWidth) {
            // [PC] max_height overlay pc = height_screen - top header
            maxHeightOverlay = $(pdSectionContent).height() - headerPage.offsetHeight - pdSectionContentPdComment.offsetHeight;
            listTakeSelectCssBottom = parseFloat(listTakeSelectCssBottom) + pdSectionContentPdComment.offsetHeight + 'px';
        } else {
            // [SP] max_height overlay sp = height_screen - top header - bottom view(scene name view + chat view)
            maxHeightOverlay = $(pdSectionContent).height() - headerPage.offsetHeight;
        }
        // Check current height of list take and max height overlay
        if (listTakeSelect.scrollHeight > maxHeightOverlay){
            // Set max height as max height overlay
            listTakeSelectCssMaxHeight = (maxHeightOverlay - parseFloat($('.list-variation-container').last().css('margin-bottom'))) + 'px';
        } else {
            listTakeSelectCssMaxHeight = '100%';
        }
        backListTake.style.display = 'flex';
        // Scroll into view
        $(listTakeSelect).removeClass('choose-take-overlay-hidden');
        $(listTakeSelect).css({
            'bottom': listTakeSelectCssBottom,
            'left': listTakeSelectCssLeft,
            'max-height': listTakeSelectCssMaxHeight,
        });
        $(listTakeSelect).find('.take-overlay-container[data-index="'+(parseInt($(el.currentTarget).data('index-take')) - 1)+'"]').get(0).scrollIntoView({behavior: 'smooth'});
    });

    function hideOverlayTake() {
        let lengthPrevVariationsShow = $('.list-variation-container').not('.hide').length;
        if(!$('.choose-take-overlay').hasClass('choose-take-overlay-hidden') && !!$('.select-take-back')[0]) {
            $('.choose-take-overlay').addClass('choose-take-overlay-hidden');
            $('.select-take-back')[0].style.display = 'none';
            let tempListIndex = []
            let taken_scenes = [];
            $('.choose-take-overlay .take-overlay-container input[checked]').each((i, v) => {
                tempListIndex.push(parseInt($(v).closest('.take-overlay-container').attr('data-index')));
            });
            $('.list-variation-container').each(function () {
                if (tempListIndex.includes(parseInt($(this).attr('data-take')))) {
                    if ($(this).hasClass('hide')) {
                        $(this).removeClass('hide');
                    }
                    taken_scenes.push($(this).data('scene-id'));
                } else if (!$(this).hasClass('hide')) {
                    $(this).addClass('hide');
                }
            });
            let prevVariationSceneId = $('.variation-button-container.active').attr('data-scene-id');
            $.ajax({
                type: 'POST',
                url: '/scene/taken/' + scene_id + '/',
                data: {
                    'taken_scenes': taken_scenes,
                },
                beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                    // Change url
                    if(response && response.url_redirect){
                        history.pushState({}, null, response.url_redirect);
                        // Focus lasted take
                        const listIndexTake = [];
                        const listIndexVariant = [];
                        const targetId = window.location.href.split('/scene/')[1];
                        $('.list-variation-container').each(function() {
                            // const list = $(this).attr('data-list-variant-id').split(', ');
                            const sceneId = $(this).data('scene-id');
                            if(taken_scenes.includes(sceneId)) {
                                listIndexTake.push(parseInt($(this).attr('data-take')));
                                listIndexVariant.push($(this).find(`.variation-button-container[data-scene-id=${targetId}]`).attr('data-variant'));
                            }
                            // $(`.variation-button-container[data-scene-id=${targetId}]`).addClass('active');
                        });
                        if ($(document).width() > maxWidthSP) {
                            let currentVariationSceneId = $('.variation-button-container.active').attr('data-scene-id');
                            let lengthCurrentVariationsShow = taken_scenes.length;
                            if (lengthPrevVariationsShow !== lengthCurrentVariationsShow && prevVariationSceneId === currentVariationSceneId) {
                                isTriggerClickVariation = true;
                                $(`.variation-button-container[data-variant=${listIndexVariant[listIndexVariant.length - 1]}][data-take=${listIndexTake[listIndexTake.length - 1]}]`).trigger('click');
                            }
                        } else {
                            $(`.variation-button-container[data-variant=${listIndexVariant[listIndexVariant.length - 1]}][data-take=${listIndexTake[listIndexTake.length - 1]}]`).trigger('click');
                        }

                    }
                },
                error: function () {
                },
                complete: function () {
                }
            });
            const listVariationContainer = $(".variation-button-container.active").closest('.list-variation-container');
            if(listVariationContainer.hasClass('hide')) {
                $($($('.list-variation .list-variation-container:not(.hide)').last()).find('.variation-button-container')[0]).trigger('click');
            }
            if($(".variation-button-container.active").length){
                const element = $(".variation-button-container.active").get(0);
                if (element) {
                    const elementRect = element.getBoundingClientRect();
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
                
                    const maxScrollTop = document.documentElement.scrollHeight - viewportHeight;
                
                    let targetScrollTop = scrollTop + elementRect.top - 100;
                    targetScrollTop = Math.min(targetScrollTop, maxScrollTop);
                
                    window.scrollTo({
                        top: targetScrollTop,
                        behavior: 'smooth'
                    });
                }
            }
            // if($(".variation-button-container.active").length){
            //     $(".variation-button-container.active").get(0).scrollIntoViewIfNeeded({behavior: 'smooth'});
            // }
        }
        removePaddingTakePC();
    }

    $(document).on('click', '.select-take-back', function(){
        hideOverlayTake();
    });

    $(document).on('scroll', function(){
        hideOverlayTake();
        let listVariation = $('.list-variation');
        let listToolTip = $('.variation-button-container');
        listToolTip.each(function (i, el) {
            calculatePositionTooltipVariation($(this), $(this).find('.variation-button-name-tooltip-container')[0]);
            $(this).find('.variation-button-name-tooltip-container').css({
                'transform': ''
            })
        })
        checkPositionTooltipVariation(listToolTip, listVariation);
    })

    /*
    * new code edited
    * remove option true to fix error click to variation will hidden list variation before scene loaded
    */
    window.addEventListener('resize', function(event) {
        hideOverlayTake();
    });

    //old code
    // window.addEventListener('resize', function(event) {
    //     hideOverlayTake();
    // }, true);

    $('.variation-button-container').on('click', function(e){
        let listVariation = $('.list-variation');
        stop_video_audio();
        // /*auto play video
        //  let sceneId = $(this).attr('data-scene-id');
        //  let videoElement = $('div[data-scene-id="' + sceneId + '"] video');
        //  if (videoElement.length > 0) {
        //      playVideo(videoElement.get(0))
        //  }
        //  */
        if ($(document).width() > maxWidthSP) {
            if (e.button === 0) {
                let sceneCurrentActive = $(this).parents('.pd-scene.block-scene-video').find('.cscene__version.slick-slide.slick-current.slick-active');
                let previewSceneId = sceneCurrentActive.attr('data-scene-id');
                let btnVariationSceneId = $(this).attr('data-scene-id');
                isTriggerClickVariation = true;
                if (previewSceneId === btnVariationSceneId) {
                    isTriggerClickVariation = false;
                }
            }
            if (!isTriggerClickVariation) {
                return;
            }
        }else if ($(document).width() < maxWidthIpadDevice) {
            let listVariationContainer = $('.variation-button-container');
            listVariationContainer.each(function (i, el) {
                $(this).find('.variation-button-name-tooltip-container').css({
                    'display': 'none'
                })
            })
            calculatePositionTooltipVariation($(this), $(this).find('.variation-button-name-tooltip-container')[0]);
            $(this).find('.variation-button-name-tooltip-container').css({
                'display': 'flex',
                'transform': 'translate(-50%, -100%)'
            })
            let listVariation = $('.list-variation');
            checkPositionTooltipVariation(listVariationContainer, listVariation);
        } else {
            let listToolTip = $('.variation-button-container');
            listToolTip.find('.variation-button-name-tooltip-container').css({
                'display': 'none'
            })
        }
        const takeIndex = $(this).attr('data-take');
        const variationIndex = $(this).attr('data-variant');

        listVariation.find('.active').removeClass('active');
        $(this).addClass('active');

        $(`#sliderHorizontal0`).slick('slickGoTo', takeIndex);
        $(`#sliderVertical${takeIndex}`).slick('slickGoTo', variationIndex);

        let sceneCurrent = $(this).parents('.pd-scene.block-scene-video').find('.cscene__version.slick-slide.slick-current.slick-active');
        let childrenSceneCurrent = sceneCurrent.children()
        let dataMaxHeightVariations = childrenSceneCurrent.attr('data-max-height-variations')
        let dataPreviewHeight = childrenSceneCurrent.attr('data-preview-height')
        let dataPreviewWidth = childrenSceneCurrent.attr('data-preview-width')
        let maxWidthViewport = $(document).width()
        listVariation.css({
            'max-height': `${dataMaxHeightVariations}px`
        })
        let sceneActive = $('.cscene__version.slick-slide.slick-current.slick-active')
        let sceneNew = $(this).parents('.pd-scene.block-scene-video').find('.cscene__variation.slick-slide.slick-current.slick-center');
        if ($(document).width() > maxWidthSP) {
            let maxWidthViewportPC = $(document).width() * percentMaxWidthPreviewPC;
            let paddingHozScreen = childrenSceneCurrent.attr('data-padding-hoz-screen')
            let widthCommentArea = childrenSceneCurrent.attr('data-width-comment')
            setStyleParentScenePC(sceneNew, sceneActive, maxWidthViewportPC, dataPreviewHeight, dataPreviewWidth, paddingHozScreen, widthCommentArea)
        } else {
            setStyleParentScene(sceneNew, sceneActive, maxWidthViewport, dataPreviewHeight, dataPreviewWidth)
        }
        if ($('.pin-preview').length > 0) {
            addBorderPreview(true, sceneActive);
        } else {
            addBorderPreview();
        }
    });
}

$('.project-item').find('.mcomment-send').on('click', function () {
    if ($('.project-item').find('.mcomment-input').hasClass('is-pin')) {
        addBorderPreview()
    }
})
