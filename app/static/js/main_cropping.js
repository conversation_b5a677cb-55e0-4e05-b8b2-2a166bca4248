$(document).ready(function () {
  var link = $('#id_image').closest('.form-group').find('a');
  link.replaceWith('<img id="avatar-img" style="width:100%">');
  $('#avatar-img').attr('src', link.attr('href'))
  $('#id_image').closest('.form-group').find('label').text('画像');
  $('#id_image').attr({
    accept: 'image/*',
    oninvalid: "this.setCustomValidity('このフィールドは必須項目です。')",
    oninput: "setCustomValidity('')"
  })
  $('#image-clear_id').parents('.col-xs-12').find('label').remove();
  $('#image-clear_id').remove();

  $('#id_image').on('click', function (event) {
    $(this).val('').clone(true);
  });

  $('#id_information').on('change', function () {
    if (this.files && this.files[0] && this.files[0].name.match(/\.html$/)) {
      $(this).siblings('.no-file').hide();
      var theSplit = $(this).val().split('\\');
      $('.have-file').text(theSplit[theSplit.length - 1]);
    } else if (this.files.length == 0) {
      $('#id_image').siblings('.no-file').show();
      return false;
    } else {
      alert('HTMLファイルをアップロードしてください。');
      $('.have-file').text('');
      $(this).val('').clone(true);
      $(this).siblings('.no-file').show();
    }
  });

  var imageCropper = {
    viewMode: 1,
    rotatable: false,
    aspectRatio: 20 / 4,
    minCropBoxWidth: 200,
    minCropBoxHeight: 200,
    minContainerHeight: 400,
  }

  $('#id_image').on('change', function () {
    readDataUpload("#id_image", imageCropper)
  })
})

function readDataUpload(id_changed, crop, callback) {
  var $image = $('#image');
  var cropBoxData, canvasData;
  $(id_changed).attr({accept: 'image/*'});

  if ($(id_changed)[0].files && $(id_changed)[0].files[0] && $(id_changed)[0].files[0].name.match(/\.(jpg|png|jpeg|JPG|PNG|JPEG)$/)) {
    let reader = new FileReader();
    reader.onload = function (e) {
      $('#image').attr('src', e.target.result);
      $('#modalCrop').modal('show');
      $(id_changed).attr('src', e.target.result);
    };
    reader.readAsDataURL($(id_changed)[0].files[0]);
  } else if ($(id_changed)[0].files.length == 0) {
    return false;
  } else {
    alert('画像をアップロードしてください。アップロードしたファイルは画像でないか、または壊れています。');
    $(id_changed).val('').clone(true);
  }

  $("#modalCrop").modal({
    show: false,
    backdrop: 'static'
  });

  $('#modalCrop').off().on('shown.bs.modal', function () {
    $image.cropper({
      viewMode: crop.viewMode,
      rotatable: crop.rotatable,
      aspectRatio: crop.aspectRatio,
      minCropBoxWidth: crop.minCropBoxWidth,
      minCropBoxHeight: crop.minCropBoxHeight,
      minContainerHeight: crop.minContainerHeight,
      ready: function () {
        $image.cropper('setCanvasData', canvasData);
        $image.cropper('setCropBoxData', cropBoxData);
      }
    });
    $('.modal:not(#modalCrop)').hide();
  }).on('hidden.bs.modal', function () {
    cropBoxData = $image.cropper('getCropBoxData');
    canvasData = $image.cropper('getCanvasData');
    $('.bootbox.modal:not(#modalCrop)').show();
    var checkLengthImage = function () {
      return $('#pre_image').length == 0 && $('.sale-content__cover-image').length == 0;
    }
    var checkExtendImage = function () {
      return $('.profile__avatar-img')[0].src.match('/default-avatar-creator.png');
    }

      if (id_changed != "#id_banner") {
          if (id_changed == "#id_image" ? checkLengthImage() : checkExtendImage()) {
              if (id_changed !== '#id_sale_content_image') {
                  $(id_changed).val('').clone(true);
              }
          }
      }

    $image.cropper('destroy');
  });

  $(".close").click(function () {
    $(id_changed).val(null);
  });

  $('.js-crop-and-upload').unbind().bind("click", function () {
    var cropData = $image.cropper("getData");
    var croppedImageDataURL = $image.cropper('getCroppedCanvas', {fillColor: '#fff'}).toDataURL("image/png");

    var uploadBanner = function () {
      $image[0].height = cropData['height'];
      $image[0].width = cropData['width'];
      $('.profile__cover-image').css('background-image', 'url(' + croppedImageDataURL + ')');
      $('#id_x_banner').val(cropData['x']);
      $('#id_y_banner').val(cropData['y']);
      $('#id_height_banner').val(cropData['height']);
      $('#id_width_banner').val(cropData['width']);
      $('#modalCrop').modal('hide');
    }

    var uploadAvatar = function () {
      $image[0].height = cropData['height'];
      $image[0].width = cropData['width'];
      $('.profile__avatar-img').attr('src', croppedImageDataURL);
      $('#id_x').val(cropData['x']);
      $('#id_y').val(cropData['y']);
      $('#id_height').val(cropData['height']);
      $('#id_width').val(cropData['width']);
      $('#modalCrop').modal('hide');
    }

    var uploadImage = function () {
      var image_dom = $('#id_image').parents('.form-group');
      if (image_dom.children('#pre_image').length > 0) {
        $('#pre_image').attr('src', croppedImageDataURL);
      } else if ($('#id_image').parents('.sale-content__cover-image').length > 0) {
        $('#id_image').parents('.sale-content__cover-image').css('background-image', 'url(' + croppedImageDataURL + ')');
      } else {
        image_dom.append($('<p style="font-weight:bold">プレビュー: </p><img id="pre_image" style="width:100%">').attr('src', croppedImageDataURL));
      }

      $image[0].width = cropData['width'];
      $image[0].height = cropData['height'];
      $('#id_image').siblings('.no-file').hide();
      $('#id_x').val(cropData['x']);
      $('#id_y').val(cropData['y']);
      $('#id_height').val(cropData['height']);
      $('#id_width').val(cropData['width']);
      $('#modalCrop').modal('hide');
    }

    var uploadSaleContentImage = function () {
      $image[0].height = cropData['height'];
      $image[0].width = cropData['width'];
      $('.x_sale_content_bb').val(cropData['x']);
      $('.y_sale_content_bb').val(cropData['y']);
      $('.height_sale_content_bb').val(cropData['height']);
      $('.width_sale_content_bb').val(cropData['width']);
      $('#modalCrop').modal('hide');
      $('.sale-content-image').css('background-image', 'url('+croppedImageDataURL+')');
    }

    switch (id_changed) {
      case "#id_banner":
        uploadBanner();
        callback();
        break;
      case "#id_avatar":
        uploadAvatar();
        callback();
        break;
      case "#id_image":
        uploadImage();
        break;
      case "#id_sale_content_image":
        uploadSaleContentImage();
        break;
    }
  });


  // Enable zoom in button
  $('.js-zoom-in').click(function () {
    $image.cropper('zoom', 0.1);
  });

  // Enable zoom out button
  $('.js-zoom-out').click(function () {
    $image.cropper('zoom', -0.1);
  });
}
