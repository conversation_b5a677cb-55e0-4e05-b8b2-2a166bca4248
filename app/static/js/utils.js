// 01EG89H6SS2141VNGDDEHBMV4Q
function msToTime(duration) {
    if (duration !== '') {
        let d = Number(duration),
            h = Math.floor(d / 3600),
            m = Math.floor(d % 3600 / 60),
            s = Math.floor(d % 3600 % 60);

        let hDisplay = h > 0 ? (h + ":") : "",
            mDisplay = m > 0 ? (m + ":") : "0:",
            sDisplay = s > 0 ? s < 10 ? "0" + s : s : "00";

        return hDisplay + mDisplay + sDisplay;
    }
    return ''
}

function TimeToSeconds(hms) {
  if (hms == undefined || hms.length == 0) {
    return 0;
  }
  const a = hms.split(':').reverse()
  if (a.length == 1) {
    hms = parseFloat(hms);
    if (isNaN(hms)) {
      return 0;
    }
    return hms;
  } else if (a.length == 2) {
    hms = parseInt(a[1]) * 60 + parseFloat(a[0]);
    if (isNaN(hms)) {
      return 0;
    }
    return hms;
  } else if (a.length == 3) {
    hms = parseInt(a[2]) * 60 * 60 + parseInt(a[1]) * 60 + parseFloat(a[0]);
    if (isNaN(hms)) {
      return 0;
    }
    return hms;
  } else {
    return 0
  }
}

function playVideo(video, current_time = -1) {
  stop_video_audio();
  if (current_time >= 0) {
    video.currentTime = current_time;
  }
  $(video).css('filter', 'none');
  video.play();
  video.setAttribute('controls', 'true');
  video.muted = false;
}

function playWavesurfer(wavesurfer, start_time, scene_pin) {
    stop_video_audio();
    wavesurfer.play();
    if (start_time >= 0) {
        if (wavesurfer.getDuration() > 0) {
            wavesurfer.setCurrentTime(start_time);
        } else {
            wavesurfer.on('waveform-ready', function () {
                wavesurfer.setCurrentTime(start_time);
            });
        }
    }
    wavesurfer.setVolume(1)
}

function stop_video_audio() {
  $('video').each((i, e) => e.pause());
  $('.s-audio-control').each(function () {
    $(this).removeClass('active')
    $(this).closest('.messenger-content').find('.s-audio').removeClass('active');
    $(this).closest('.mmessage-content').find('.s-audio').removeClass('active');
  });
  let ws_arr;
  ws_arr = wavesurfer_arr;
  for (let i = 0; i < ws_arr.length; i++) {
    if (ws_arr[i]) {
      if (ws_arr[i].isPlaying()) {
        ws_arr[i].playPause()
      }
    }
  }
}

function play_video_wave(wavesurfer, video, start_time = 0, target_pin) {
  setTimeout(function () {
    let video_duration = video.duration - start_time;
    let mp3_duration = wavesurfer.getDuration();
    let current_audio = wavesurfer.getCurrentTime();
    let time_play = start_time;
    if (start_time + current_audio >= video_duration) {
      wavesurfer.setCurrentTime(0)
    } else {
      time_play = start_time + current_audio;
    }
    video.currentTime = time_play;
    video.muted = true;
    video.play();
    $(video).css('filter', 'none');
    if (video_duration < mp3_duration) {
      $(video).on('pause', function () {
        wavesurfer.pause();
        target_pin.removeClass('playing')
      });
      wavesurfer.on('seek', function (position) {
        var currentTime = position * wavesurfer.getDuration();
        if (currentTime >= video_duration) {
          target_pin.removeClass('playing');
          video.pause();
        } else {
          video.currentTime = currentTime + start_time
        }
      });
    } else {
      wavesurfer.on('pause', function () {
        let wavesurfer_current_time = wavesurfer.getCurrentTime();
        if (wavesurfer_current_time === mp3_duration) {
          video.pause();
          target_pin.removeClass('playing')
        }
      });
      wavesurfer.on('seek', function (position) {
        var currentTime = position * wavesurfer.getDuration();
        video.currentTime = currentTime + start_time
      });
    }
  }, 1000)
}


function play_wave_wave(scene_wavesurfer, wavesurfer, start_time = 0, target_pin, scene_pin) {
    let scene_duration = 0;
    if (scene_wavesurfer.getDuration()) {
        scene_duration = scene_wavesurfer.getDuration() - start_time;
    }
    let mp3_duration = wavesurfer.getDuration();
    let current_audio = wavesurfer.getCurrentTime();
    if (!current_audio) {
        current_audio = 0
    }
    let time_play = start_time;
    if (scene_duration > 0 && start_time + current_audio >= scene_duration) {
      wavesurfer.setCurrentTime(0)
    } else if (scene_duration > 0) {
      time_play = start_time + current_audio;
    }
    scene_wavesurfer.setCurrentTime(time_play);
    scene_wavesurfer.setVolume(0);
    scene_wavesurfer.play();
    if (scene_wavesurfer.getDuration()) {
        scene_duration = scene_wavesurfer.getDuration() - start_time;
    }
    scene_pin.addClass('playing');
    if (scene_duration < mp3_duration) {
      scene_wavesurfer.on('pause', function () {
        wavesurfer.pause();
        scene_wavesurfer.setVolume(1);
        target_pin.removeClass('playing');
        scene_pin.removeClass('playing');
      });
      wavesurfer.on('seek', function (position) {
        var currentTime = position * wavesurfer.getDuration();
        if (currentTime >= scene_duration) {
          scene_wavesurfer.pause();
          scene_wavesurfer.setVolume(1);
          target_pin.removeClass('playing');
          scene_pin.removeClass('playing');
        } else {
            scene_wavesurfer.setCurrentTime(currentTime + start_time);
        }
      });
    } else {
      wavesurfer.on('pause', function () {
        let wavesurfer_current_time = wavesurfer.getCurrentTime();
        if (wavesurfer_current_time >= mp3_duration) {
          scene_wavesurfer.pause();
          scene_wavesurfer.setVolume(1);
          target_pin.removeClass('playing');
          scene_pin.removeClass('playing');
        }
      });
      wavesurfer.on('seek', function (position) {
        var currentTime = position * wavesurfer.getDuration();
        scene_wavesurfer.setCurrentTime(currentTime);
      });
    }
}

function play_video_ready(wavesurfer, video, start_time = 0, target_pin) {
  setTimeout(function (){
    $(".project-item__content").find(".loader").hide();
    wavesurfer.play();
    target_pin.addClass('playing');
  }, 800)

  if (wavesurfer.getDuration() > 0) {
    play_video_wave(wavesurfer, video, start_time, target_pin)
  } else {
    let src = $(wavesurfer.mediaContainer).siblings('.s-audio-source');
    let link = src.attr('data-link');
    wavesurfer.load(link);
    wavesurfer.on('waveform-ready', function () {
      play_video_wave(wavesurfer, video, start_time, target_pin)
    });
  }
}

function playPinWavesurfer(wavesurfer, video, start_time = 0, target_pin) {
  $('video').each(function (i, e) {
    e.pause();
  });
  let is_play = false;
  for (let i = 0; i < wavesurfer_arr.length; i++) {
    if (wavesurfer_arr[i]) {
      if (wavesurfer_arr[i].isPlaying()) {
        if (wavesurfer_arr[i] === wavesurfer) {
          is_play = true;
        }
        wavesurfer_arr[i].pause();
      }
    }
  }


  $('.video-pin-time').each(function () {
    $(this).removeClass('playing')
  });

  if (!is_play) {
    $(".project-item__content").find(".loader").show();
    if (video.readyState === 4) {
      play_video_ready(wavesurfer, video, start_time, target_pin);
      $(video).off('canplaythrough');
    } else {
      video.load();
      $(video).off('canplaythrough').on('canplaythrough', function () {
        play_video_ready(wavesurfer, video, start_time, target_pin);
        $(video).off('canplaythrough');
      });
    }
  }
}


function playWavesurferWavesurfer(scene_wavesurfer, wavesurfer, start_time = 0, target_pin, scene_pin) {
    //stop_video_audio();
    let is_play = false;
    for (let i = 0; i < wavesurfer_arr.length; i++) {
        if (wavesurfer_arr[i]) {
            if (wavesurfer_arr[i].isPlaying()) {
                if (wavesurfer_arr[i] === wavesurfer) {
                    is_play = true;
                }
                wavesurfer_arr[i].playPause();
            }
        }
    }

    $('.video-pin-time').each(function () {
        $(this).removeClass('playing')
    });

    if (!is_play) {
        if (!wavesurfer.getDuration()) {
            loadWave(wavesurfer)
        }

        if (!scene_wavesurfer.getDuration()) {
            loadWave(scene_wavesurfer)
        }

        setTimeout(function () {
            target_pin.addClass('playing');
            if (wavesurfer.getDuration()) {
                wavesurfer.play();
                play_wave_wave(scene_wavesurfer, wavesurfer, start_time, target_pin, scene_pin)
            } else {
                let src = $(wavesurfer.mediaContainer).siblings('.s-audio-source');
                let link = src.attr('data-link');
                if (wavesurfer.loaded) {
                    wavesurfer.play();
                    play_wave_wave(scene_wavesurfer, wavesurfer, start_time, target_pin, scene_pin)
                } else {
                    loadWave(wavesurfer);
                    wavesurfer.play();
                    wavesurfer.on('waveform-ready', function () {
                        play_wave_wave(scene_wavesurfer, wavesurfer, start_time, target_pin, scene_pin)
                    });
                }
            }
        }, 1000);
    }
}

function loadWave(wavesurfer) {
    let index_wave = wavesurfer_arr.indexOf(wavesurfer);
    let parent_wave = $('.s-audio[data-wavesurfer^=' + index_wave + ']');
    let link = parent_wave.find('.s-audio-source').attr('data-link');
    let peaks_cmt = parent_wave.find('.s-audio-source').attr('data-peaks-loaded');
    let cmt_peaks = [];
    if (peaks_cmt) {
        cmt_peaks = peaks_cmt.split(" ").map(Number);
        wavesurfer.load(link, cmt_peaks);
        wavesurfer.loaded = true
    } else {
        wavesurfer.load(link);
    }
}

function play_video_pin(video, current_time = -1) {
  $('video').each((i, e) => e.pause());
  if (current_time >= 0) {
    video.currentTime = current_time;
  }
  $(video).css('filter', 'none');
  video.play();
  video.setAttribute('controls', 'true');
}

function playAudio(video, audio, current_time = -1) {
  $('audio').each((i, e) => e.pause());
  if (current_time >= 0) {
    audio.currentTime = current_time;
  }
  audio.muted = false;
  audio.play();
  video.muted = true;
  stopVideo(video)
}

function stopVideo(video) {
  $('.video-pin-time').each(function () {
    $(this).removeClass('active');
  });
  video.pause();
}

function stopAudio(audio) {
  audio.pause();
}

function formatTime(time) {
  return (("0" + Math.floor(time / 60)).slice(-2) + ':' + ("0" + Math.floor(time % 60)).slice(-2));
}

function UpdateUrl(link, params) {
  let url = new URL(link);
  let query_string = url.search;
  let search_params = new URLSearchParams(query_string);
  for (let key in params) {
    if (search_params.has(key)) {
      search_params.set(key, params[key]);
    } else {
      search_params.append(key, params[key]);
    }
  }
  url.search = search_params.toString();
  return url.toString();
}

function initCollapseButton($project_video_item) {
  $project_video_item.find('.video-item-collapse-button').off().on('click', function () {
    if (!$(this).hasClass('active')) {
      $(this).addClass('active');
      $project_video_item.addClass('active');
      if (!$('.project-item.active .show-comment').length) {
        $project_video_item.parents('.project-item').find('.project-video-item:not(.active)').addClass('hide');
      }
    } else {
      $(this).removeClass('active');
      $project_video_item.removeClass('active');
      if (!$('.project-item.active .show-comment').length) {
        $project_video_item.parents('.project-item').find('.project-video-item').removeClass('hide');
      }
    }
  });
}

function handleClickVersion(target) {
  let video_item_list = target.parent('.video-item-component').siblings('.video-item-component')
  let num_of_video_item = video_item_list.length + 1;
  let self_index = target.parent().index();

  //stop this video
  let self_video = target.parent().find('video').get(0);
  if (self_video) {
      stopVideo(self_video);
  }

  //deactive self video item and play next videos
  target.parent().removeClass('active playing');
  let next_index = (self_index + 1) % num_of_video_item;
  let next_video_item_component = target.parents('.video-item-list').find('.video-item-component').eq(next_index);
  next_video_item_component.addClass('active playing');
  if (next_video_item_component.find('video').length > 0) {
      playVideo(next_video_item_component.find('video').get(0));
  }

  //update tag
  if (next_index === 0) {
    next_video_item_component.find('.version-tag').attr('data-content', '');
    next_video_item_component.find('.version-tag').css('background', '#009ace');
    next_video_item_component.find('video').removeClass('gray-scale');
  } else {
    next_video_item_component.find('.version-tag').css('background', '#53565A');
    next_video_item_component.find('video').addClass('gray-scale');
  }
}

function handleBulletClick($project_video_item, min_index = 0, max_index, target) {
  var bullet_index = target.data('index');
  target.addClass('active').siblings('.video-item-bullet').removeClass('active');
  // Do something
  let video_item_list = $project_video_item.find('.video-item-list');
  video_item_list.removeClass('active').addClass('hide');
  let old_video = video_item_list.find('.video-item-component.active');
  stopVideo(old_video.find('video').get(0));
  old_video.removeClass('active');

  let new_video_item_list = $project_video_item.find('.video-item-list').eq(bullet_index);
  new_video_item_list.removeClass('hide').addClass('active');
  let new_video = new_video_item_list.find('.video-item-component.playing').first();
  if (new_video.length !== 1) {
    new_video = new_video_item_list.find('.video-item-component').first();
  }

  playVideo(new_video.find('video').get(0));
  let pins = new_video.find('video').parents('.project-video-item.show-comment').find('.video-item-comment .pin-icon-time');
  pins.each(function (i, e) {
    if ($(e).parents('.video-comment-input-pin').is('.active')) {
      $(e).html(msToTime(new_video.find('video').get(0).currentTime));
    }
  });
  new_video.addClass('active');
  // Prev Button Data Update
  $project_video_item.find('.video-item-bullet-prev').attr('data-current_index', bullet_index);

  if (bullet_index === min_index) {
    $project_video_item.find('.video-item-bullet-prev').addClass('disable');
  } else {
    $project_video_item.find('.video-item-bullet-prev').removeClass('disable');
  }
  // Next Button Data Update
  $project_video_item.find('.video-item-bullet-next').attr('data-current_index', bullet_index);
  if (bullet_index === max_index) {
    $project_video_item.find('.video-item-bullet-next').addClass('disable');
  } else {
    $project_video_item.find('.video-item-bullet-next').removeClass('disable');
  }
  // Update Thumbnails
  $project_video_item.find('.video-item-thumbnail').removeClass('active');
  $project_video_item.find('.video-item-thumbnail').eq(bullet_index).addClass('active');
}

function initThumbnailClick($project_video_item) {
  $project_video_item.find('.video-item-thumbnail').off().on('click', function () {
    let index = parseInt($(this).attr('data-index'));
    if (!$(this).hasClass('active')) {
      $project_video_item.find('.video-item-thumbnail').removeClass('active');
      $(this).addClass('active');
      // Update bullet
      $project_video_item.find('.video-item-bullet').eq(index).trigger('click');
    }
  });
}

function generateDownloadLink(event) {
  event.stopPropagation();
  let comment_id = $(event.target).parents('.video-comment-item-reply').attr('data-cmt-id');
  $.ajax({
    type: "GET",
    url: "/top/get_file_download_link",

    data: {
      "comment_id": comment_id,
      'type': 'scene'
    },
    beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
      window.location.href = response.url;
    },
    fail: function (response) {
      toastr.error('エラーが発生しました', 'ファイルをダウンロード');
    }
  })
}

function openModalPauseVideo() {
  $(document).on('shown.bs.modal', function () {
      stop_video_audio();
  });

  $(document).on('hidden.bs.modal', function () {
      stop_video_audio();
  });
}

function isValidHttpUrl(string) {
  let url;
  
  try {
    url = new URL(string);
  } catch (_) {
    return false;  
  }

  return url.protocol === "http:" || url.protocol === "https:";
}

function showToastPlayMix(music, voice, sound) {
  let count = 0;
  count = music ? count + 1 : count
  count = voice ? count + 1 : count
  count = sound ? count + 1 : count
  if (count > 1) {
    let message = "";
    if (count == 3) {
      message = "<div style='display: flex; align-items: center;'>MUSIC<span style='font-size: 8px; padding: 4px 5px; line-height: 150%;'>✕</span>SOUND EFFECTS<span style='font-size: 8px; padding: 4px 5px; line-height: 150%;'>✕</span>VOICE</div>";
    } else {
      if (music) {
        if (sound) {
          message = "<div style='display: flex; align-items: center;'>MUSIC<span style='font-size: 8px; padding: 4px 5px; line-height: 150%;'>✕</span>SOUND EFFECTS</div>";
        } else {
          message = "<div style='display: flex; align-items: center;'>MUSIC<span style='font-size: 8px; padding: 4px 5px; line-height: 150%;'>✕</span>VOICE</div>";
        }
      } else {
        message = "<div style='display: flex; align-items: center;'>SOUND EFFECTS<span style='font-size: 8px; padding: 4px 5px; line-height: 150%;'>✕</span>VOICE</div>";
      }
    }
    
    toastr.info(message, "", {
      titleClass: "system-toast-title",
      toastClass: "system-toast",
      messageClass: "system-toast-message",
      timeOut: 3000,
    });
  }
}

function arrayUnique(array) {
  var a = array.concat();
  for (var i = 0; i < a.length; ++i) {
      for (var j = i + 1; j < a.length; ++j) {
          if (a[i] === a[j])
              a.splice(j--, 1);
      }
  }

  return a;
}

var circles = document.querySelectorAll('.circle');

function updateSVG(time) {
    circles.forEach((circle, index) => {
        const angle = time * (2 - index / 10) / 5;
        circle.setAttribute('transform', `rotate(${angle} 0 0)`);
    });

    requestAnimationFrame(updateSVG);
}

function removeLoadAnimation() {
  $('#loading_animation').remove();
}

function animationLoading(time) {
  let circles = document.querySelectorAll('.circle');
  circles.forEach((circle, index) => {
      const angle = time * (2 - index / 10) / 5;
      circle.setAttribute('transform', `rotate(${angle} 0 0)`);
  });

  requestAnimationFrame(animationLoading);
}

function addLoadingAnimation() {
  $('#loading_animation').empty()

  $('#loading_animation').append(`<svg class='loader-example' viewBox='0 0 100 100'>
    <defs>
        <filter id='goo'>
            <feGaussianBlur in='SourceGraphic' stdDeviation='8' result='blur' />
            <feColorMatrix in='blur' mode='matrix' values='1 0 0 0 0
                                                          0 1 0 0 0
                                                          0 0 1 0 0
                                                          0 0 0 25 -8' result='goo' />
            <feBlend in='SourceGraphic' in2='goo' />
        </filter>
    </defs>
    <g filter='url(#goo)' fill='#f0f0f0' stroke='#fcfcfc'>
        <g transform='translate(50, 50)'>
            <g class='circle -a'>
                <g transform='translate(-50, -50)'>
                    <circle cx='25' cy='50' r='9' />
                </g>
            </g>
        </g>
        <g transform='translate(50, 50)'>
            <g class='circle -b'>
                <g transform='translate(-50, -50)'>
                    <circle cx='50' cy='25' r='8'  />
                </g>
            </g>
        </g>
        <g transform='translate(50, 50)'>
            <g class='circle -c'>
                <g transform='translate(-50, -50)'>
                    <circle cx='75' cy='50' r='7' />
                </g>
            </g>
        </g>
        <g transform='translate(50, 50)'>
            <g class='circle -d'>
                <g transform='translate(-50, -50)'>
                    <circle cx='50' cy='75' r='6' />
                </g>
            </g>
        </g>
        <g transform='translate(50, 50)'>
            <g class='circle -e'>
                <g transform='translate(-50, -50)'>
                    <circle cx='25' cy='50' r='5' />
                </g>
            </g>
        </g>
        <g transform='translate(50, 50)'>
            <g class='circle -f'>
                <g transform='translate(-50, -50)'>
                    <circle cx='50' cy='25' r='4' />
                </g>
            </g>
        </g>
        <g transform='translate(50, 50)'>
            <g class='circle -g'>
                <g transform='translate(-50, -50)'>
                    <circle cx='75' cy='50' r='3' />
                </g>
            </g>
        </g>
        <g transform='translate(50, 50)'>
            <g class='circle -h'>
                <g transform='translate(-50, -50)'>
                    <circle cx='50' cy='75' r='2' />
                </g>
            </g>
        </g>
    </g>
  </svg>`)
  animationLoading(0)
  $('#loading_animation').removeClass('hide')
}

function removeLoadingAnimation() {
  $('#loading_animation').addClass('hide')
  $('#loading_animation').empty()
}

function checkSafari() {
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
}