function initReadyProfileFooter() {
    clickChangeLanguage();
    checkNewlineFooterMenu();
    initModalEditFooter();
    initSortableFooterMenu();
    initClickFooterButton()
}

function clickChangeLanguage() {
    $(document).on('click', 'footer .change-language-button, .header-profile-sp-dropdown .change-language-button span', function(e) {
        e.stopPropagation();
        changeButtonLanguageState($(this));
        changeContent();
        checkNewlineFooterMenu();
    })

    if(checkCurrentLan()) {
        $('footer .change-language-button').eq(0).trigger('click');
    }
}

function changeButtonLanguageState(target) {
    if(!target.is('span') || (target.is('span') && target.attr('data-select') != target.parents('.change-language-button').attr('data-language'))) {
        if (target.parents('.profile-footer-lower, .header-profile-sp-dropdown').find('.change-language-button').attr('data-language') == 'jp') {
            $('.change-language-button').attr('data-language', 'en')
            $('footer .change-language-button').find('span').text('JP')
            $('.profile.header-fullscreen').addClass('lang-en')
            var refresh = window.location.protocol + "//" + window.location.host + window.location.pathname + '?lan=en';
        } else {
            $('.change-language-button').attr('data-language', 'jp')
            $('footer .change-language-button').find('span').text('EN')
            $('.profile.header-fullscreen').removeClass('lang-en')
            var refresh = window.location.protocol + "//" + window.location.host + window.location.pathname;
        }

        window.history.pushState({ path: refresh }, '', refresh);
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
}

function changeContent() {
    let is_jp = $('.change-language-button').attr('data-language') == 'jp';
    //Catch Phrase
    let cp1 = $('.profile-artist-catch-phrase').attr('data-catchphrase-1');
    let cp1_en = $('.profile-artist-catch-phrase').attr('data-catchphrase-1-en');
    let cp2 = $('.profile-artist-catch-phrase').attr('data-catchphrase-2');
    let cp2_en = $('.profile-artist-catch-phrase').attr('data-catchphrase-2-en');
    let cp3 = $('.profile-artist-catch-phrase').attr('data-catchphrase-3');
    let cp3_en = $('.profile-artist-catch-phrase').attr('data-catchphrase-3-en');

    let cp1_final = is_jp ? cp1 : cp1_en
    if(cp1_final == '') {
        cp1_final = is_jp ? cp1_en : cp1
    }

    let cp2_final = is_jp ? cp2 : cp2_en
    if(cp2_final == '') {
        cp2_final = is_jp ? cp2_en : cp2
    }

    let cp3_final = is_jp ? cp3 : cp3_en
    if(cp3_final == '') {
        cp3_final = is_jp ? cp3_en : cp3
    }

    $('.profile-artist-catch-phrase span').eq(0).text(cp1_final)
    $('.profile-artist-catch-phrase span').eq(1).text(cp2_final)
    $('.profile-artist-catch-phrase span').eq(2).text(cp3_final)

    resizePhrase('25vw')

    //Tagline
    let tl1 = $('.profile-artist-info-name').attr('data-tagline-1');
    let tl1_en = $('.profile-artist-info-name').attr('data-tagline-1-en');
    let tl2 = $('.profile-artist-info-title').attr('data-tagline-2');
    let tl2_en = $('.profile-artist-info-title').attr('data-tagline-2-en');

    let tl1_final = is_jp ? tl1 : tl1_en
    let tl1_final_reverse = is_jp ?  tl1_en : tl1
    if(tl1_final == '') {
        tl1 = $('.profile-artist-info-name').attr('data-tagline-1-initial');
        tl1_en = $('.profile-artist-info-name').attr('data-tagline-1-initial-en');
        tl1_final = is_jp ? tl1 : tl1_en
        if(tl1_final == '') {
            if(tl1_final_reverse == '') {
                tl1_final = is_jp ?  tl1_en : tl1
            } else {
                tl1_final = tl1_final_reverse
            }
        }
    }

    let tl2_final = is_jp ? tl2 : tl2_en
    let tl2_final_reverse = is_jp ?  tl2_en : tl2
    if(tl2_final == '') {
        tl2 = $('.profile-artist-info-title').attr('data-tagline-2-initial');
        tl2_en = $('.profile-artist-info-title').attr('data-tagline-2-initial-en');
        tl2_final = is_jp ? tl2 : tl2_en
        if(tl2_final == '') {
            if(tl2_final_reverse == '') {
                tl2_final = is_jp ?  tl2_en : tl2
            } else {
                tl2_final = tl2_final_reverse
            }
        }
    }

    $('.profile-artist-info-name').text(tl1_final)
    $('.profile-artist-info-title').text(tl2_final)

    //Statement
    resizeQuote();

    //Artist + Title
    let artist_name = $('.statement-info-name').attr('data-artist-name');
    let artist_name_en = $('.statement-info-name').attr('data-artist-name-en');
    let artist_final = is_jp ? artist_name : artist_name_en
    if(artist_final == '') {
        artist_final = is_jp ? artist_name_en : artist_name
    }

    $('.statement-info-name').text(artist_final)

    let job_title = $('.statement-info-title').attr('data-title');
    let job_title_en = $('.statement-info-title').attr('data-title-en');
    let job_final = is_jp ? job_title : job_title_en
    if(job_final == '') {
        job_final = is_jp ? job_title_en : job_title
    }

    $('.statement-info-title').text(job_final)

    // Title page
    let titlePage = artist_final;
    if (job_final && job_final !== '') {
        titlePage += ' | ' + job_final;
    }
    $('title').text(titlePage);

    //Profile Title
    let profile_title = $('.profile__profile .profile__profile-quote').attr('data-title')
    let profile_title_en = $('.profile__profile .profile__profile-quote').attr('data-title-en')
    let title_final = is_jp ? profile_title : profile_title_en
    if(title_final == '') {
        title_final = is_jp ? profile_title_en : profile_title
    }
    $('.profile__profile .profile__profile-title').text(title_final)

    //textnavbar
    let nav_btn = $('.header-profile-artist .sheader-link.sheader-link-block[href="#profile"]')
    if(nav_btn.length) {
        nav_btn.text(title_final)
    }

    //Profile Text
    let profile_text = $('.profile__profile-quote').attr('data-title-text')
    let profile_text_en = $('.profile__profile-quote').attr('data-title-text-en')
    let profile_text_final = is_jp ? profile_text : profile_text_en
    if(profile_text_final == '') {
        profile_text_final = is_jp ? profile_text_en : profile_text
    }

    profile_text = $('.profile__profile-quote span').text(profile_text_final)

    //Footer
    //Menu + Sub
    $('.profile-footer-upper .profile-footer-menu-item, .profile-footer-lower-menu .profile-footer-menu-item').each(function(i, e) {
        let title = $(e).attr('data-title')
        let title_en = $(e).attr('data-title-en')
        let title_final = is_jp ? title : title_en
        if(title_final == '') {
            title_final = is_jp ? title_en : title
        }
        $(e).text(title_final)
    })
    //Copyright
    let cpright = $('.profile-footer-copyright').attr('data-copyright')
    if(cpright) {
        $('.profile-footer-copyright').text(cpright)
    } else {
        $('.profile-footer-copyright').text('©SOREMO Co., ltd. All right reserved.')
    }
}

function checkNewlineFooterMenu () {
    let items = $('.profile-footer-upper .profile-footer-menu-item')
    let last = -1

    items.removeClass('end-of-line no-after')
    items.each(function(i, e) {
        last = last == -1 ? e : last
        if(e.offsetTop != last.offsetTop) {
            $(last).addClass('end-of-line')
            if(e.offsetTop == last.offsetTop) {
                $(last).removeClass('end-of-line')
                $(e).addClass('end-of-line')
            }
        } else {
            $(last).removeClass('end-of-line no-after')
            if(e.offsetTop != last.offsetTop) {
                $(last).addClass('end-of-line no-after')
            }
        }
        last = e;
    })

    let lower_items = $('.profile-footer-lower .profile-footer-menu-item')
    let lowerlast = -1
    lower_items.removeClass('end-of-line no-after')
    lower_items.each(function(i, e) {
        lowerlast = lowerlast == -1 ? e : lowerlast
        if(e.offsetTop != lowerlast.offsetTop) {
            $(lowerlast).addClass('end-of-line')
            if(e.offsetTop == lowerlast.offsetTop) {
                $(lowerlast).removeClass('end-of-line')
                $(e).addClass('end-of-line')
            }
        } else {
            $(lowerlast).removeClass('end-of-line no-after')
            if(e.offsetTop != lowerlast.offsetTop) {
                $(lowerlast).addClass('end-of-line no-after')
            }
        }

        lowerlast = e;
    })
}

function initModalEditFooter() {
    $('#modal_profile_footer.modal').addClass('long-modal')
    $('#modal_profile_footer.modal .popup-body').addClass('nice-scroll')

    $(document).on('click', '.editable.profile-footer, .new.profile-footer', function(e) {
        if ($(e.target).is('.approve-btn') || $(e.target).is('.new-button')) {
            return;
        } else if ($(this).is('.new')) {
            $(this).attr({'data-check': true});
            $(this).removeAttr('data-approve data-rejected');
            $(this).find('.menu-checking').addClass('hide');
        }
        prepareDataModalEditFooter();
        $('#modal_profile_footer').modal('show')
    })

    $(document).on('click', '#modal_profile_footer .btn-submit-profile-footer', function() {
        let result_change = updateDataModalEditFooter();
        if (result_change){
            submitBlock('footer', $(this))
        }
        $('#modal_profile_footer').modal('hide')
    })

    $(document).on('click', '#modal_profile_footer .btn-close-profile-footer', function() {
        $('footer.new').attr('data-edited', '')
        $('#modal_profile_footer').modal('hide')
    })

    $(document).on('click', '.footer-menu-main .item-main, .footer-menu-social .item-main, .footer-menu-sub .item-main', function (e) {
        let is_new = $(e.target).is('.add-item') || $(e.target).parents('.add-item').length;
        let target_id = $(e.target).parents('.item-container').attr('data-id');
        if($(e.target).parents('.footer-menu-main').length) {
            openSubModalFooter('main', is_new, target_id);
        } else if($(e.target).parents('.footer-menu-social').length) {
            if(is_new && $('.footer-menu-social').find('.item-container').length > 8) {
                toastr.error("最大リンク数は8です！");
                return;
            }
            openSubModalFooter('social', is_new, target_id);
        } else {
            openSubModalFooter('sub', is_new, target_id);
        }
    })

    $(document).on('click', '#modal_profile_footer_menu .btn-close-profile-footer-menu, #modal_profile_footer_social .btn-close-profile-footer-social', function () {
        $(this).parents('.modal#modal_profile_footer_menu, .modal#modal_profile_footer_social').modal('hide')
        $('body').addClass('modal-open');
    })

    $(document).on('click', '#modal_profile_footer_menu .btn-submit-profile-footer-menu', function (e) {
        updateDataToModalEditFooter(e.target);
        $(this).parents('.modal#modal_profile_footer_menu').modal('hide')
        $('body').addClass('modal-open');
    })

    $(document).on('click', '#modal_profile_footer_social .btn-submit-profile-footer-social', function (e) {
        updateDataToModalEditFooter(e.target);
        $(this).parents('.modal#modal_profile_footer_social').modal('hide')
        $('body').addClass('modal-open');
    })

    $(document).on('click', '#modal_profile_footer  .item-container .icon-delete', function (e) {
        $('#modal_profile_footer_confirm_delete').modal('show')
        $('#modal_profile_footer_confirm_delete').attr('data-delete-id', $(e.target).parents('.item-container').attr('data-id'))
        if($(e.target).parents('.footer-menu-main').length) {
            $('#modal_profile_footer_confirm_delete').attr('data-delete-type', 'main')
        } else if($(e.target).parents('.footer-menu-social').length) {
            $('#modal_profile_footer_confirm_delete').attr('data-delete-type', 'social')
        } else {
            $('#modal_profile_footer_confirm_delete').attr('data-delete-type', 'sub')
        }
    })

    $(document).on('click', '#modal_profile_footer_confirm_delete .btn-close-profile-footer-confirm-delete', function () {
        $('#modal_profile_footer_confirm_delete').modal('hide')
        $('body').addClass('modal-open');
        $('#modal_profile_footer_confirm_delete').attr('data-delete-id', '')
        $('#modal_profile_footer_confirm_delete').attr('data-delete-type', '')
    })

    $(document).on('click', '#modal_profile_footer_confirm_delete .btn-submit-profile-footer-confirm-delete', function () {
        let delete_id = $('#modal_profile_footer_confirm_delete').attr('data-delete-id')
        if($('#modal_profile_footer_confirm_delete').attr('data-delete-type') == 'main') {
            $('.footer-menu-main .item-container[data-id=' + delete_id +']').remove();
        } else if($('#modal_profile_footer_confirm_delete').attr('data-delete-type') == 'social') {
            $('.footer-menu-social .item-container[data-id=' + delete_id +']').remove();

            if($('.footer-menu-social .item-container').length > 8) {
                $('.footer-menu-social .item-container .item-main.add-item').addClass('btn--disabled');
            } else {
                $('.footer-menu-social .item-container .item-main.add-item').removeClass('btn--disabled');
            }
        } else {
            $('.footer-menu-sub .item-container[data-id=' + delete_id +']').remove();
        }
        $('#modal_profile_footer_confirm_delete').modal('hide')
        $('body').addClass('modal-open');
        $('#modal_profile_footer_confirm_delete').attr('data-delete-id', '')
        $('#modal_profile_footer_confirm_delete').attr('data-delete-type', '')
    })

    $(document).on('keyup', '#modal_profile_footer_menu input, #modal_profile_footer_social input', function() {
        validateFooterSubModal();
    })
}

function initSortableFooterMenu() {
    $('#modal_profile_footer .popup-body .footer-menu-main, #modal_profile_footer .popup-body .footer-menu-social, #modal_profile_footer .popup-body .footer-menu-sub').sortable({
        axis: 'y',
        items: '.item-container',
        handle: '.item-button-icon.icon-drag',
        tolerance: "pointer",
        helper: 'clone',
        start: function (event, ui) {
            ui.placeholder.height(ui.helper.outerHeight());
        },
        change: function (event, ui) {
        },
        update: function (event, ui) {
        },
    })
}

function prepareDataModalEditFooter() {
    //menu-main
    let items_main = $('.profile-footer-upper .profile-footer-menu-item')
    let menu_main = $('#modal_profile_footer .footer-menu-main');
    menu_main.find('.item-container').remove();
    items_main.each(function(i, e) {
        menu_main.append(generateItemMenuFooter($(e).attr('data-id'), $(e).attr('data-title'), $(e).attr('data-title-en'), $(e).attr('data-url')))
    })

    menu_main.append(generateAddItem('main'))
    //menu-social
    let items_social = $('.profile-footer-upper-right .profile-footer-menu-icon')
    let menu_social = $('#modal_profile_footer .footer-menu-social');
    menu_social.find('.item-container').remove();
    items_social.each(function(i, e) {
        menu_social.append(generateItemSocialFooter($(e).attr('data-id'), $(e).attr('data-social-icon'), $(e).attr('data-url')))
    })

    menu_social.append(generateAddItem('social'))

    if($('.footer-menu-social .item-container').length > 8) {
        $('.footer-menu-social .item-container .item-main.add-item').addClass('btn--disabled');
    } else {
        $('.footer-menu-social .item-container .item-main.add-item').removeClass('btn--disabled');
    }

    //copyright
    let current_footer = $('footer .profile-footer-copyright')
    $('#id_copyright').val(current_footer.attr('data-copyright'))

    //menu-sub
    let items_sub = $('.profile-footer-lower-menu .profile-footer-menu-item')
    let menu_sub = $('#modal_profile_footer .footer-menu-sub');
    menu_sub.find('.item-container').remove();
    items_sub.each(function(i, e) {
        menu_sub.append(generateItemMenuFooter($(e).attr('data-id'), $(e).attr('data-title'), $(e).attr('data-title-en'), $(e).attr('data-url')))
    })
    menu_sub.append(generateAddItem('sub'))
}

function updateDataModalEditFooter() {
    //menumain
    $('.profile-footer-upper .profile-footer-menu-item').addClass('data-checking');
    let is_change = false;
    let menu_main = $('#modal_profile_footer .footer-menu-main');
    menu_main.find('.item-container .item-main:not(.add-item)').each(function(i, e) {
        let item = $(e).parents('.item-container')
        let id = item.attr('data-id')
        let title = item.find('.item-main-column1-menu-title').text()
        let title_en = item.find('.item-main-column1-menu-title-en').text()
        let url = item.find('.item-main-column2-menu-link').text()
        let target_item = $('.profile-footer-upper .profile-footer-menu-item[data-id=' + id + ']')
        if(target_item.length) {
            target_item.removeClass('data-checking')
            if(target_item.attr('data-title') != title) {
                target_item.attr('data-title', title)
                target_item.text(title)
                is_change = true;
            }

            if(target_item.attr('data-title-en') != title_en) {
                target_item.attr('data-title-en', title_en)
                is_change = true;
            }

            if(target_item.attr('data-url') != url) {
                target_item.attr('data-url', url)
                is_change = true;
            }

            if(target_item.attr('data-order') != i) {
                target_item.attr('data-order', i)
                is_change = true;
            }
        } else {
            is_change = true;
            $('.profile-footer-upper').append(`<span class="profile-footer-menu-item"
                          data-title="${title}"
                          data-content-title="${title}"
                          data-title-en="${title_en}"
                          data-content-title-en="${title_en}"
                          data-url="${url}"
                          data-content-url="${url}"
                          data-id="${id}"
                          data-order="${i}"
                          data-content-order="${i}">${title}</span>`)
        }
    })

    $('.profile-footer-upper .profile-footer-menu-item.data-checking').each(function(i, e) {
        is_change = true;
        let id = $(e).attr('data-id')
        if(!id.includes('new_') && listMenuMainItemDeleted.indexOf(id) === -1) {
            listMenuMainItemDeleted.push(id)
        }
        $(e).remove()
    })

    //menusocial
    $('.profile-footer-upper-right .profile-footer-menu-icon').addClass('data-checking');
    let menu_social = $('#modal_profile_footer .footer-menu-social');
    menu_social.find('.item-container .item-main:not(.add-item)').each(function(i, e) {
        let item = $(e).parents('.item-container')
        let id = item.attr('data-id')
        let icon = item.find('.item-main-column1-menu-icon').attr('data-social-icon')
        let url = item.find('.item-main-column2-menu-link').text()
        let target_item = $('.profile-footer-upper-right .profile-footer-menu-icon[data-id=' + id + ']')
        if(target_item.length) {
            target_item.removeClass('data-checking')
            if(target_item.attr('data-social-icon') != icon) {
                target_item.attr('data-social-icon', icon)
                is_change = true;
            }

            if(target_item.attr('data-url') != url) {
                target_item.attr('data-url', url)
                is_change = true;
            }

            if(target_item.attr('data-order') != i) {
                target_item.attr('data-order', i)
                is_change = true;
            }
        } else {
            is_change = true;
            $('.profile-footer-upper-right').append(`<span class="profile-footer-menu-icon"
                          data-social-icon="${icon}"
                          data-content-social-icon="${icon}"
                          data-url="${url}"
                          data-content-url="${url}"
                          data-id="${id}"
                          data-order="${i}"
                          data-content-order="${i}"></span>`)
        }
    })

    $('.profile-footer-upper-right .profile-footer-menu-icon.data-checking').each(function(i, e) {
        is_change = true;
        let id = $(e).attr('data-id')
        if(!id.includes('new_') && listMenuSocialItemDeleted.indexOf(id) === -1) {
            listMenuSocialItemDeleted.push(id)
        }
        $(e).remove()
    })

    //copyright
    let input_copyright = $('#id_copyright').val().trim()
    let current_copy_right = $('.profile-footer-copyright').attr('data-copyright')

    if(input_copyright != current_copy_right) {
        if(input_copyright == '') {
            $('.profile-footer-copyright').text('©SOREMO Co., ltd. All right reserved.')
        } else {
            $('.profile-footer-copyright').text('©' + input_copyright)
        }
        is_change = true;
        $('.profile-footer-copyright').attr('data-copyright', input_copyright)
    }

    //menusub
    $('.profile-footer-lower-menu .profile-footer-menu-item').addClass('data-checking');
    let menu_sub = $('#modal_profile_footer .footer-menu-sub');
    menu_sub.find('.item-container .item-main:not(.add-item)').each(function(i, e) {
        let item = $(e).parents('.item-container')
        let id = item.attr('data-id')
        let title = item.find('.item-main-column1-menu-title').text()
        let title_en = item.find('.item-main-column1-menu-title-en').text()
        let url = item.find('.item-main-column2-menu-link').text()
        let target_item = $('.profile-footer-lower-menu .profile-footer-menu-item[data-id=' + id + ']')
        if(target_item.length) {
            target_item.removeClass('data-checking')
            if(target_item.attr('data-title') != title) {
                target_item.attr('data-title', title)
                target_item.text(title)
                is_change = true;
            }

            if(target_item.attr('data-title-en') != title_en) {
                target_item.attr('data-title-en', title_en)
                is_change = true;
            }

            if(target_item.attr('data-url') != url) {
                target_item.attr('data-url', url)
                is_change = true;
            }

            if(target_item.attr('data-order') != i) {
                target_item.attr('data-order', i)
                is_change = true;
            }
        } else {
            is_change = true;
            $('.profile-footer-lower-menu').append(`<span class="profile-footer-menu-item"
                          data-title="${title}"
                          data-content-title="${title}"
                          data-title-en="${title_en}"
                          data-content-title-en="${title_en}"
                          data-url="${url}"
                          data-content-url="${url}"
                          data-id="${id}"
                          data-order="${i}"
                          data-content-order="${i}">${title}</span>`)
        }
    })

    $('.profile-footer-lower-menu .profile-footer-menu-item.data-checking').each(function(i, e) {
        is_change = true;
        let id = $(e).attr('data-id')
        if(!id.includes('new_') && listMenuSubItemDeleted.indexOf(id) === -1) {
            listMenuSubItemDeleted.push(id)
        }
        $(e).remove()
    })

    if(is_change) {
        $('footer.new').attr('data-edited', true);
        reOrderMenuItem();
    }
    return is_change;
}

function openSubModalFooter(type, is_new, target_id) {
    if(type == 'main' || type == 'sub') {
        if(is_new) {
            $('input#id_menu_title').val('')
            $('input#id_menu_title_en').val('')
            $('input#id_menu_url').val('')
            target_id = 'new_' + type + '_' + (Math.random() + 1).toString(36).substring(7);
        } else {
            let target_item = $('.footer-menu-' + type + ' .item-container[data-id='+ target_id +']');
            $('input#id_menu_title').val(target_item.find('.item-main-column1-menu-title').text())
            $('input#id_menu_title_en').val(target_item.find('.item-main-column1-menu-title-en').text())
            $('input#id_menu_url').val(target_item.find('.item-main-column2-menu-link').text())
        }
        $('#modal_profile_footer_menu').modal('show')
        $('#modal_profile_footer_menu').attr('data-type', type)
        $('#modal_profile_footer_menu').attr('data-id', target_id)
    } else if(type == 'social') {
        if(is_new) {
            $('label[data-social-icon=home]').trigger('click');
            $('input#id_social_url').val('')
            target_id = 'new_' + type + '_' +  (Math.random() + 1).toString(36).substring(7);
        } else {
            let target_item = $('.footer-menu-' + type + ' .item-container[data-id='+ target_id +']');
            $('label[data-social-icon=' + target_item.find('.item-main-column1-menu-icon').attr('data-social-icon') +']').trigger('click');
            $('input#id_social_url').val(target_item.find('.item-main-column2-menu-link').text())
        }
        $('#modal_profile_footer_social').modal('show')
        $('#modal_profile_footer_social').attr('data-type', type)
        $('#modal_profile_footer_social').attr('data-id', target_id)
    }
    validateFooterSubModal();
}

function generateItemMenuFooter(id, title, title_en, url) {
    return `<div class="item-container" data-id="${id}">
        <div class="item-main">
            <div class="item-main-column1">
                <div class="item-main-column1-menu-title">${title}</div>
                <div class="item-main-column1-menu-title-en">${title_en}</div>
            </div>
            <div class="item-main-column2">
                <div class="item-main-column2-menu-link"">${url}</div>
            </div>
        </div>

        <div class="item-button">
            <div class="item-button-icon icon-drag">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19 9H5C4.45 9 4 9.45 4 10C4 10.55 4.45 11 5 11H19C19.55 11 20 10.55 20 10C20 9.45 19.55 9 19 9ZM5 15H19C19.55 15 20 14.55 20 14C20 13.45 19.55 13 19 13H5C4.45 13 4 13.45 4 14C4 14.55 4.45 15 5 15Z" fill="#A7A8A9"/>
                    </svg>
            </div>

            <div class="item-button-icon icon-delete">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6 19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V7H6V19ZM19 4H15.5L14.5 3H9.5L8.5 4H5V6H19V4Z" fill="#A7A8A9"/>
                    </svg>
            </div>
        </div>
    </div>`
}

function generateItemSocialFooter(id, icon, url) {
    return `<div class="item-container" data-id="${id}">
        <div class="item-main">
            <div class="item-main-column1 social-icon">
                <div class="item-main-column1-menu-icon"
                    data-social-icon='${icon}'></div>
            </div>
            <div class="item-main-column2 social-url">
                <div class="item-main-column2-menu-link">${url}</div>
            </div>
        </div>

        <div class="item-button">
            <div class="item-button-icon icon-drag">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19 9H5C4.45 9 4 9.45 4 10C4 10.55 4.45 11 5 11H19C19.55 11 20 10.55 20 10C20 9.45 19.55 9 19 9ZM5 15H19C19.55 15 20 14.55 20 14C20 13.45 19.55 13 19 13H5C4.45 13 4 13.45 4 14C4 14.55 4.45 15 5 15Z" fill="#A7A8A9"/>
                    </svg>
            </div>

            <div class="item-button-icon icon-delete">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6 19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V7H6V19ZM19 4H15.5L14.5 3H9.5L8.5 4H5V6H19V4Z" fill="#A7A8A9"/>
                    </svg>
            </div>
        </div>
    </div>`
}

function generateAddItem(type) {
    let btn_text = 'メニューを追加';
    switch(type) {
        case 'social':
            btn_text = 'アイコンリンクを追加'
            break
        case 'sub':
            btn_text = 'サブメニューを追加'
            break
        default:
            break
    }

    return `<div class="item-container">
        <div class="item-main add-item">
            <div class="add-item-container">
                <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M15.9998 2.66797C8.63984 2.66797 2.6665 8.6413 2.6665 16.0013C2.6665 23.3613 8.63984 29.3346 15.9998 29.3346C23.3598 29.3346 29.3332 23.3613 29.3332 16.0013C29.3332 8.6413 23.3598 2.66797 15.9998 2.66797ZM21.3332 17.3346H17.3332V21.3346C17.3332 22.068 16.7332 22.668 15.9998 22.668C15.2665 22.668 14.6665 22.068 14.6665 21.3346V17.3346H10.6665C9.93317 17.3346 9.33317 16.7346 9.33317 16.0013C9.33317 15.268 9.93317 14.668 10.6665 14.668H14.6665V10.668C14.6665 9.93464 15.2665 9.33463 15.9998 9.33463C16.7332 9.33463 17.3332 9.93464 17.3332 10.668V14.668H21.3332C22.0665 14.668 22.6665 15.268 22.6665 16.0013C22.6665 16.7346 22.0665 17.3346 21.3332 17.3346Z" fill="#F0F0F0"/>
                    </svg>
                <span class="label--8" style="line-height: 175%;">${btn_text}</span>
            </div>
        </div>
    </div>`
}

function updateDataToModalEditFooter(element) {
    if($(element).parents('#modal_profile_footer_menu').length) {
        let type = $('#modal_profile_footer_menu').attr('data-type')
        let title = $('#modal_profile_footer_menu input#id_menu_title').val().trim()
        let title_en = $('#modal_profile_footer_menu input#id_menu_title_en').val().trim()
        let url = $('#modal_profile_footer_menu input#id_menu_url').val().trim()
        let id = $('#modal_profile_footer_menu').attr('data-id')
        let itemDom = $('.item-container[data-id='+id+']');

        if(id.includes('new_') && !itemDom.length) {
            $('#modal_profile_footer .footer-menu-' + type + ' .add-item').parents('.item-container').before(generateItemMenuFooter(id, title, title_en, url))
        } else {
            let current_item = $('#modal_profile_footer .footer-menu-' + type + ' .item-container[data-id=' + id +']')
            current_item.before(generateItemMenuFooter(id, title, title_en, url))
            current_item.remove();
        }

    } else if($(element).parents('#modal_profile_footer_social').length) {
        let type = $('#modal_profile_footer_social').attr('data-type')
        let icon = $('#modal_profile_footer_social input[name=footer-social-link]:checked').val().trim()
        let url = $('#modal_profile_footer_social input#id_social_url').val().trim()
        let id = $('#modal_profile_footer_social').attr('data-id')
        let itemDom = $('.item-container[data-id='+id+']');
        if(id.includes('new_') && !itemDom.length) {
            $('#modal_profile_footer .footer-menu-' + type + ' .add-item').parents('.item-container').before(generateItemSocialFooter(id, icon, url))
        } else {
            let current_item = $('#modal_profile_footer .footer-menu-' + type + ' .item-container[data-id=' + id +']')
            current_item.before(generateItemSocialFooter(id, icon, url))
            current_item.remove();
        }
    }

    if($('.footer-menu-social .item-container').length > 8) {
        $('.footer-menu-social .item-container .item-main.add-item').addClass('btn--disabled');
    } else {
        $('.footer-menu-social .item-container .item-main.add-item').removeClass('btn--disabled');
    }
}

function validateFooterSubModal() {
    let no_title = !$('#modal_profile_footer_menu input#id_menu_title').val().trim().length && !$('#modal_profile_footer_menu input#id_menu_title_en').val().trim().length
    let no_url = !$('#modal_profile_footer_menu input#id_menu_url').val().trim().length
    if(no_title || no_url) {
        $('#modal_profile_footer_menu .btn-submit-profile-footer-menu').addClass('btn--disabled')
    } else {
        $('#modal_profile_footer_menu .btn-submit-profile-footer-menu').removeClass('btn--disabled')
    }

    if(!$('#modal_profile_footer_social input#id_social_url').val().trim().length) {
        $('#modal_profile_footer_social .btn-submit-profile-footer-social').addClass('btn--disabled')
    } else {
        $('#modal_profile_footer_social .btn-submit-profile-footer-social').removeClass('btn--disabled')
    }
}

function reOrderMenuItem() {
    //main
    let main = $('.profile-footer-upper .profile-footer-menu-item')
    for (let i = 0; i < main.length; i++) {
        let item = $('.profile-footer-upper .profile-footer-menu-item[data-order=' + i +']')
        $('.profile-footer-upper').append(`${item[0].outerHTML}`)
        item.remove()
    }
    //social
    let social = $('.profile-footer-upper-right .profile-footer-menu-icon')
    social.each(function(i, e) {
        let item = $('.profile-footer-upper-right .profile-footer-menu-icon[data-order=' + i +']')
        $('.profile-footer-upper-right').append(`${item[0].outerHTML}`)
        item.remove()
    })
    //sub
    let sub = $('.profile-footer-lower-menu .profile-footer-menu-item')
    sub.each(function(i, e) {
        let item = $('.profile-footer-lower-menu .profile-footer-menu-item[data-order=' + i +']')
        $('.profile-footer-lower-menu').append(`${item[0].outerHTML}`)
        item.remove()
    })

    checkNewlineFooterMenu();
}

function initClickFooterButton() {
    $(document).on('click', 'footer.profile-footer .profile-footer-menu-item, footer.profile-footer .profile-footer-menu-icon', function(e) {
        e.stopPropagation();
        let url = $(this).attr('data-url')
        if(url) {
            if(!isValidHttpUrl(url)) {
                url = 'https://' + url
            }
            window.open(url, "_blank");
        }
    })
}

function checkCurrentLan() {
    const urlParams = new URLSearchParams(window.location.search);

    return urlParams.get('lan') == 'en'
}
