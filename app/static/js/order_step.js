var max_length_phone = 30;
var max_length_message = 1000;

function previousPage(x) {
    var current_step, next_step;
    current_step = x.parents('.order-step__form-tab');
    next_step = x.parents('.order-step__form-tab').prev();
    next_step.show();
    next_step.addClass('active');
    current_step.hide();
    current_step.removeClass('active');
}

function nextPage(x) {
    var current_step, next_step;
    current_step = x.parents('.order-step__form-tab');
    next_step = x.parents('.order-step__form-tab').next();
    next_step.show();
    next_step.addClass('active');
    current_step.hide();
    current_step.removeClass('active');
}

function nextPage1() {
    $(document).on('click', '#btn__next_1', function (e) {
        e.preventDefault();
        // $('#modal-detail-topic').modal('hide');
        $('.section-create-project').removeClass('hide');
        $('.section-topic-detail').addClass('hide');
        let current_step, next_step;
        next_step = $('.order-step__form-tab').first();
        // next_step = current_step.next();
        next_step.show();
        next_step.addClass('active');
        // current_step.hide();
        // current_step.removeClass('active');
        updateDataSelection();
    });
}

function updateDataSelection() {
    selections = [];
    $('.section-container').each(function () {
        let selection_id = $(this).attr('data-selection');
        let selectionDom = $(this);
        cleanDataSelection(selectionDom, selection_id)
    });
}

let selections = [];
function cleanDataSelection(formContainer, index) {
    let selectionNewDataFormat = {
        'id': '',
        'title': '',
        'artist_name': '',
        'selector_checkbox': '',
        'toggles': [{
            'detail': '',
            'status': ''
        }],
        'toggles_on': [{
            'detail': '',
            'status': ''
        }],
    };
    let hasSelection = false;

    for (i = 0; i < selections.length; i++) {
        if (selections[i]['id'] === index) {
            selectionNewDataFormat = selections[i];
            hasSelection = true;
        }
    }

    // information
    selectionNewDataFormat['id'] = index;
    selectionNewDataFormat['title'] = formContainer.find('.section-content__title').text();

    let titleSale = [];
    formContainer.find('.section-content__list-media .last-selected').each(function (i,e) {
        let artistDom = $(e).find('.section-content__title-artist');
        if(artistDom.length) {
            let saleName = artistDom.first().find('.item-sale-content-name').text();
            let artistName = artistDom.first().find('.item-artist-name').text();
            titleSale.push(saleName + '/' + artistName);
        }
    });

    selectionNewDataFormat['artist_name'] = titleSale.join(', ');

    let toggles = [];
    let toggles_on = [];

    // toggle
    formContainer.find('.section-content__toggle').each(function () {
        let status = $(this).find('.switch-checkbox').is(':checked') ? 'on' : 'off';
        let detail = $(this).find('.toggle-text-topic').text();
        if (detail === '') {
            return
        }

        let toggle = {
            'detail': detail,
            'status': status
        };
        if (status === 'on') {
            toggles_on.push(toggle);
        }

        toggles.push(toggle)
    });

    selectionNewDataFormat['toggles'] = toggles;
    selectionNewDataFormat['toggles_on'] = toggles_on;

    let selector_checkbox = '';

    // select box
    formContainer.find('.section-content__radio input:checked').each(function () {
        let detail = $(this).parents('.input-radio').find('.detail-radio').text();
        if (detail === '') {
            return
        }
        selector_checkbox = detail
    });

    selectionNewDataFormat['selector_checkbox'] = selector_checkbox;
    if (!hasSelection) {
        selections.push(selectionNewDataFormat);
    }
}

function validateDescription() {
    let messageDom = $('#id_description');
    let isChrome = window.chrome;
    let messageValue = messageDom.val();
    if (isChrome && messageDom.length) {
        messageValue = messageDom.val().replace(/(\r\n|\n|\r)/g, '  ');
    }

    if (messageValue.length > 1000) {
        messageDom.val(messageValue.slice(0, max_length_message))
    }

    if (messageValue === '') {
        $('#btn__next_2').addClass('disable');
    }
}

function nextPage2() {
    let messageDom = $('#id_description');
    if(messageDom.length) {
        let isChrome = window.chrome;
        let messageValue = messageDom.val();
        if (isChrome) {
            messageValue = messageDom.val().replace(/(\r\n|\n|\r)/g, '  ');
        }

        if (messageValue.length > 1000) {
            messageDom.val(messageValue.slice(0, max_length_message))
        }

        if (messageValue === '') {
            $('#btn__next_2').addClass('disable');
        }

        $('#id_description').on('input', function (e) {
            messageValue = messageDom.val().trim();
            if (messageValue !== '') {
                $('#btn__next_2').removeClass('disable');
            } else {
                $('#btn__next_2').addClass('disable');
            }
        });
    }

    // Budget
    $(document).on('input', '#id_budget', function (e) {
        let intValue = this.value.replaceAll(',', '');
        if (intValue.length > this.maxLength) {
            this.value = intValue.slice(0, this.maxLength - 4);
        }
        this.value = intValue.replace(/\D/g, '').replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    });

    $('#btn__previous_2').on('click', function (e) {
        e.preventDefault();
        previousPage($(this));
        // $('#modal-detail-topic').modal('show');
        $('.section-topic-detail').removeClass('hide');
        $('.section-create-project').addClass('hide')
    });

    $('#btn__next_2').on('click', function (e) {
        e.preventDefault();
        validateDescription();
        nextPage($(this));
    });
}

function nextPage3() {
    var $datepicker;

    // Range time
    Date.prototype.addDays = function (days) {
        let date = new Date(this.valueOf());
        date.setDate(date.getDate() + days);
        return date;
    };
    let startDate = new Date();
    let endDate = moment(new Date()).add(1, 'months')
    var defaultDate = [new Date(), moment(new Date()).add(1, 'months').toDate()];
    if (startDate && endDate) {
      defaultDate = [moment(startDate).toDate(), moment(endDate).toDate()];
    }
    flatpickr(".js-daterangepicker", {
      mode: "range",
      dateFormat: "Y/m/d",
      defaultDate: defaultDate,
      showMonths: 1,
      rangeSeparator: "~",
      onOpen: function (selectedDates, dateStr, instance) {
        $(instance.element)
          .next(".c-icon-date-range")
          .addClass("is-icon-active");
      },
      onClose: function (selectedDates, dateStr, instance) {
        $(instance.element)
          .next(".c-icon-date-range")
          .removeClass("is-icon-active");
      },
    //   onChange: function (selectedDates, dateStr, instance) {
    //     let startDate = selectedDates[0];
    //     let endDate = selectedDates[1];
    //     $(instance.element).attr(
    //       "data-start-time",
    //       moment(startDate).format("yyyy-MM-DD HH:mm:ss")
    //     );
    //     if (endDate)
    //       $(instance.element).attr(
    //         "data-end-time",
    //         moment(endDate).format("yyyy-MM-DD HH:mm:ss")
    //       );
    //   },
      // minDate: "today",
      // maxDate: new Date().fp_incr(120),
    });

    // Datepicker
    if ($('.mcalendar').length > 0) {
        $('.mcalendar').each(function () {
            var date = new Date();
            $datepicker = $(this);
            let current_date = $("#id_deadline_1").val();
            if (!$('#id_deadline_1').val()) {
                current_date = formatDate(endDate);
            } else {

               current_date = formatDate($('#id_deadline_1').val().split(':')[0]);
            }
            flatpickr("#id_deadline_1", {
              mode: "single",
              dateFormat: "Y/m/d",
              defaultDate: formatDate(current_date),
              showMonths: 1,
              onOpen: function (selectedDates, dateStr, instance) {
                $(instance.element)
                  .next(".c-icon-date-range")
                  .addClass("is-icon-active");
              },
              onClose: function (selectedDates, dateStr, instance) {
                $(instance.element)
                  .next(".c-icon-date-range")
                  .removeClass("is-icon-active");
              },
              onChange: function (selectedDates, dateStr, instance) {
                let startDate = selectedDates[0];
                let endDate = selectedDates[1];
                $(instance.element).attr(
                  "data-start-time",
                  moment(startDate).format("yyyy-MM-DD HH:mm:ss")
                );
                if (endDate)
                  $(instance.element).attr(
                    "data-end-time",
                    moment(endDate).format("yyyy-MM-DD HH:mm:ss")
                  );
              },
              // minDate: "today",
              // maxDate: new Date().fp_incr(120),
            });

            $('#id_deadline_1').on('change', function () {
                let selected_date = $(this).val();
                if (selected_date != '' & !moment(selected_date, 'YYYY/MM/DD', true).isValid()) {
                    $(this).val(formatDate());
                    $datepicker.datepicker('setDate', formatDate())
                }
            })
        })
    }


    $('#btn__previous_3').on('click', function (e) {
        e.preventDefault();
        previousPage($(this));
    });

    $('#btn__next_3').on('click', function (e) {
        e.preventDefault();
        nextPage($(this));
    });
}

function disableBtnSubmitContact() {
    if ($('#id_fullname').length) {
        var fullnameDom = $('#id_fullname').val().trim();
        var emailDom = $('#id_email').val().trim();
        var emailConfirmDom = $('#id_email_confirm').val().trim();

        if (fullnameDom === '' || emailDom === '' || emailConfirmDom === '') {
            $('#submit-contact').addClass('disable');
        }

        $('#id_fullname, #id_email, #id_email_confirm').on('input', function (e) {
            fullnameDom = $('#id_fullname').val().trim();
            emailDom = $('#id_email').val().trim();
            emailConfirmDom = $('#id_email_confirm').val().trim();

            if (fullnameDom !== '' && emailDom !== '' && emailConfirmDom !== '') {
                $('#submit-contact').removeClass('disable');
            } else {
                $('#submit-contact').addClass('disable');
            }
        });



        $('#submit-contact').on('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            if (validateFormContact()) {
                $('#modalConfirm').modal('show');
            }
            return
        })
    }
}

function checkFormContact() {
    $('#id_phone').on('input', function (e) {
        var phone = $('#id_phone').val().trim();
        if (this.value.length > 30) {
            this.value = this.value.slice(0, 30);
        }
        $('#id_phone').val(phone.replace(common_not_number_regex, ''));
        if (phone === '') {
            $('input[name="contact_channel"]')[0].click()
        }
    });

    $('input[name="contact_channel"]').on('change', function () {
        let phone = $('#id_phone').val().trim();
        if (phone === '') {
            $('input[name="contact_channel"]')[0].click()
        }
    });

    $('#btn__previous_4').on('click', function (e) {
        e.preventDefault();
        previousPage($(this));
    });

    $('#submit-contact').on('click', function (e) {
        e.preventDefault();
        e.stopPropagation();
        if ($('.order-step__form-tab-4').length) {
            if (validateFormContact()) {
                $('#modal-confirm-step').modal('show');
            }
        } else {
            $('#modal-confirm-step').modal('show');
        }

        return
    })
}

function getProgressUploaded(){
    let file_previews = $(".mattach-previews").find(".mattach-template");
    let total = 0;
    let uploaded = 0;
    file_previews.each(function(i, item){
        total += parseInt($(item).attr("data-total"));
        uploaded += parseInt($(item).attr("data-loaded"));
    });
    return Math.max(2, uploaded/total * 70);
}

function confirmStep() {
    $('#submit-confirm').on('click', function (e) {
        $('.errorlist').remove();
        $('.error-border').removeClass('error-border');
        e.preventDefault();
        let form = $('#form-order-step');
        let button_dom = $('#submit-confirm');
        if (!button_dom.hasClass('disable')) {
            if ($('#email').length && !validateFormContact()) {
                return
            }

            // submit form
            button_dom.addClass('disable');

            form.off().on('submit', function (e) {
                e.preventDefault();
                let add_by_sale = $('#form-order-step').attr('data-add-by-sale');
                let hide_step_3 = $('#form-order-step').attr('data-hide-step-3');
 
                // Step 3
                if (hide_step_3 === 'False') {
                    let dates = $("#deadline_date").val().split(" から ");
                    let startDateString = dates[0];
                    let endDateString = dates[1];
                    $("#id_start_time").val(startDateString);
                    $("#id_end_time").val(endDateString);
                    if ($('#id_deadline_1').val()) {
                        $('#id_deadline').val($('#id_deadline_1').val() + ' ' + $('#id_time').val());
                    } else {
                        $('#id_deadline').val('')
                    }
                } else {
                    $('#id_start_time').val('');
                    $('#id_end_time').val('');
                    $('#id_deadline').val('')
                }

                let budgetValue = $('#id_budget').val().replaceAll(',', '').trim();
                budgetValue = budgetValue != '' ?  parseInt(budgetValue) : 0;
                $('#id_budget').val(budgetValue);
                var formData = new FormData(form[0]);
                let data_id = $('#form-order-step').attr('data-order-id');
                if (add_by_sale === 'True' && $('#form-order-step').attr('data-contact-artist-profile') === 'True') {
                    formData.append('artist_id', artist_id);
                    formData.append('contact_artist', 'true');
                    formData.append('list_file_id', list_file);
                    formData.append('list_folder_id', list_folder);
                    formData.append('data_id', data_id)

                } else if (add_by_sale === 'False') {
                    formData.append('selections', JSON.stringify(selections));
                    formData.append('topic_id', $('.section-topic-detail').attr('data-topic'));
                    formData.append('list_file_id', Object.keys(list_file_id_form));
                    formData.append('list_folder_id', Object.values(list_folder_id_form));
                } else {
                    formData.append('artist_id', artist_id);
                    formData.append('sale_id', sale_id);
                    formData.append('sale_name', saleName);
                    formData.append('add_by_sale', 'true');
                    formData.append('data_id', data_id)
                }
                
                $.ajax({
                    type: 'POST',
                    url: '/direct/create_offer',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (data) {
                        if (data.status === 'success') {
                            $('#submit-ok').parents('a').attr('href', data.url);
                            $('#modal-create-success').modal('show');
                            $('.upload-button-wrapper').removeClass('clicked').addClass('success')
                        }
                    },
                    statusCode: {
                        400: function (data) {
                            let str = data.responseText;
                            str = str.replaceAll(')(', ', ').replaceAll(')', '').replaceAll('(', '');
                            let array = str.split(',');
                            var items = [];

                            for (i = 0; i <= array.length - 2; i += 2) {
                                items[array[i]] = array[i + 1]
                            }

                            for (const [key, value] of Object.entries(items)) {
                                console.log(key);
                                let dom = $('#id_' + key.replaceAll(/'/g, '').trim());
                                let new_value = value.replace('[', '').replace(']', '').replaceAll(/'/g, '').trim();
                                $(`<ul class="errorlist"><li>${new_value}</li></ul>`).insertAfter(dom);
                                dom.addClass('error-border')
                            }
                        }
                    },
                    complete: function () {
                        $('#modal-confirm-step').modal('hide');
                        button_dom.removeClass('disable');
                        $myDropZone['sended'] = true;
                        $myDropZone.removeAllFiles();
                        list_file_id_form = {};
                        list_folder_id_form = {};
                        list_file_remove_form = [];
                        list_temp_folder_id_form = {};
                        list_file_name_form = [];
                        list_folder_name_form = [];
                        list_temp_folder_name_form = [];
                        list_name = [];
                        is_exceeded_length_form = false;
                        $('.mattach-template-form.collection-item.item-template').remove();

                        $('.upload-button-wrapper .fill .process').css('width', '100%');
                        setTimeout(function () {
                            $('.upload-button-wrapper').removeClass('success').css('display', 'none');
                            $('.upload-button-wrapper .fill .process').css('width', '0');
                        }, 2000);
                    }
                });
            });

            if ($myDropZone.files.length > 0 && $myDropZone.files[0]) {
                let file_loaded = Object.values(list_file_id_form);
                let file_loading = $myDropZone.files.length - file_loaded.length;

                if (file_loading > 0) {
                    $('.upload-button-wrapper').css('display', 'flex');
                    $('.upload-button-wrapper').addClass('clicked');
                    $('.upload-button-wrapper .fill .process').css('width', '2%');
                    var waiting_file_loading = setInterval(function () {
                        let current_file_loaded = Object.values(list_file_id_form);
                        let current_file_loading = $myDropZone.files.length - current_file_loaded.length;
                        console.log('loading: ' + current_file_loading);
                        let progress = getProgressUploaded();
                        $('.upload-button-wrapper .fill .process').css('width', progress + '%');
                        if (!current_file_loading) {
                            clearInterval(waiting_file_loading);
                            console.log('ok');
                            form.submit()
                        }
                    }, 100);
                } else {
                    form.submit()
                }
            } else {
                form.submit()
            }
        }
        return
    })
}

// Active project type
function activeProjectType() {
    $('.order-step__option-item').on('click', function () {
        if ($(this).hasClass('active')) {
            $('#modal-detail-topic').modal('show');
        } else {
            selections = [];
            $('.order-step__option-item').removeClass('active');
            $(this).addClass('active');
            let topic_id = $(this).attr('data-topic');
            ajaxGetTopicDetail(topic_id)
        }
    });
}

function defaultValueRadio(arrayDefaultValue) {
    let listValue = ['#id_bgm', '#id_se', '#id_voice'];
    for (i = 0; i < listValue.length; i++) {
        let idValue = listValue[i] + '_' + arrayDefaultValue[i];
        $(idValue).trigger('click')
    }
}

function getValueForModalConfirm() {
    $('#modal-confirm-step').on('shown.bs.modal', function () {
        let add_by_sale = $('#form-order-step').attr('data-add-by-sale');
        let hide_step_3 =  $('#form-order-step').attr('data-hide-step-3');
        const url_string = window.location.href;
        var url = new URL(url_string);


        if (add_by_sale === 'False') {
            // step 1
            let projectType = $('.order-step__option-item.active .order-step__option-heading').text();
            $('#id_project_type_value').text(projectType);
            $('#id_project_type_value').text($('.active-topic').find('.list-topics__content-bottom__title span').text());
            for (i = 0; i < selections.length; i++) {
                let selectionHtml = '';
                if (selections[i].selector_checkbox !== '') {
                    selectionHtml = '<div class="bodytext--13">\n' +
                        '                            <label class="input-radio" style="margin-bottom: 0">\n' +
                        '                              <input type="radio" value="" index="" required="true" checked=""><span class="detail-radio">' + selections[i].selector_checkbox + '</span>\n' +
                        '                              <div class="check-mark"></div>\n' +
                        '                            </label></div>';
                }
                $(`<div class="popup-body__item p-0">
                          <div class="heading--13">${selections[i].title}</div>
                          <div class="bodytext--13">${selections[i].artist_name}</div>
                          ${selectionHtml}
                          </div>`).appendTo($('.popup-body-selection'));

                for (j = 0; j < selections[i]['toggles'].length; j++) {
                    let checkStatus = selections[i]['toggles'][j]['status'] == 'on' ? 'checked' : '';
                    $(`<div class="form-check custom-switch disabledbutton">
                <label class="form-check-label">
                  <div class="form-check-group">
                    <input class="form-check-input switch-checkbox" type="checkbox" ${checkStatus}><span class="switch-slider"></span>
                  </div>
                  <span class="switch-label" style="color: #000000">${selections[i]['toggles'][j].detail}
                  </span>
                </label>
              </div>`).appendTo($('.popup-body-selection'));
                }
            }

            // Step 2
            $('#id_description_value').text($('#id_description').val());
            $('#id_budget_value').text($('#id_budget').val());
            $('.mattach-template.mattach-template-form').each(function () {
                let file_name = $(this).find('.mcommment-file__name').text();
                let classFile = $(this).find('.mcommment-file__name .icon').hasClass('icon--sicon-clip') ? 'icon--sicon-clip' : 'icon--sicon-storage';

                $(`<div class="order-step__file">
               <i class="icon ${classFile}"></i>
                  <div class="order-step__file-name">${file_name}</div>
               </div>`).appendTo('.order-step__upload-file')

            });
        } else if ($('#form-order-step').attr('data-contact-artist-profile') === 'True') {
            $('#id_description_value').text(message);
            $('#id_album_name').text(artist_name);
            $.each(list_name_file, function( index, value ) {
                let classFile = 'icon--sicon-clip';
                $(`<div class="order-step__file">
               <i class="icon ${classFile}"></i>
                  <div class="order-step__file-name">${value}</div>
               </div>`).appendTo('.order-step__upload-file')
            });
            $.each(list_name_folder, function( index, value ) {
                let classFile = 'icon--sicon-storage';
                $(`<div class="order-step__file">
               <i class="icon ${classFile}"></i>
                  <div class="order-step__file-name">${value}</div>
               </div>`).appendTo('.order-step__upload-file')
            });
        } else {
            $('#id_description_value').text(message);
            $('#id_budget_value').text($('#id_budget').val());
            $('#id_album_name').text(saleName);
        }

        // Step 3
        if (add_by_sale === 'False' || hide_step_3 === 'False') {
            $('#id_schedule_time_value').text($('#deadline_date').val());
            if ($('#id_deadline_1').val()) {
                $('#id_deadline_value').text($('#id_deadline_1').val() + ' ' + $('#id_time').val());
            }
            getValueRadio();
        }

        // Step 4
        getValueContract();

    });

    $('#modal-confirm-step').on('hidden.bs.modal', function () {
        $('#modal-confirm-step .order-step__file').remove();
        $('.popup-body-selection').empty();
    });
}

function getValueRadio() {
    let listValue = ['contract_type', 'ownership_type', 'disclosure_rule'];
    listValue.map((x) => {
        let current_value = $('input[name='+x+']:checked').attr('data-value');
        let idValue = '#id_' + x + '_value';
        $(idValue).text(current_value)
    });
}

function getValueContract() {
    let listValue = ['#id_email', '#id_fullname', '#id_job_type', '#id_enterprise', '#id_company_url', '#id_phone'];
    listValue.map((x) => {
        let current_value = $(x).val();
        let idValue = x + '_value';
        $(idValue).text(current_value)
    });
    let current_value = $('input[name="contact_channel"]:checked').attr('data-value');
    $('#id_contact_channel_value').text(current_value)
}

var artist_id;
var sale_id;
var message;
var budget;
var saleName;
var artist_name;
var list_file;
var list_folder;
var list_name_file = [];
var list_name_folter = [];
function updateDataWhenOfferArtist() {
    let formDom = $('#form-order-step');
    artist_id = formDom.attr('data-artist-id');
    sale_id = formDom.attr('data-sale-id');
    message = formDom.attr('data-message');
    budget = formDom.attr('data-budget');
    saleName = formDom.attr('data-sale-name');
    if (artist_id && message && sale_id) {
        message = message.replace(/(\\n)/g, '\n');
        $('#id_description').val(message);
        let budgetValue = budget ? budget.split('.')[0] : 0;
        $('#id_budget').val(budgetValue);
        $('#id_budget').trigger('input')
    } else if (artist_id && message && $('#form-order-step').attr('data-contact-artist-profile') === 'True') {
        message = message.replace(/(\\n)/g, '\n');
        $('#id_description').val(message);
        artist_name = $('#form-order-step').attr('data-artist-name');
        list_name_file = $('#form-order-step').attr('data-name-file');
        if (list_name_file) {
            list_name_file = list_name_file.split(',');
        }
        list_name_folder = $('#form-order-step').attr('data-name-folder');
        if (list_name_folder) {
            list_name_folder = list_name_folder.split(',');
        }
        list_file = url.searchParams.get('file');
        if (list_file) {

        }
        list_folder = url.searchParams.get('folder');
        if (list_folder) {

        }

    } else {
        artist_id = '';
        sale_id = '';
        message = '';
        $('#form-order-step').attr('data-add-by-sale', 'False');
        $('#form-order-step').attr('data-contact-artist-profile', 'False');
    }
}

function nextStepWithTopic() {
    const url_string = window.location.href;
    var url = new URL(url_string);
    let topic_id = url.searchParams.get('topic_id');
    if (topic_id) {
        let selected_album_index = url.searchParams.get('selected');
        if (selected_album_index) {
            setSelectedAlbum(topic_id, parseInt(selected_album_index));
        }
        let topicDom = $('.order-step__form-tab .list-topics__topics-container[data-topic=' + topic_id + ']').find('.button-show-detail-topic');
        if (topicDom.length) {
            topicDom.trigger('click');
        }
    }
}

function setSelectedAlbum(topic_id, index) {
    let topic = $('.list-topics__topics-container[data-topic=' + topic_id + ']');
    if (topic.length) {
        let albums = topic.find('.list-topics__content-bottom__list-circle .list-circle__component');
        if (albums.length > index + 1) {
            albums.eq(index).addClass('last-selected');
        }
    }
}

$(document).ready(function () {
    nextPage1();
    nextPage2();
    nextPage3();
    disableBtnSubmitContact();
    checkFormContact();
    confirmStep();
    activeProjectType();
    getValueForModalConfirm();
    // showDetailTopic();
    // nextStepWithTopic();
    showAlbumPreview();
    updateDataWhenOfferArtist();
});
