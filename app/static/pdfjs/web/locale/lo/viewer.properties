# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=àº«àºà»àº²àºà»àº­àºàº«àºà»àº²
previous_label=àºà»àº­àºàº«àºà»àº²
next.title=àº«àºà»àº²àºàº±àºà»àº
next_label=àºàº±àºà»àº

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=àº«àºà»àº²
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=àºàº²àº {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} àºàº²àº {{pagesCount}})

zoom_out.title=àºàº°àº«àºàº²àºàº­àº­àº
zoom_out_label=àºàº°àº«àºàº²àºàº­àº­àº
zoom_in.title=àºàº°àº«àºàº²àºà»àºàº»à»àº²
zoom_in_label=àºàº°àº«àºàº²àºà»àºàº»à»àº²
zoom.title=àºàº°àº«àºàº²àº
presentation_mode.title=àºªàº±àºàºà»àº½àºà»àºàº±àºà»àº«àº¡àºàºàº²àºàºàº³àºªàº°à»àº«àºàºµ
presentation_mode_label=à»àº«àº¡àºàºàº²àºàºàº³àºªàº°à»àº«àºàºµ
open_file.title=à»àºàºµàºà»àºàº¥à»
open_file_label=à»àºàºµàº
print.title=àºàº´àº¡
print_label=àºàº´àº¡
download.title=àºàº²àº§à»àº«àº¥àº
download_label=àºàº²àº§à»àº«àº¥àº
bookmark.title=àº¡àº¸àº¡àº¡àº­àºàºàº°àºàº¸àºàº±àº (àºªàº³à»àºàº»àº² àº«àº¥àº· à»àºàºµàºà»àºàº§àº´àºà»àºà»àº«àº¡à»)
bookmark_label=àº¡àº¸àº¡àº¡àº­àºàºàº°àºàº¸àºàº±àº

# Secondary toolbar and context menu
tools.title=à»àºàº·à»àº­àºàº¡àº·
tools_label=à»àºàº·à»àº­àºàº¡àº·
first_page.title=à»àºàºàºµà»àº«àºà»àº²àºàº³àº­àº´àº
first_page_label=à»àºàºàºµà»àº«àºà»àº²àºàº³àº­àº´àº
last_page.title=à»àºàºàºµà»àº«àºà»àº²àºªàº¸àºàºà»àº²àº
last_page_label=à»àºàºàºµà»àº«àºà»àº²àºªàº¸àºàºà»àº²àº
page_rotate_cw.title=àº«àº¡àº¹àºàºàº²àº¡à»àºàº±àº¡à»àº¡àº
page_rotate_cw_label=àº«àº¡àº¹àºàºàº²àº¡à»àºàº±àº¡à»àº¡àº
page_rotate_ccw.title=àº«àº¡àº¹àºàºàº§àºà»àºàº±àº¡à»àº¡àº
page_rotate_ccw_label=àº«àº¡àº¹àºàºàº§àºà»àºàº±àº¡à»àº¡àº




# Document properties dialog box
document_properties_file_name=àºàº·à»à»àºàº¥à»:
document_properties_file_size=àºàº°àº«àºàº²àºà»àºàº¥à»:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_page_size_unit_inches=in
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=àº¥àº§àºàºàº±à»àº
document_properties_page_size_orientation_landscape=àº¥àº§àºàºàº­àº
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=àºàº»àºà»àº²àº
document_properties_page_size_name_legal=àºà»à»àºàº»àºàº«àº¡àº²àº
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_close=àºàº´àº

# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_close=àºàº»àºà»àº¥àºµàº

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=à»àºàºµàº/àºàº´àºà»àºàºàºà»àº²àº
toggle_sidebar_label=à»àºàºµàº/àºàº´àºà»àºàºàºà»àº²àº
document_outline_label=à»àºàº»à»àº²àº®à»àº²àºà»àº­àºàº°àºªàº²àº
findbar_label=àºàº»à»àºàº«àº²

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.

# Find panel button title and messages
find_input.title=àºàº»à»àºàº«àº²
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.

# Error panel labels
error_more_info=àºà»à»àº¡àº¹àºà»àºàºµà»àº¡à»àºàºµàº¡
error_less_info=àºà»à»àº¡àº¹àºàºà»àº­àºàº¥àº»àº
error_close=àºàº´àº
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
rendering_error=àº¡àºµàºà»à»àºàº´àºàºàº²àºà»àºàºµàºàºàº·à»àºàºàº°àºàº°àºàºµà»àºàº³àº¥àº±àºà»àº£àº±àºà»àºàºµàº«àºà»àº².

# Predefined zoom values
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.

loading_error=àº¡àºµàºà»à»àºàº´àºàºàº²àºà»àºàºµàºàºàº·à»àºàºàº°àºàº°àºàºµà»àºàº³àº¥àº±àºà»àº«àº¥àº PDF.
invalid_file_error=à»àºàº¥à» PDF àºà»à»àºàº·àºàºà»àº­àºàº«àº¥àº·à»àºªàºàº«àº²àº.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
password_ok=àºàº»àºàº¥àº»àº
password_cancel=àºàº»àºà»àº¥àºµàº

