/* 01EG89H6SS2141VNGDDEHBMV4Q */
.mg-top-lg {
    margin-top: 64px;
}

.mg-top {
    margin-top: 32px;
}

.mg-top-sm {
    margin-top: 16px;
}

.mg-top-xs {
    margin-top: 8px;
}

.mg-bottom-lg {
    margin-bottom: 64px;
}

.mg-bottom {
    margin-bottom: 32px;
}

.mg-bottom-sm {
    margin-bottom: 16px;
}

.mg-bottom-xs {
    margin-bottom: 8px;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-center {
    text-align: center;
}

.btn-general {
    background: #a7a8a9;
    cursor: pointer;
    transition: 0.1s;
}

.btn-general:hover {
    background: #009ace;
}

.btn-general-font {
    color: #a7a8a9;
    cursor: pointer;
    transition: 0.1s;
}

.btn-general-font:hover {
    color: #009ace;
}

.overflow-x-scroll {
    overflow-x: scroll;
}

.nice-scroll::-webkit-scrollbar {
    background-color: #fff;
    width: 12px;
    height: 12px;
    border-radius: 8px;
    padding: 2px;
}

/* background of the scrollbar except button or resizer */
.nice-scroll::-webkit-scrollbar-track {
    background-color: transparent;
}

/* scrollbar itself */
.nice-scroll::-webkit-scrollbar-thumb {
    background-color: #f0f0f0;
    border-radius: 16px;
    border: 3px solid #fff;
}

/* set button(top and bottom of the scrollbar) */
.nice-scroll::-webkit-scrollbar-button {
    display: none;
}

.flex_row {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.flex_column {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.align-left {
    align-items: start;
}

.align-right {
    align-items: end;
}

.custom-switch.disabled {
    pointer-events: none;
}

.custom-switch.disabled .switch-label {
    color: #a7a8a9 !important;
}

.custom-switch.disabled .form-check-group .switch-slider:before,
.custom-switch.disabled .form-check-group input:checked + .switch-slider:before,
.custom-switch.disabled .form-check-group input:checked + .switch-slider {
    background-color: #f0f0f0;
}

.system-toast {
    border: 1px solid #009ace;
    border-radius: 6px !important;
    background-color: #fcfcfc;
    padding: 0 !important;
    min-width: 50px;
    box-shadow: 2px 5px 8px rgba(0, 154, 206, 0.05) !important;
    opacity: 1 !important;
}

#toast-container .system-toast.toast-info {
    padding-left: 0 !important;
    max-width: fit-content;
    white-space: nowrap;
}

.system-toast-message {
    color: #009ace;
    font-weight: 300;
    font-size: 11px;
    line-height: 200%;
    padding: 1px 16px;
}

.system-toast-title {
    display: hide;
}

#toast-container.toast-top-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

#loading_animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: #fff;
    z-index: 9999;
    /* background-image: url('/static/images/icon-loading-b.svg'); */
    background-size: 32px;
    background-position: center;
    background-repeat: no-repeat;
}

#loading_animation svg {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
    display: block;
    height: 64px;
    width: 64x;
}
