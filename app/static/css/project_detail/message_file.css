.pd-section-file {
    width: 267px;
    border-left: 1px solid #f0f0f0;
    padding: 16px 0 0 0;
    transition: all .3s;
    overflow: hidden;
}

.file-hide {
    display: block;
}

.mCSB_inside > .mCSB_container {
    margin-right: 0;
}

@media (max-width: 992px) {
    .pd-section-file {
        width: 100%;
        transition: all .3s;
    }

    .pd-file {
        display: block;
        padding-top: 0;
        transition: all .3s;
        overflow: hidden;
    }

    .pd-file-heading {
        margin-top: 0;
        padding: 16px 0;
        border-top: 1px solid #f0f0f0;
        /* padding: 0 0 16px; */
        border-bottom: 1px solid #f0f0f0;
    }

    .pd-file.active {
        transition: all .3s;
    }

    .pd-section-file.pd-section-file-active {
        width: 100%;
        height: 100%;
        display: block;
    }
    .pd-section-file .pd-file-content {
        display: block;
    }

    .file-hide {
        display: none;
    }
}

.pd-section-file.active {
    width: 55px;
    transition: all .3s;
}

.pd-section-file .pd-file-content {
    max-height: 80vh;
    transform: translateX(0);
    visibility: visible;
    opacity: 1;
    transition: all .3s;
    overflow-x: hidden;
}

.pd-section-file .pd-file-content.active {
    transform: translateX(100%);
    visibility: hidden;
    opacity: 0;
    transition: all .3s;
}

.pd-file-toggle {
    margin-left: auto;
    font-size: 16px;
    display: inline-flex;
}

.pd-file-toggle.active {
    transform: rotate(180deg);
    transition: all .3s;
}

@media (max-width: 992px) {
    .pd-file-toggle.active {
        transform: rotate(90deg);
        transition: all .3s;
    }
}

@media (max-width: 992px) {
    .pd-file-toggle {
        color: #000;
        transform: rotate(270deg);
        transition: all .3s;
    }
}

.pd-file-heading {
    display: flex;
    align-items: center;
    margin-top: 8px;
    margin-bottom: 24px;
    color: #a7a8a9;
}

.pd-file-hide-heading {
    display: flex;
    align-items: center;
    margin-top: -3px;
    color: #a7a8a9;
}

@media (max-width: 992px) {
    .pd-file-heading {
        margin-top: 0;
        /* padding: 0 0 16px; */
        border-bottom: 1px solid #f0f0f0;
    }
}

.pd-file-heading > .icon, .pd-file-hide-heading > .icon {
    /* stylelint-disable-line */
    font-size: 24px;
    margin-right: 8px;
}

.pd-file-heading:hover {
    cursor: pointer;
    color: #009ace;
}

.pd-file-heading.active {
    font-size: 0;
}

.pd-file-heading.active > .icon {
    margin-right: 0;
}

.pd-file-heading.active .pd-file-toggle {
    display: none;
}

.sview-user {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 2px 4px 0 0;
}

.sview-user .notification {
    margin-left: 2px;
    font-size: 8px;
    color: #53565a;
    border: 1px solid #a7a8a9;
    height: 14px;
    line-height: 12px;
}

.sview-user-seen {
    display: flex;
}

.sview-user-seen:not(:first-child) {
    margin-left: -5px;
}

.sview-user-seen .avatar {
    border: 1px solid #fff;
}

.tfile-item {
    margin-bottom: 0px;
}

.tfile-item:last-child {
    margin-bottom: 0;
}

.tfile-item-time {
    margin-bottom: 0px;
    color: #a7a8a9;
    font-size: 8px;

    padding: 16px 0 0 8px;
    border-bottom: 1px solid #f0f0f0;
}

.tfile-list {
    margin-bottom: 8px;
}

.tfile-info {
    text-align: right;
    position: absolute;
    right: 0;
    top: 4px;
}

.acr-result-icon {
    text-align: right;
    position: absolute;
    right: 0;
    top: 16px;
    transform: translateY(-50%);
    height: auto;
    padding: 0;
    display: flex;
    align-items: flex-start;
    margin-right: 24px;
    /* z-index: 9999; */
}

.acr-result-icon.deactive svg path {
    fill: #f0f0f0;
}

.acr-result-icon.active svg path {
    fill: #009ace;
}

.acr-result-icon.active:hover svg path, .tfile-item-content .active .acr-result-icon.active:hover svg path{
    fill: #0076A5;
}

.tfile-item-content .active .acr-result-icon.active svg path {
    fill: #FFFFFF;
}

.tfile-item-content .active .acr-result-icon.deactive svg path {
    fill: #FFFFFF;
    opacity: 0.1;
}

.acr-result-icon.deactive {
    cursor: default;
}

.mfolder__sub .acr-result-icon {
    text-align: right;
    position: absolute;
    right: 0;
    top: 12px;
    height: auto;
    padding: 0;
    display: flex;
    align-items: flex-start;
    margin-right: 32px;
    z-index: 9999;
}

.tfile-info .icon {
    /* stylelint-disable-line */
    color: #a7a8a9;
    margin-right: 0;
    font-size: 16px;
    padding-top: 0px;
    transition: 0.2s;
}

.tfile-info .icon:hover {
    color:#009ace;
    cursor: pointer;
    scale:1.2;
}

.tfile-type {
    position: relative;
    margin-bottom: 8px;
}

.tfile-type:last-child {
    margin-bottom: 0;
}

.tfile-type--audio .messenger-content {
    display: flex;
}

.tfile-type--audio .s-audio {
    width: 100%;
}

.tfile-type--audio .s-audio-control {
    margin-right: 8px;
}

.tfile-type--video .messenger-content {
    display: flex;
}

.tfile-type--video .s-audio {
    width: 100%;
}

.tfile-type--video .s-audio-control {
    margin-right: 8px;
}

/* .tfile-type.has-user-seen .s-file {
    padding: 4px 0px 0px 0px;
} */

.tfile-type .s-audio {
    /*padding: 6px 65px 24px 6px;*/
    line-height: 20px;
    flex-wrap: nowrap;
}

.tfile-type .s-video {
    /*padding: 6px 65px 24px 6px;*/
    line-height: 20px;
    flex-wrap: nowrap;
}

.tfile-infor.active .s-file, .tfile-infor.active .s-audio {
    background-color: #009ace;
    color: #fff;
}

.tfile-infor.active .loading.icon--sicon-download:before {
  content: "__";
  background-image: url(../../images/icon-loading-outline-w.svg);
  background-size: contain;
  background-repeat: no-repeat;
  color: transparent;
  cursor: default;
}

.tfile-infor.active .done.icon--sicon-download:before {
  content: "__";
  background-image: url(../../images/icon_done_w.svg);
  background-size: contain;
  background-repeat: no-repeat;
  color: transparent;
  cursor: default;
}

.tfile-infor.active .tfile-info .icon {
    /* stylelint-disable-line */
    color: #fff;
}

.tfile-infor.active .notification {
    border: 1px solid #fff;
    color: #fff;
}

.tfile-file {
    position: relative;
    background: #fff;
}

.tfile-file .s-audio-text {
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    width: 150px;
    text-overflow: ellipsis;
    color: #000000;
}

/*.tfile-file .icon {*/
/*    !* stylelint-disable-line *!*/
/*    display: none;*/
/*}*/

.tfile-file .sview-user {
    /* position: absolute; */
    /* right: 0px; */
    /* bottom: 2px; */
    padding-bottom:2px;
}

.sfolder {
    border-radius: 6px;
    border: 1px solid #F0F0F0;
    background-color: #FCFCFC;
}

.sfolder-gray,
.sfolder-gray .hasSub {
    background-color: #f0f0f0;
    color: #000000;
}

.sfolder .mfolder {
    padding: 12px 0 4px 16px;
    margin: 0;
    list-style: none;
    font-size: 12px;
}

.sfolder .mfolder ul {
    padding-left: 25px;
    margin: 0;
    list-style: none;
}

.sfolder .mfolder li {
    /* stylelint-disable-line */
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-bottom: 12px;
}

.sfolder .mfolder li:first-child {
    margin-top: 12px;
}

.sfolder .mfolder li:last-child {
    margin-bottom: 0;
}

.sfolder .mfolder .sview-user {
    top: 24px;
    left: 0px;
}

.sfolder .mfolder .sview-user .avatar-image {
    width: 8px
}

.sfolder .mfolder .icon {
    /* stylelint-disable-line */
    font-size: 16px;
    margin-right: 8px;
    position: relative;
    line-height: 24px;
    color: #a7a8a9;
}

.sfolder .mfolder > li {
    /* stylelint-disable-line */
}

.sfolder .mfolder > li:first-child {
    margin-top: 0;
}

.sfolder .mfolder > li > .icon {
    /* stylelint-disable-line */
    font-size: 16px;
    margin-right: 8px;
    position: relative;
    bottom: -1px;
}

.mfolder__sub {
    overflow: hidden;
    position: relative;
    display: flex;
    flex-wrap: nowrap;

}

.mfolder__sub span,
.parent-folder .hasSub,
.list-group-item .hasSub,
.list-group-item .sfile-item{
    display: inline-block;
    margin-right: 5px;
    overflow: hidden;
    padding-right: 30px;
    white-space: nowrap;
    cursor: default;
    text-overflow: ellipsis;
    /*color: #000;*/
    font-size: 13px;
}

.list-group-item .hasSub,
.list-group-item .sfile-item {
    max-width: 72%;
}

.sfile-item .icon {
    margin-right: 8px;
}

.mfolder__sub span,
.parent-folder .hasSub {
    width: calc(100% - 53px);
}

.parent-folder .hasSub {
    margin-right: 2px;
}

.mfolder__sub .icon {
    color: #a7a8a9;
    font-size: 14px !important;
}

.mfolder__sub .fa.icon {
    padding: 2px 0;
}

.parent-folder {
    display: flex;
    margin-right: -10px;
    align-items: center;
    max-width: 100%;
}

/*.parent-folder .hasSub{*/
/*    width: 72%;*/
/*    overflow: hidden;*/
/*    display:inline-block;*/
/*}*/

.parent-folder .icon--sicon-download{
    color: #a7a8a9;
    font-size: 14px !important;
}
