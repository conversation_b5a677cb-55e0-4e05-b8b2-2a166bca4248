@charset "UTF-8";
/* CSS Document */

/* ---------
    Topics
--------- */


.topics {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.topics-items {
    width: 50%;
    margin: 120px auto 0px;
    padding: 0px 15px;
    position: relative;
}

.topic-tag {
    color:#ffffff;
    background-color: #009ace;
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-size: 0.8125rem;
    padding: 5px 15px;
}

.topics-day {
    color:#53565A;
}


@media (max-width:560px) {

/* topics */
.topics {
    flex-direction: column;
    flex-wrap: wrap;
  }

.topics-items {
    width: 100%;
    margin: 60px auto 0px;
  }

}
