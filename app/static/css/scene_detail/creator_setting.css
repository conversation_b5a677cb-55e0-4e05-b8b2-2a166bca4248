:root{
    --black1-color: #000000;
    --black2-color: #53565A;
    --grey1-color: #A7A8A9;
    --grey2-color: #D3D3D3;
    --white-color: #FFFFFF;
    --grey3-color: #F0F0F0;
    --blue-color: #009ACE;
    --background-color: #FCFCFC;
}

* {
    scroll-margin-top: 65px;
  }

.has-success .form-control {
    border-color: #ccc !important;
}

.has-success .control-label {
    color: #333 !important;
}

.has-success .help-block {
    color: #737373 !important;
}

.user-info__notifi ul {
    display: inline-flex;
    list-style: none;
    padding-left: 0px;
}

/* Content tabs */
.user-info__name-file {
    display: block;
    font-size: 13px;
    font-weight: 300;
    line-height: 20px;
    color: var(--black1-color);
    margin-bottom: 15px;
}

.user-info__cancel-img {
    margin-left: 24px;
}

.user-info__cancel {
    font-size: 11px;
    line-height: 17px;
    font-weight: 300;
    background: var(--white-color);
    color: var(--black2-color);
    border: 1px solid var(--black2-color);
    border-radius: 4px;
    height: 25px;
    padding: 4px 8px;
}

.user-info {
    position: relative;
    margin-top: 65px;
    background-color: var(--background-color);
}

.h3-title {
    font-size: 1.1em;
    color: #1a1a1a;
}

.user-info__upload label {
    color: white;
}

.user-info__form label {
    margin-left: 0;
}

.user-info__upload {
    margin-top: 10px;
}

.user-info__wrap {
    float: left;
    position: fixed;
    top: 165px;
    background-color: var(--background-color);
    z-index: 2;
    width: 200px;
}

.user-info__heading {
}

.user-info__heading h3 {
    font-size: 24px;
    font-weight: 400;
    line-height: 36px;
    padding: 40px 0 4px;
    margin: 0;
    color: var(--black1-color);
}

.user-info__heading .account__field-text {
    margin-top: 0;
    margin-bottom: 16px;
}

.user-info__content {
    border: 1px solid var(--soremo-border);
    border-radius: 12px;
    padding: 16px 17px;
    background-color: var(--white-color);
}

.account__form-group-wrap {
}


.account__form-group-wrap .account__tradeoff {
    width: 40%;
    padding-top: 5px;
    margin: 16px 0 0;
}

.account__form-group-wrap .account__trade-item.active:first-child:after,
.account__form-group-wrap .account__trade-item:first-child:after,
.account__form-group-wrap .account__trade-item.active:last-child:after,
.account__form-group-wrap .account__trade-item:last-child:after {
    left: calc(50% - 1.5px) !important;
}

.account__form-group-wrap .account__trade-slider {
    display: flex;
    margin: 0 -10%;
}
.account__form-group-wrap .account__trade-item {
    width: 20%;
    position: relative;
}

.account__form-group-wrap .account__trade-item:hover {
    cursor: pointer;
}

.account__form-group-wrap .account__trade-item:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: var(--soremo-border);
    top: 50%;
    left: 50%;
    transform: translateY(-50%);
}

.account__form-group-wrap .account__trade-item:after {
    content: "";
    position: absolute;
    left: 50%;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 1px solid var(--soremo-placeholder);
    background-color: var(--white-color);
    transform: translate(-50%, -50%);
}

.account__form-group-wrap .account__trade-item:first-child:after {
    left: calc(50% - 7px);
}

.account__form-group-wrap .account__trade-item:last-child:after {
    left: calc(50% + 7px);
}

.account__form-group-wrap .account__trade-item:last-child:before {
    display: none;
}

.account__form-group-wrap .account__trade-item.active:after {
    width: 16px;
    height: 16px;
    background-image: url(../images/radio_button.png);
    background-repeat: no-repeat;
    background-size: 16px;
    background-position: center;
    background-color: var(--white-color);
    border: 1px solid var(--blue-color);
}

.account__form-group-wrap .account__trade-item.active:first-child:after {
    left: calc(50% - 1.5px);
}
.account__form-group-wrap .account__trade-item.active:last-child:after {
    left: calc(50% + 1.5px);
}

.account__trade-item .input-radio {
    position: absolute;
    top: -10px;
    left: 33px;
    z-index: 2;
}

.account__form-group-wrap .account__trade-label {
    display: flex;
    justify-content: space-between;
    padding-top: 15px;
    text-transform: uppercase;
    font-size: 11px;
    line-height: 17px;
    color: var(--black1-color);
}
.account__form-group-wrap .account__trade-description {
    margin-bottom: 35px;
}

.user-info__content .user-info__form {
    max-width: 100%;
}

.account__form-group.form-group {
    margin-bottom: 0;
}

.account__form-group .form-group {
    margin: 0;
    padding: 0;
}

.account__form-group .form-group:last-child {
    margin-bottom: 40px;
}

.account__form-group .form-group label {
    margin: 0;
    padding: 0;
}

.account__field-label {
    display: block;
    font-size: 16px;
    font-weight: 400;
    line-height: 200%;
    color: var(--black1-color);
    margin-bottom: 8px;
    margin-top: 16px;
}

.account__field-hint {
    display: block;
    font-size: 11px;
    font-weight: 300;
    line-height: 17px;
    color: var(--grey1-color);
    margin-bottom: 8px
}

.form-group .form-control.account__input-text-blue {
    color: var(--blue-color);
}

input:required {
    border: 1px solid #2CC84D;
    border-radius: 4px;
  }

.account__form-heading {
    font-size: 18px;
    font-weight: 400;
    line-height: 27px;
    margin: 0;
    padding: 40px 0 4px;
    color: var(--black1-color);
    border-top: 1px solid var(--soremo-border);
}

.account__field-description {
    font-size: 11px;
    font-weight: 300;
    line-height: 17px;
    color: var(--black1-color);
    margin: 4px 0 0;
}

.acc_action {
    text-align: left;
}

.acc_action .form-group {
    margin-bottom: 0;
}

.acc_action #btn__ok {
    width: auto;
    border-radius: 4px;
}

.form-textarea textarea {
    width: 100%;
}

.account__field-text {
    font-size: 11px;
    line-height: 17px;
    color: var(--black1-color);
    margin-top: 8px;
    margin-bottom: 0;
}

.account__field-date {
    font-size: 11px;
    line-height: 17px;
    color: var(--grey1-color);
    margin-top: 8px;
    margin-bottom: 0;
}

.input-group {
    position: relative;
    display: table;
    border-collapse: separate;
}

.input-group-addon {
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
    color: var(--grey1-color);
    text-align: center;
    background-color: var(--soremo-border);
    border-radius: 4px 0px 0px 4px;
    padding: 12px 2px 12px 16px;
    border: none;
    height: auto;
}
.input-group-addon, .input-group-btn {
    width: 1%;
    white-space: nowrap;
    vertical-align: middle;
}

.form-group .form-control.input-group-slug {
    font-size: 13px;
    line-height: 20px;
    height: auto;
    color: var(--black1-color);
    padding: 12px 16px 12px 4px;
    border-radius: 0px 4px 4px 0px ;
    border: 1px solid var(--soremo-border);
}

.account__form-group-link {
    display: flex;
    flex-wrap: wrap;
}

.account__form-group-link .input-group {
    margin-top: 9px;
    margin-right: 24px;
}

#qrcode {
    display: block;
    width: 200px;
    margin: 16px 0;
}

.account__copy-link {
    margin-top: 9px;
}

.account__copy-link img {
    height: 16px;
    width: auto;
    margin-right: 8px;
}

.account__copy-link.disable img {
    filter: contrast(0.5);
}

.account__form-group .account__form-multi {
    display: block;
}

.account__form-group .account__form-multi:last-child {
    margin-bottom: 0;
}

.account__form-group .account__form-multi.account__form-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 0;
}

.account__form-group .account__form-multi .input-radio {
    display: block;
    position: relative;
    padding-left: 27px;
    font-weight: 400;
    line-height: 20px;
    color: var(--black1-color);
    font-size: 13px;
    margin: 12px 0;
}

.account__form-group .input-radio input:checked ~ .check-mark {
    border: 1px solid var(--blue-color);
}

.account__form-group .input-radio .check-mark {
    width: 16px;
    height: 16px;
    border: 1px solid var(--grey1-color);
    top: 2px
}

.account__form-group .input-radio .check-mark:after {
    top: 2px;
    left: 2px;
    width: 10px;
    height: 10px;
}

.notification-time {
    position: relative;
    display: flex;
    align-items: center;
}

.notification-time .input-time {
    border: 1px solid var(--soremo-border);
    box-sizing: border-box;
    border-radius: 4px;
    height: 45px;
    margin-left: 42px;
    padding: 10px 40px 10px 16px;
    color: var(--black2-color);
}

.notification-time:after {
    content: '\e925';
    font-family: 'soremoicons';
    pointer-events: none;
    position: absolute;
    bottom: 12px;
    right: 16px;
    font-size: 16px;
    color: var(--grey1-color);
}

.notification-time select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    cursor: pointer;
}

.notification-time .input-time:focus-visible {
    outline: none;
}

.notification-time .input-arrow {
    display: flex;
    flex-direction: column;
}

.notification-time .input-button {
    background: none;
    border: none;
    color: #a7a8a9;
    padding: 0;
    font-size: 8px;
}

.account__status {
    font-size: 8px;
    line-height: 12px;
    padding: 2px 8px;
    margin-left: 8px;
    border-radius: 4px;
}

.account__status-confirmed {
    color: var(--blue-color);
    border: 1px solid var(--blue-color);
}

.account__status-normal {
    color: var(--black2-color);
    border: 1px solid var(--black2-color);
}

.form-group-wrap {
    position: relative;
}

.form-group-wrap .account__field-label {
    margin-top: 8px;
}

.form-group-content {
    width: 100%;
    margin: 0 -15px;
    display: flex;
    align-items: center;
}

.account__form-group .form-group .form-group-content label {
    margin-bottom: 8px;
    width: 100%;
}

.form-group-action {
    cursor: pointer;
    width: 20px;
}

.form-group-action .delete-row {
    font-size: 18px;
    color: var(--grey1-color);
}

.form-group-action .delete-row:hover {
    color: var(--blue-color);
}

.account__add {
    width: calc(70% - 60px);
}

.account__add-row {
    background-color: var(--white-color);
    color: var(--grey1-color);
    border: 1px dashed var(--soremo-border);
    border-radius: 12px;
    padding: 16px;
    text-align: center;
    display: block;
    cursor: pointer;
}

.account__add-row:hover {
    background-color: var(--blue-color);
    color: var(--background-color) !important;
}

.account__add-icon .icon {
    font-size: 20px;
}

.account__add-icon p {
    font-size: 13px;
    line-height: 20px;
}

.account__field-link a {
    display: flex;
    justify-content: space-between;
    background-color: var(--soremo-border);
    border-radius: 6px;
    padding: 8px 16px;
    margin-top: 8px;
    margin-bottom: 8px;
    width: 100%;
}

.account__field-link a .account__field-link-text {
    font-size: 13px;
    line-height: 20px;
    color: var(--black1-color);
    max-width: 250px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    text-overflow: ellipsis;
    display: block;
    white-space: nowrap;
}

.account__field-link a .account__field-link-time {
    font-size: 11px;
    color: var(--grey1-color);
}

.account__field-list-selected {
    display: flex;
    flex-wrap: wrap;
}

.account__field-item {
    font-size: 11px;
    line-height: 17px;
    padding: 4px 16px;
    color: var(--black1-color);
    background-color: var(--soremo-border);
    border-radius: 4px;
    margin: 4px 8px 4px 0;
    cursor: pointer;
    border: 1px solid var(--soremo-border);
}

.account__field-item:hover, .account__field-item.selected:hover  {
    color: var(--white-color);
    background-color: var(--blue-color);
    border: 1px solid var(--blue-color);
}

.account__field-item.selected {
    color: var(--black2-color);
    background-color: var(--white-color);
    border: 1px solid var(--black2-color);
}

.form-check.custom-switch {
    border-top: 1px solid var(--soremo-border);
    padding-top: 19px;
}

.form-check.custom-switch .switch-label {
    font-size: 13px;
    margin-left: 8px;
}

.form-group-wrap .account__submit-renewal {
    width: auto;
}

.select-time {
    display: none;
}

/* End content tabs */

/* Datepicker */
.mcalendar .datepicker-inline .day:before {
    width: 32px;
    height: 32px;
}

.datepicker .table-condensed .dow {
    border-bottom: none;
    color: var(--grey1-color);
}

.mcalendar .datepicker-inline .day.today:before {
    background-color: var(--grey1-color) !important;
    color: var(--white-color);
}

.mcalendar .datepicker-inline .day.active:before {
    background-color: transparent !important;
}

.datepicker .day.active, .day.active.today {
    z-index: 2;
    color: var(--white-color);
}

.datepicker .table-condensed {
    width: 100%;
    border-radius: 6px;
    filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.1));
}

.datepicker .day {
    position: relative;
}

.mcalendar .datepicker-inline .day.day__deadline:before,
.mcalendar .datepicker-inline .day.today:before {
    border-radius: 50%;
}

.mcalendar .datepicker-inline .day.day__deadline:before {
    background-color: var(--soremo-border) !important;
}

.mcalendar .datepicker-inline .day.today:before {
    background-color: var(--grey1-color) !important;
    height: 32px;
    width: 32px;
}

.mcalendar.mcalendar--small .datepicker-inline .old.day.today {
    background-color: var(--white-color) !important;
}

.mcalendar.mcalendar--small .datepicker-inline .old.day.today::before {
    background-color: var(--white-color) !important;
    color: var(--black1-color) !important;
}

.mcalendar .datepicker-inline .day.active,
.mcalendar .datepicker-inline .day.active.day__off,
.mcalendar .datepicker-inline .day.today,
.mcalendar .datepicker-inline .day.active.day__maybe {
    border: none;
}

.mcalendar .datepicker-inline .day.active:after,
.mcalendar .datepicker-inline .day.day__off.active:after,
.mcalendar .datepicker-inline .day.today:after,
.mcalendar .datepicker-inline .day.day__maybe.active:after {
    display: block;
    content: '';
    position: absolute;
    top: 0;
    left: 60%;
    background-color: transparent;
    background-image: none;
    background-repeat: no-repeat;
    background-size: 99%;
    background-position: center;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    transform: none;
}

.mcalendar .datepicker-inline .day.day__off:after,
.mcalendar .datepicker-inline .day.active.day__off:after,
.mcalendar .datepicker-inline .day.day__off.active:after,
.mcalendar.mcalendar--small .datepicker-inline .day.active.day__off:after {
    content: '';
    position: absolute;
    top: 0;
    left: 60%;
    background-image: url(../images/img_day_off.png);
    background-repeat: no-repeat;
    background-size: 99%;
    background-position: center;
    height: 16px;
    width: 16px;
}

.mcalendar .datepicker-inline .day.day__maybe:after,
.mcalendar.mcalendar--small .datepicker-inline .day.day__maybe.active:after,
.mcalendar .datepicker-inline .day.active.day__maybe:after,
.mcalendar.mcalendar--small .datepicker-inline .day.active.day__maybe:after {
    content: '';
    position: absolute;
    top: 0;
    left: 60%;
    background-image: url(../images/img_day_maybe.png);
    background-repeat: no-repeat;
    background-size: 99%;
    background-position: center;
    height: 16px;
    width: 16px;
}

.mcalendar .datepicker-inline .day.active {
    color: #000 !important;
}

.mcalendar .datepicker-inline .today.day.active {
    color: #fff !important;
}

.mcalendar.mcalendar--small .datepicker .table-condensed > tbody > tr > td.day,
.datepicker-switch {
    color: var(--black1-color);
}

.mcalendar .datepicker-inline .prev, .mcalendar .datepicker-inline .next {
    visibility: hidden !important;
}

.mcalendar .datepicker-inline .day.new, .mcalendar .datepicker-inline .day.old {
    height: 34px;
}

.account__field-tasks {
    margin: 0 -15px !important;
}

.account__field-tasks.cannot-check a{
    cursor: default!important;
}

/* End datepicker */

/* Owner */
.user-info__main.account__info {
    padding: 0;
    margin: 0 0 80px;
}
/* End owner */

@media (max-width: 992px) {
    .user-info {
        background-color: var(--white-color);
    }

    .user-info__content {
        border: none;
        padding: 0;
        margin: 0 -15px;
    }

    .account__form-group .form-group label {
        width: 100%;
    }

    .account__form-group-wrap .account__tradeoff {
        width: 100%;
        padding: 10px;
        margin: 0;
    }

    .account__form-group-wrap .account__trade-label {
        margin: 0 -10px;
    }

    .user-info__heading h3 {
        font-size: 18px;
        line-height: 27px;
        padding: 12px 0;
    }

    .user-info__images-wrap {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }

    .nav-item {
        margin: 6px 0;
    }

    .user-info__wrap {
        display: none;
    }

    .user-info__main.account__info {
        width: 100%;
        margin: 0;
    }

    .account__form-group-link .input-group {
        width: 100%;
        margin-right: 0;
    }

    .form-group-content, .account__add {
        width: 100%;
    }

    #qrcode {
        margin: 16px auto;
    }

    .mcalendar .table-condensed {
        margin: 8px 0 !important;
    }

    .account__field-link a {
        width: 100%;
    }

    .form-group-content {
        display: block;
        border: 1px solid var(--soremo-border);
        border-radius: 8px;
        margin: 0;
        margin-bottom: 16px;
        padding-top: 16px;
    }

    .form-group-action {
        width: 100%;
        text-align: center;
        margin: 10px 0 19px;
    }

    .form-group-action {
        width: 100%;
    }

    .notification-time .input-time {
        margin: 8px 0 8px 27px;
    }

    .acc_action .form-group {
        text-align: center;
    }

    .notification-time:after {
        bottom: 20px;
    }
}

@media (min-width: 993px) {
    .form-group-content {
        position: relative;
    }
    .form-group-content .form-group-action {
        position: absolute;
        bottom: 20px;
        right: 265px;
    }

    .label-errors .form-group-action {
        bottom: 38px;
    }
}

@media (min-width: 740px) and (max-width: 1023px) {
    .account__form-group-link .input-group {
        width: 80%;
        margin-top: 9px;
        margin-right: 24px;
    }

    .account__form-group .form-group label.input-radio {
        width: auto;
    }

    .form-group-content {
        display: flex;
        flex-wrap: wrap;
    }
}

.input-radio-card-content .caption--11,
.input-radio-card-content .heading--16 {
    line-height: 200% !important;
}

.input-radio-card-content .heading--16 {
    letter-spacing: -1px
}
