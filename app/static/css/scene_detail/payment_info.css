.payment {
    margin: 0;
    padding: 0;
    display: flex;
    align-content: flex-start;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
}

.payment-title {
    width: 100%;
    text-align: center;
}

.form-container .field-container:first-of-type {
    grid-area: name;
}

.form-container .field-container:nth-of-type(2) {
    grid-area: number;
}

.form-container .field-container:nth-of-type(3) {
    grid-area: expiration;
}

.form-container .field-container:nth-of-type(4) {
    grid-area: security;
}

.field-container input {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.field-container input:focus {
    outline: none;
}

.field-container {
    position: relative;
    margin: 8px 0 0;
    border-radius: 4px;
    padding: 4px 16px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.form-container {
    display: grid;
    grid-column-gap: 10px;
    grid-template-columns: auto auto;
    grid-template-rows: 90px 90px 90px;
    grid-template-areas: "name name" "number number" "expiration security";
    max-width: 400px;
    padding: 20px;
    color: #707070;
}

.payment label {
    padding-bottom: 5px;
    font-size: 13px;
}

.payment input {
    width: 100%;
    border: none;
    color: #53565a;
}

.payment input::placeholder {
    color: #a7a8a9;
}

.ccicon {
    height: 38px;
    position: absolute;
    right: 6px;
    top: calc(50% - 17px);
    width: 60px;
}

/* CREDIT CARD IMAGE STYLING */
.preload * {
    -webkit-transition: none !important;
    -moz-transition: none !important;
    -ms-transition: none !important;
    -o-transition: none !important;
}

.payment .container {
    max-width: 500px;
    width: 100%;
    max-height: 251px;
    height: 54vw;
    padding: 20px;
    margin: 20px 10px;
}

#ccsingle {
    position: absolute;
    right: 15px;
    top: 20px;
}

#ccsingle svg {
    width: 100px;
    max-height: 60px;
}

.creditcard svg#cardfront,
.creditcard svg#cardback {
    width: 100%;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.14);
    border-radius: 22px;
}

#generatecard {
    cursor: pointer;
    float: right;
    font-size: 12px;
    color: #fff;
    padding: 2px 4px;
    background-color: #909090;
    border-radius: 4px;
    cursor: pointer;
    float: right;
}

/* CHANGEABLE CARD ELEMENTS */
.creditcard .lightcolor,
.creditcard .darkcolor {
    -webkit-transition: fill .5s;
    transition: fill .5s;
}

.creditcard .visa,
.creditcard .lightblue {
    fill: #03A9F4;
}

.creditcard .visadark,
.creditcard .lightbluedark {
    fill: #0288D1;
}

.creditcard .mastercard,
.creditcard .red {
    fill: #ef5350;
}

.creditcard .mastercarddark,
.creditcard .reddark {
    fill: #d32f2f;
}

.creditcard .discover,
.creditcard .purple {
    fill: #ab47bc;
}

.creditcard .discoverdark,
.creditcard .purpledark {
    fill: #7b1fa2;
}

.creditcard .unionpay,
.creditcard .cyan {
    fill: #26c6da;
}

.creditcard .unionpaydark,
.creditcard .cyandark {
    fill: #0097a7;
}

.creditcard .jcb,
.creditcard .green {
    fill: #66bb6a;
}

.creditcard .jcbdark,
.creditcard .greendark {
    fill: #388e3c;
}

.creditcard .express,
.creditcard .lime {
    fill: #d4e157;
}

.creditcard .expressdark,
.creditcard .limedark {
    fill: #afb42b;
}

.creditcard .maestro,
.creditcard .yellow {
    fill: #ffeb3b;
}

.creditcard .maestrodark,
.creditcard .yellowdark {
    fill: #f9a825;
}

.creditcard .club,
.creditcard .orange {
    fill: #ff9800;
}

.creditcard .clubdark,
.creditcard .orangedark {
    fill: #ef6c00;
}

.creditcard .unknown,
.creditcard .grey {
    fill: #bdbdbd;
}

.creditcard .unknowndark,
.creditcard .greydark {
    fill: #616161;
}

/* FRONT OF CARD */
#svgname {
    text-transform: uppercase;
}

#cardfront .st2 {
    fill: #FFFFFF;
}

#cardfront .st3 {
    font-weight: 600;
}

#cardfront .st4 {
    font-size: 54.7817px;
}

#cardfront .st5 {
    font-weight: 400;
}

#cardfront .st6 {
    font-size: 33.1112px;
}

#cardfront .st7 {
    opacity: 0.6;
    fill: #FFFFFF;
}

#cardfront .st8 {
    font-size: 24px;
}

#cardfront .st9 {
    font-size: 36.5498px;
}

#cardfront .st10 {
    font-weight: 300;
}

#cardfront .st11 {
    font-size: 16.1716px;
}

#cardfront .st12 {
    fill: #4C4C4C;
}

/* BACK OF CARD */
#cardback .st0 {
    fill: none;
    stroke: #0F0F0F;
    stroke-miterlimit: 10;
}

#cardback .st2 {
    fill: #111111;
}

#cardback .st3 {
    fill: #F2F2F2;
}

#cardback .st4 {
    fill: #D8D2DB;
}

#cardback .st5 {
    fill: #C4C4C4;
}

#cardback .st6 {
    font-weight: 400;
}

#cardback .st7 {
    font-size: 27px;
}

#cardback .st8 {
    opacity: 0.6;
}

#cardback .st9 {
    fill: #FFFFFF;
}

#cardback .st10 {
    font-size: 24px;
}

#cardback .st11 {
    fill: #EAEAEA;
}

#cardback .st12 {
}

#cardback .st13 {
    font-size: 37.769px;
}

/* FLIP ANIMATION */
.container {
    perspective: 1000px;
}

.creditcard {
    margin: 10px;
    width: 100%;
    max-width: 400px;
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    transition: -webkit-transform 0.6s;
    -webkit-transition: -webkit-transform 0.6s;
    transition: transform 0.6s;
    transition: transform 0.6s, -webkit-transform 0.6s;
    cursor: pointer;
}

.creditcard .front,
.creditcard .back {
    position: absolute;
    width: 100%;
    max-width: 400px;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
    color: #47525d;
}

.creditcard .back {
    -webkit-transform: rotateY(180deg);
    transform: rotateY(180deg);
}

.creditcard.flipped {
    -webkit-transform: rotateY(180deg);
    transform: rotateY(180deg);
}

.delete-card-btn {
    z-index: 10;
    border-radius: 10px;
    display: none;
    position: absolute;
    top: 30%;
    left: 10%;
    background: white;
    padding: 10px 20px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    color: #009ace;
}

.delete-card-btn:hover {
    color: #0076a5;
}

.show-btn-delete .delete-card-btn {
    display: block;
}
