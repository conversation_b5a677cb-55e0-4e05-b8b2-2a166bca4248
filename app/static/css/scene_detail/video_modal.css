
.video-modal {
    color: #333;
}

.video-modal .modal-dialog {
    width: 90%;
}

@media screen and (max-width: 767px) {
    .video-modal .modal-dialog {
        width: 95%;
    }
}

.video-modal .modal-content {
    border-radius: 0;
}

.video-modal .modal-body {
    max-height: 750px;
    height: 80%;
    padding: 60px 90px;
    display: flex;
}

@media screen and (max-width: 767px) {
    .video-modal .modal-body {
        display: block;
        padding: 10px 15px;
        max-height: none;
        height: auto;
    }
}

.video-modal__left {
    width: 45%;
    max-width: 100%;
}

@media screen and (max-width: 767px) {
    .video-modal__left {
        width: 100%;
    }
}

.video-modal__right {
    width: 55%;
    padding-top: 80px;
    padding-left: 60px;
    position: relative;
    max-width: 830px;
    margin-left: auto;
}

@media screen and (max-width: 767px) {
    .video-modal__right {
        padding-left: 0;
        padding-top: 0;
        width: 100%;
    }
}

.video-modal .project-video-item {
    width: 100%;
}

.video-modal__title {
    font-size: 30px;
    margin-bottom: 40px;
}

@media screen and (max-width: 767px) {
    .video-modal__title {
        font-size: 25px;
        margin-bottom: 25px;
    }
}

.video-modal__tree {
    background: #fff;
    margin-bottom: 40px;
    padding-right: 30px;
    max-height: calc(100% - 90px);
    overflow: auto;
}

@media screen and (max-width: 767px) {
    .video-modal__tree {
        max-height: calc(100vh - 90px);
    }
}

.video-modal .modal-tree-info {
    padding: 0;
    margin: 0;
    list-style: none;
    position: relative;
}

.video-modal .modal-tree-info li:before {
    content: '';
    position: absolute;
    left: 30px;
    top: 100%;
    display: block;
    border-left: 4px solid #707070;
    height: 35px;
}

.video-modal .chapter-name,
.video-modal .scene-name {
    margin-bottom: 35px;
    position: relative;
}

.video-modal .chapter-name span,
.video-modal .scene-name span {
    font-size: 20px;
    display: inline-block;
    background-image: linear-gradient(45deg, #e2e2e2, #cbc8c8);
    padding: 6px 25px;
    border-radius: 20px;
    min-height: 40px;
    min-width: 60px;
}

@media screen and (max-width: 767px) {
    .video-modal .chapter-name span,
    .video-modal .scene-name span {
        font-size: 18px;
        padding: 7px 25px;
        width: calc(100% - 20px);
    }
}

.video-modal .chapter-name input[type='text'],
.video-modal .chapter-name textarea,
.video-modal .scene-name input[type='text'],
.video-modal .scene-name textarea,
.video-modal .video-version input[type='text'],
.video-modal .video-version textarea {
    background-color: transparent;
    border: none;
    padding: 0;
}

.video-modal .chapter-name input[type='text']:focus,
.video-modal .chapter-name textarea:focus,
.video-modal .scene-name input[type='text']:focus,
.video-modal .scene-name textarea:focus,
.video-modal .video-version input[type='text']:focus,
.video-modal .video-version textarea:focus {
    outline: none;
    border: none;
}

.video-modal .chapter-name:focus,
.video-modal .scene-name:focus,
.video-modal .video-version:focus {
    outline: none;
}

.video-modal .chapter-name:focus span,
.video-modal .scene-name:focus span,
.video-modal .video-version:focus span {
    height: 40px;
}

.video-modal .modal-tree {
    margin: 0 0 0 30px;
    padding: 0;
    list-style: none;
    text-align: left;
    position: relative;
}

.video-modal .modal-tree li {
    font-size: 12px;
    position: relative;
}

.video-modal .modal-tree li:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    display: block;
    height: 28px;
    border-bottom: 4px solid #707070;
    width: 50px;
}

.video-modal .modal-tree li:after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -24px;
    display: block;
    border-left: 4px solid #707070;
    height: calc(100% + 24px);
}

.video-modal .modal-tree li ul {
    list-style: none;
    padding-left: 75px;
    /* stylelint-disable */
    /* stylelint-enable */
}

.video-modal .modal-tree li ul li:last-child:after {
    display: none;
}

.video-modal .modal-tree li ul li[data-max-version="2"]:last-child:after {
    display: block;
    height: calc(100% - 15px);
    bottom: 15px;
}

.video-modal .modal-tree li span:not(.video-version) {
    /* stylelint-disable-line */
    display: inline-block;
    background-image: linear-gradient(45deg, #e2e2e2, #cbc8c8);
    padding: 6px 25px;
    border-radius: 20px;
    margin-left: 50px;
}

.video-modal .modal-tree li span:not(.video-version):hover {
    cursor: pointer;
}

.video-modal .modal-tree .video-variation {
    padding-bottom: 35px;
}

.video-modal .modal-tree .video-variation ul {
    /* stylelint-disable */
    /* stylelint-enable */
}

.video-modal .modal-tree .video-variation ul li {
    padding-top: 10px;
}

.video-modal .modal-tree .video-variation ul li:first-child {
    padding-top: 0;
    margin-left: -75px;
}

.video-modal .modal-tree .video-variation ul li:first-child:before,
.video-modal .modal-tree .video-variation ul li:first-child:after {
    display: none;
}

.video-modal .modal-tree .video-variation.variation-active {
    /* stylelint-disable */
    /* stylelint-enable */
}

.video-modal .modal-tree .video-variation.variation-active:before {
    border-bottom-color: #009dc4;
    z-index: 0;
}

.video-modal .modal-tree .video-variation.variation-active:after {
    z-index: 1;
}

.video-modal .modal-tree .video-variation.variation-active li:before,
.video-modal .modal-tree .video-variation.variation-active li:after {
    border-color: #009dc4;
}

.video-modal .modal-tree .video-variation .version-active span {
    /* stylelint-disable-line */
    background-image: none;
    background-color: #009dc4;
    color: #fff;
}

.video-modal .modal-tree .video-variation:before {
    top: -10px;
}

.video-modal__preview {
    font-size: 25px;
    margin-bottom: 30px;
}

@media screen and (max-width: 767px) {
    .video-modal__preview {
        font-size: 15px;
        margin-bottom: 15px;
    }
}

.video-modal__action {
    position: absolute;
    top: 0;
    right: 0;
}

@media screen and (max-width: 767px) {
    .video-modal__action {
        position: relative;
        text-align: center;
        margin: 30px 0;
    }
}

.video-modal__action .video-modal-btn {
    border-radius: 50px;
    height: 54px;
    border: 2px solid #009dc4;
    line-height: 50px;
    font-size: 25px;
    text-transform: uppercase;
    min-width: 192px;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.06);
}

@media screen and (max-width: 767px), screen and (min-device-width: 768px) and (max-device-width: 1024px) {
    .video-modal__action .video-modal-btn {
        font-size: 17px;
        height: 36px;
        line-height: 32px;
        min-width: auto;
    }
}

.video-modal__action .video-save-btn {
    background-color: #009dc4;
    margin-right: 35px;
}

@media screen and (max-width: 767px), screen and (min-device-width: 768px) and (max-device-width: 1024px) {
    .video-modal__action .video-save-btn {
        margin: 0 auto 15px auto;
        width: 115px;
        display: block;
    }
}

.video-modal__action .video-save-btn:before {
    content: '';
    display: inline-block;
    margin-right: 30px;
    width: 21.26px;
    height: 21.14px;
    background-image: url("../../images/icon-save.svg");
    background-size: cover;
}

@media screen and (max-width: 767px), screen and (min-device-width: 768px) and (max-device-width: 1024px) {
    .video-modal__action .video-save-btn:before {
        width: 10.9px;
        height: 10.68px;
        margin-right: 15px;
    }
}

.video-modal__action .video-cancel-btn {
    text-transform: uppercase;
}

@media screen and (max-width: 767px), screen and (min-device-width: 768px) and (max-device-width: 1024px) {
    .video-modal__action .video-cancel-btn {
        display: block;
        margin: 0 auto;
        width: 136px;
    }
}

.video-modal__action .video-cancel-btn:before {
    content: '';
    display: inline-block;
    margin-right: 30px;
    width: 18.16px;
    height: 17.69px;
    background-image: url("../../images/icon-close.svg");
    background-size: cover;
}

@media screen and (max-width: 767px), screen and (min-device-width: 768px) and (max-device-width: 1024px) {
    .video-modal__action .video-cancel-btn:before {
        width: 10.83px;
        height: 11.04px;
        margin-right: 15px;
    }
}

.video-modal-info-detail {
    position: absolute;
    top: 25px;
    left: 25px;
    max-width: calc(100% - 120px);
}

@media screen and (max-width: 767px), screen and (min-device-width: 768px) and (max-device-width: 1024px) {
    .video-modal-info-detail {
        top: 8px;
        left: 8px;
        max-width: calc(100% - 40px);
    }
}

.video-modal-breadcrumbs {
    background-color: #333;
    border-radius: 50px;
    color: #fff;
    font-size: 20px;
    padding: 6px 20px;
}

@media screen and (max-width: 767px), screen and (min-device-width: 768px) and (max-device-width: 1024px) {
    .video-modal-breadcrumbs {
        font-size: 10px;
        padding: 6px 15px;
    }
}

.video-modal-version-text {
    background-color: #53565a;
    color: #fff;
    border-radius: 50px;
    font-size: 16px;
    padding: 5px 20px;
    display: inline-block;
    margin-top: 8px;
}

@media screen and (max-width: 767px), screen and (min-device-width: 768px) and (max-device-width: 1024px) {
    .video-modal-version-text {
        font-size: 8px;
        padding: 5px 15px;
    }
}

.video-modal-version {
    position: absolute;
    width: 24px;
    height: 24px;
    line-height: 25px;
    font-size: 1em;
    color: #fff;
    background-color: #009dc4;
    border-radius: 50%;
    top: 18px;
    right: 18px;
    text-align: center;
    box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.16);
    z-index: 1;
    cursor: pointer;
    font-weight: unset !important;
    padding: unset !important;
}

@media screen and (max-width: 767px), screen and (min-device-width: 768px) and (max-device-width: 1024px) {
    .video-modal-version {
        font-size: 10px;
        width: 20px;
        height: 20px;
        line-height: 25px;
        top: 8px;
        right: 8px;
    }
}

.video-modal .video-item-bullets {
    width: 190px;
    overflow: hidden;
}

.video-modal .video-item-bullet-list {
    overflow: visible;
    width: auto;
}

.video-modal .video-item-control {
    margin-top: 10px;
}

.video-modal .video-item-bullet {
    width: 15px;
    height: 15px;
    margin: 0 6px;
}

.video-modal .video-item-bullet.active {
    width: 15px;
    height: 15px;
    margin-top: 0;
}

.video-modal .video-item-bullet-prev,
.video-modal .video-item-bullet-next {
    width: 30px;
    height: 30px;
}

.video-modal-component-content-video {
    position: relative;
    overflow: hidden;
    padding-bottom: 56.25%;
}

.video-modal .chapter-edit,
.video-modal .scene-edit,
.video-modal .version-edit {
    display: inline-block;
    vertical-align: bottom;
    margin-left: 2px;
    margin-bottom: 10px;
    opacity: 0;
    visibility: hidden;
}

@media screen and (max-width: 767px) {
    .video-modal .chapter-edit,
    .video-modal .scene-edit,
    .video-modal .version-edit {
        opacity: 1;
        visibility: visible;
    }
}

.video-modal-version-item {
    position: absolute;
    display: none;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
}

.video-modal-version-item.version-active {
    display: block;
}

.video-modal-version-item video {
    margin: 0 auto;
}

.variation-upload {
    position: absolute;
    top: 4px;
    left: 14px;
    z-index: 9;
    opacity: 0;
    transition: all .3s;
}

@media screen and (max-width: 767px) {
    .variation-upload {
        opacity: 1;
    }
}

.variation-upload .button {
    visibility: hidden;
}

@media screen and (max-width: 767px) {
    .variation-upload .button {
        visibility: visible;
    }
}

.variation-upload:hover {
    opacity: 1;
    transition: all .3s;
}

.variation-upload:hover .button {
    visibility: visible;
}

.variation-upload--before {
    top: auto;
    bottom: -40px;
    left: 20px;
}

.variation-upload--after {
    top: 50%;
    left: -10px;
}

.variation-upload__file-input {
    display: none !important;
    /* stylelint-disable-line */
}

.variation-upload__link {
    border-radius: 50%;
    line-height: 1;
}

.variation-upload__link:before {
    content: '+';
    width: 24px;
    height: 24px;
    line-height: 24px;
    background-color: #53565a;
    color: #fff;
    font-size: 24px;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.06);
}

.input-field-error {
    color: #e6002d;
    font-size: 10px;
    position: absolute;
    top: calc(100% + 3px);
    left: 50px;
}

.open-video-modal img.video-upload {
    position: absolute;
    right: 25px;
    background-color: #FFFFFF;
    padding: 10px;
    border-radius: 50px;
    top: 5px;
    width: 10px;
    background-image: url(../../images/icon-settings.svg);
    background-size: contain;
}

.video-status-done div{
    position: absolute;
    background-color: #FFFFFF;
    padding: 10px;
    border-radius: 50px;
    top: 5px;
    width: 24px;
    height: 24px;
    background-image: url(../../images/icon-heart-done.svg);
    background-repeat: no-repeat;
    background-size: contain;
}

.video-icon-done div{
    background-image: url(../../images/icon-heart-done.svg);
}

.video-icon-undone div{
    background-image: url(../../images/icon-heart-undone.svg);
}

.video-icon-undone:not(.cannot-check) div:hover, .video-icon-done:not(.cannot-check) div:hover{
    background-image: url(../../images/icon-heart-hover.svg);
}

.video-modal .video-modal-version.gray-background {
    background: rgb(83, 86, 90);
}

.video-modal .video-version:hover .comment-edit-button-group,
.video-modal .video-version:active .comment-edit-button-group {
    display: flex;
}

/* for mobile screen */
@media screen and (max-width: 576px) {
    .video-modal .video-version .comment-edit-button-group {
        display: flex;
    }
}

/*for ipad screen*/
@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) {
    .video-modal .video-version .comment-edit-button-group {
        display: flex;
    }

    .variation-upload {
        opacity: 1;
    }

    .variation-upload .button {
        visibility: visible;
    }

    .video-modal .chapter-edit,
    .video-modal .scene-edit,
    .video-modal .version-edit {
        opacity: 1;
        visibility: visible;
    }
}

.video-version.left .comment-edit-button-group {
    right: unset;
    margin-left: 50px;
    bottom: -15px;
    z-index: 10;
}

.video-modal__header {
    font-size: 12px;
    display: flex;
}

.label-reupload {
    cursor: pointer;
    color: #009dc4;
}

.video-modal__right .selected_file {
    margin-left: auto;
    max-width: 50%;
    overflow: hidden;
}
