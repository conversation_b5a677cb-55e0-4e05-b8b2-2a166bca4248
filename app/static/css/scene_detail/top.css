body {
    overflow-y: scroll;
}

.owner-top {
    margin-top: 75px;
    margin-bottom: 100px;
}

.project-item__info {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);}

.video-item-button-bottom:before {
    margin: -20px auto 8px;
}

.project-item__title:before {
    padding-right: 5px;
}

.project-item__title {
    text-transform: uppercase;
}

.video-item-bullets {
    justify-content: center;
}

.video-item-bullet,
.video-item-bullet.active {
    position: relative;
    height: 10px;
    width: 10px;
    background-color: #a7a8a9;
    border-radius: 50px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    transition: .3s;
}

.video-item-bullet.active {
    height: 12px;
    width: 12px;
    background-color: #009ace;
}

.video-comment-title {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.video-item-component .video-item-component-content {
    overflow: hidden;
}

.video-item-component .video-comment-seen-item-img,
.video-item-component .video-comment-user-img,
.project-item__member-item img {
    border-radius: 50%;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.video-time-slider-start.disabled {
    color: #d9d9d9;
}

.video-button:hover {
    background-color: rgba(122, 122, 122, 0.4);
}

.video-item-component .video-comment-action .video-comment-delete.button--disabled {
    color: #a7a8a9;
}

.project-chapter-video-item {
    position: relative;
    width: auto;
}

.project-chapter-video-item video {
    height: 120px;
}

video {
    object-fit: cover;
    background: #333333;
}

.project-video-item.show-comment .video-item-wrap {
    width: calc(50% - 15px);
}

.project-video-item.show-comment .video-item-wrap-noshare-comment {
    width: calc(100% - 15px) !important;
}

.project-tab-update .project-video-item {
    margin-bottom: 20px;
}

.video-item-history-item {
    margin-bottom: 10px;
}

.project-video-item.active.show-comment .video-item-list {
    width: calc(100% - 15px);
}

.video-time-slider-item .video-time-slider-content .video-time-slider-end,
.video-time-slider-item .video-time-slider-label .video-time-slider-label-end {
    opacity: 1;
    visibility: visible;
}

.video-item-component:hover .video-item-edit.show,
.video-item-component .video-item-edit.show,
.video-item-component:hover .video-item-share.show,
.video-item-component .video-item-share.show {
opacity: 1;
visibility: visible;
transition: all .3s; }

.video-button.disabled {
    background-color: rgba(108, 108, 108, 0.12);
    color: #ffffff24;
    transition: 0s;
}

.video-button.disabled.video-item-button-top:before{
    background-color: #ffffff24;
}

.video-button.confirmed {
    background-color: rgba(0, 232, 53, 0.54);
    color: white;
    transition: 0s;
}

.video-button.confirmed.video-item-button-top:before{
    background-color: rgba(0, 232, 53, 1);
}

.project-chapter-video-item-content {
    border: 3px solid transparent;
}

.project-tab.project-tab-update.active video {
    border: 4px solid transparent;
}

.project-tab.project-tab-update.active video.focus {
    box-shadow(0 0 5px rgba(81, 203, 238, 1));
    border: 4px solid rgba(81, 203, 238, 1);
    transition: .3s;
}

.project-tab.project-tab-update.active .clickable-title:hover {
    color: #0076a5;
    cursor: pointer;
}

.video-item-bullet-new::after {
    position: absolute;
    top: -2px;
    right: -2px;
    height: 6px;
    width: 6px;
    background-color: red;
    content: '';
    border-radius: 50px;
}

.video-item-bullet-list {
    padding: 10px;
}

.video-item-component .version-tag, .video-item-component .new-tag {
    position: absolute;
    z-index: 11;
    padding: 2px 15px;
    right: 0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    cursor: pointer;
    font-weight: 700;
    text-align: center;
    top: 20px;
}

.video-item-component .version-tag:before {
    content: attr(data-content);
    background: #333333;
    color: #fff;
    position: absolute;
    top: -5px;
    right: -5px;
    border-radius: 50%;
}

.video-item-component .version-tag:before:hover {
    background: #0076a5;
    color: white;
}

.video-item-component .version-tag.after, .video-item-component .new-tag {
    background: #009ace;
    color: white;
}

.video-item-component .version-tag.after:hover {
    color: white;
    filter: brightness(0.9);
}

.project-video-item .video-comment-user-img {
    border-radius: 50%;
}

.project-video-item .video-comment-item-reply-user {
    text-align: center;
}

.project-video-item .video-comment-item-reply {
    border: none;
}

.video-comment-seen {
    position: absolute;
    bottom: -20px;
    left: 0;
}

.project-video-item .video-comment-item-reply {
    padding: 0;
}

.project-video-item .video-comment-item-reply-content {
    padding: 10px;
    position: relative;
    border: 1px solid #d9d9d9;
    border-radius: 5px;
    width: 100%;
}

.sub-item .video-comment-item-reply-content {
    width: calc(90% - 60px);
}

.project-video-item .video-comment-item-reply-user {
    padding-right: 5px;
    width: 60px;
}

.project-video-item .video-comment-item-reply.right {
    flex-direction: row-reverse;
}

.project-video-item .video-comment-item-reply.right .video-comment-item-reply-user {
    padding-right: 0;
    padding-left: 5px;
    width: 60px;
}

.project-video-item .video-comment-item-reply.sub-item {
    margin-left: 60px;
}

.sub-item.right .video-comment-item-reply-content {
    width: calc(90% - 60px);
    float: right;
}

.project-video-item .video-comment-item-reply.sub-item.right {
    margin-left: 0;
    margin-right: 60px;
}

.comment-edit-button-group {
    width: 60px;
    height: 20px;
    border: 1px solid #d9d9d9;
    border-radius: 10px;
    position: absolute;
    right: 10px;
    bottom: -10px;
    background: white;
}

.video-comment-item-reply:not(.editing):hover .comment-edit-button-group {
    display: flex;
}

.right .comment-edit-button-group {
    left: 10px;
}

.comment-edit-button-group {
    display: none;
}

.comment-edit-button-group button {
    width: 50%;
    background: white;
}

.comment-edit-button-group button:hover {
    width: 50%;
    background: #d9d9d9;
}

.comment-edit-button-group #edit-comment {
    border-radius: 10px 0 0 10px;
}

.comment-edit-button-group #edit-comment img {
    position: absolute;
    top: 9%;
    left: 16%;
}

.comment-edit-button-group #delete-comment {
    border-radius: 0 10px 10px 0;
}

.comment-edit-button-group #delete-comment img {
    position: absolute;
    top: 9%;
    right: 14%;
}

.comment-edit-button-group button img {
    width: 15px;
    margin: auto;
    filter: invert(87%) sepia(0%) saturate(5%) hue-rotate(352deg) brightness(104%) contrast(87%);
}

.comment-edit-button-group button:hover img {
    filter: invert(94%) sepia(61%) saturate(0%) hue-rotate(189deg) brightness(106%) contrast(106%);
}

.video-comment-action .button,
.video-comment-action-edit .button {
    color: #a7a8a9;
}

.video-comment-action .button:hover,
.video-comment-action-edit .button:hover {
    color: #0076a5;
}

.project-video-item .video-comment-item.sub-item {
    padding: 10px 10px 0;
    width: 80%;
    margin-left: 60px;
}

.button-switch {
    transform: scale(0.8);
}

.video-comment-input-pin img {
    width: 20px;
    margin: 0 8px 0 7px;
    filter: invert(87%) sepia(0%) saturate(5%) hue-rotate(352deg) brightness(104%) contrast(87%);
}

.video-comment-input-pin {
    margin-left: 5px;
    background: #fff;
    min-width: 35px;
    cursor: pointer;
}

.video-comment-input-label.button--disabled img {
    filter: invert(78%) sepia(4%) saturate(77%) hue-rotate(169deg) brightness(87%) contrast(86%);
}

.video-comment-input-label img {
    width: 25px;
    filter: invert(32%) sepia(10%) saturate(233%) hue-rotate(174deg) brightness(99%) contrast(91%);
}

.video-comment-input-label:hover img {
    filter: invert(52%) sepia(62%) saturate(2660%) hue-rotate(160deg) brightness(86%) contrast(101%);
}


.project-video-item .video-comment-message .video-comment-input-label {
    margin-left: 0;
    padding: 0 5px;
}

.pin-popup {
    position: absolute;
    border-radius: 5px;
    border: 1px solid #d9d9d9;
    padding: 10px;
    bottom: 50px;
    left: 40px;
    background: #fff;
    z-index: 2;
    display: none;
}

.pin-popup.show {
    display: block;
}

.video-comment-item.comment-form {
    position: relative;
    padding: 10px 10px 0;
}

.video-comment-item.comment-form .video-comment-message,
.video-comment-item .video-comment-message {
    padding: 10px 0;
}

.comment-for-version, .comment-for-scene {
    color: #d9d9d9;
    position: relative;
}

.comment-for-version.checked, .comment-for-scene.checked {
    color: #707070;
}

.comment-for-version.checked .comment-pin-time {
    color: #009ace;
}

.video-comment-input-pin.active img {
    filter: invert(45%) sepia(100%) saturate(512%) hue-rotate(147deg) brightness(88%) contrast(104%);
}

input[type=radio] {
    opacity: 0;
}

.pin-popup label {
    margin-left: 10px;
}

.pin-popup span.radio-input {
    position: absolute;
    top: 5px;
    left: 0;
    height: 15px;
    width: 15px;
    border-radius: 50%;
    background: #d9d9d9;
}

.pin-popup input:checked~span.radio-input {
    background: #009ace;
}

.pin-popup label:not(.checked):hover span.radio-input {
    background: #707070;
}

.pin-time-reload {
    margin-left: 5px;
    filter: invert(100%) sepia(0%) saturate(1632%) hue-rotate(213deg) brightness(92%) contrast(84%);
}

.pin-time-reload:hover {
    filter: invert(45%) sepia(13%) saturate(5590%) hue-rotate(157deg) brightness(97%) contrast(103%);
}

.comment-title {
    min-width: 80%;
    position: relative;
    padding: 5px 0;
    text-align: center;
    border: 1px solid #d9d9d9;
    margin: 20px 10%;
    border-radius: 50px;
    font-weight: bold;
}

.comment-title:before, .comment-title:after {
    content: '';
    height: 1px;
    width: 12.5%;
    background: #d9d9d9;
    position: absolute;
    top: 50%;
}

.comment-title:before {
    left: -12.5%;
}

.comment-title:after {
    right: -12.5%;
}

.project-video-item .video-comment-seen {
    width: auto;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    bottom: -20px;
}

.project-video-item .right .video-comment-seen {
    right: 0;
    left: auto;
}

.project-video-item .video-comment-seen .video-comment-seen-item {
    margin: 0 2px;
}

.project-video-item .video-comment-item-reply {
    margin-bottom: 30px;
}

@media (max-width: 576px) {
    .project-video-item.show-comment .video-item-wrap {
        width: 100%;
    }
}

.project-video-item .video-comment-content {
    width: 100%;
}

.video-comment-text {
    overflow: hidden;
}

.video-comment-seen-item.more {
    filter: invert(44%) sepia(0%) saturate(435%) hue-rotate(174deg) brightness(96%) contrast(79%);
}

.video-comment-item-reply-content-container {
    width: 80%;
}

.video-comment-item-reply-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.right .video-comment-item-reply-name {
    text-align: right;
}

.project-video-item .video-comment-audio-title:hover {
    cursor: pointer;
}

.video-comment-input-pin .pin-icon-time {
    margin: 1px 0 0 1px;
    font-size: .7em;
    min-width: 25px;
    text-align: center;
    display: none;
}

.video-comment-input-pin.active .pin-icon-time {
    display: block;
}

.video-item-comment {
    max-height: calc(100vh - 400px);
    overflow-y: scroll;
}

.video-pin-time:before, .video-pin-start {
    cursor: pointer;
    margin-right: 24px;
    margin-top: 2px;
}

.project-video-item .video-item-comment-close {
    transform: rotate(0deg);
}

.project-chapter-video-item-menu, .project-delivery-item-menu {
    position: absolute;
    min-width: 100px;
    max-width: 300px;
    right: 20px;
    background: #333333;
    display: none;
}

.project-chapter-video-item {
    cursor: grab;
}

.project-chapter-video-item-menu li, .project-delivery-item-menu li {
    padding: 5px 10px;
    list-style: none;
    cursor: pointer;
}

.project-chapter-video-item-menu li:hover,
.project-delivery-item-menu li:hover {
    background: #707070;
}

.project-chapter-video-item-menu li:hover span,
.project-delivery-item-menu li:hover span {
    color: #0076a5;
}

.project-chapter-video-item-menu li:hover img,
.project-delivery-item-menu li:hover img {
    filter: invert(54%) sepia(81%) saturate(3591%) hue-rotate(162deg) brightness(95%) contrast(103%);
}

.project-chapter-video-item-menu li span,
.project-delivery-item-menu li span {
    padding-left: 5px;
    float: right;
    margin: auto;
    color: #fff;
}

.project-chapter-video-item-menu li img,
.project-delivery-item-menu li img {
    width: 20px;
    height: 20px;
}

.project-chapter-video-item:hover .project-chapter-video-item-menu,
.project-delivery-item:hover .project-delivery-item-menu {
    display: block;
    z-index: 10;
}

.scenes__rate {
    top: 5px;
    bottom: auto;
    left: 5px;
}

.project-delivery .video-item-variation {
    position: absolute;
    top: 25px;
    left: 10px;
    color: #fff;
    overflow: hidden;
    text-align: left;
    font-size: 11px;
}

.empty-message {
    flex-grow: 1;
    text-align: center;
    padding: 20%;
}

.video-item-list .new-update-tag {
    margin: 0 2px;
    background: #009ace;
    padding: 5px 20px;
    font-weight: bold;
    color: #fff;
    position: absolute;
    top: -5px;
    left: -5px;
    z-index: 10;
}

.video-item-list {
    position: relative;
}

.video-item-thumbnail {
    position: relative;
}

.variation-name {
    position: absolute;
    z-index: 2;
    padding: 5px 10px;
    background: #009ace;
    color: white;
    top: 0;
    display: none;
}

.video-item-thumbnail:hover .variation-name{
    display: block;
}

.project-delivery-item {
    position: relative;
}

.upload-scene-video-btn img {
    filter: invert(93%) sepia(93%) saturate(28%) hue-rotate(20deg) brightness(107%) contrast(105%);
}

.right .comment__download--bottom {
    float: right;
}

.comment__download--bottom {
    margin-left: 0;
}

.project-video-item.show-comment.remove_on_close {
    display: flex;
}

.project-video-item.remove_on_close:not(.show-comment) {
    display: none;
}

.video-comment-seen-item {
    border-radius: 50px;
}

.project-delivery-item-content:hover video,
.project-chapter-video-item-content:hover video,
.video-item-component-content-video:hover video {
    filter: none;
    cursor: pointer;
}

.gray-scale {
    filter: grayscale(100%) !important;
}

.video-item-component-content-video .video-item-variation {
    position: absolute;
    top: 50px;
    left: 20px;
    color: #fff;
    overflow: hidden;
    text-align: left;
    font-size: 1em;
}

.video-comment-time {
    font-size: .9rem !important;
}

.loader {
    position: fixed;
    left: 45%;
    left: 50%;
    z-index: 10000;
    top: 40%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.loader img {
    width: 40px;
}

.project-item__filter-item {
    position: relative;
}

.project-item__filter-item span {
    position: absolute;
    top: -6px;
    right: 6px;
    background: #009ace;
    text-align: center;
    border-radius: 50px;
    line-height: 4px;
    width: auto;
    min-width: 15px;
    height: 15px;
    padding: 6px 2px 0;
    font-size: .7em;
    color: #fff;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.gray.project-item__filter-item span {
    background: #a5a6a7;
}

.project-item__filter-item span[value='0'] {
    display: none;
}

/* .video-pin-time span {
    position: absolute;
    top: -20px;
    left: 10px;
    background: #fcfcfc;
    color: #009ace;
    padding: 5px 10px;
    max-width: fit-content;
    opacity: 0;
    transition: .2s;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
} */

.video-pin-time {
    position: relative;
}

.video-pin-time:hover span {
    opacity: 1;
}

.modal-backdrop {
    z-index: 998;
}
.modal {
    z-index: 1000;
}

.bootbox.modal {
    z-index: 1050;
}

.modal.fade,
.modal.fade .modal-dialog {
    transition: none;
}

.play-video {
    position: absolute;
    top: 1px;
    height: 50%;
    width: 100%;
}

#processingSceneModal .modal-dialog {
    margin: 0;
}

#processingSceneModal .modal-content {
    border: none;
    box-shadow: none;
    border-radius: 0;
    min-height: 100vh;
}
@media (max-width: 576px) {
    #processingSceneModal .modal-content {
        min-height: calc(100vh - 150px);
    }

    #processingSceneModal .video-item-comment {
        max-height: none;
        overflow-y: scroll;
    }

    #processingSceneModal {
        overflow-y: scroll !important;
    }
}

#processingSceneModal .modal-body {
    padding: 0;
    max-height: none;
}

#processingSceneModal.modal {
    margin-top: calc(13vh + 105px);
}

#processingSceneModal {
    overflow: hidden;
}
@media (max-width: 576px) {
    #processingSceneModal.modal {
        top: 0;
        margin-top: 80px;
    }
}

.video-comment-seen-item img {
    border-radius: 50%;
}

.video-item-button-bottom.update-owner:before {
    background-image: url("../../images/icon-heart.svg");
}

.video-item-button-bottom.mark-as-ok:before {
    background-image: url("../../images/icon-check.svg");
}

.video-item-button-bottom.upload_version_admin:before {
    background-image: url("../../images/icon-upload.svg");
}

.video-item-button-bottom.project-chapter-video-undone:before {
    background-image: url("../../images/icon-undone.svg");
    filter: none;
}


.project-chapter-video-item:hover .bottom-menu,
.project-delivery-item-content:hover .bottom-menu {
    box-shadow: 0 2px 6px 1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    z-index: 2;
}

.project-chapter-video-item:hover video,
.project-delivery-item-content:hover video{
    background: transparent;
}

.project-member-action {
    width: 60px;
    height: 20px;
    border: 1px solid #d9d9d9;
    border-radius: 10px;
    position: absolute;
    right: 10px;
    background: white;
    display: none;
    bottom: -10px;
}

.project-member-action a img {
    width: 15px;
    margin: auto;
    filter: invert(87%) sepia(0%) saturate(5%) hue-rotate(352deg) brightness(104%) contrast(87%);
}

.project-member-action a:hover img {
    filter: invert(45%) sepia(100%) saturate(512%) hue-rotate(147deg) brightness(88%) contrast(104%);
}

.project-member-action #edit-member {
    border-radius: 10px 0 0 10px;
}

.project-member-action #edit-member img {
    position: absolute;
    top: 9%;
    left: 16%;
}

.project-member-action #delete-member {
    border-radius: 0 10px 10px 0;
}

.project-member-action #delete-member img {
    position: absolute;
    top: 9%;
    right: 14%;
}

.member-item:hover .project-member-action {
    display: flex;
}
.project-member-setting .project-setting-member__setting {
    position: relative;
}

.setting-member {
    position: relative;
    right: 10px;
    text-align: left;
}
.member-item {
    position: relative;
}

.popover {
    left: 0;
    border-radius: 8px;
    border: none;
    padding: 15px;
    width: 100%;
    max-width: 100%;
    max-height: 70vh;
    overflow: hidden;
    position: absolute;
    right: 0;
    top: 120px;
    display: none;
    z-index: 999;
}

.popover-overlay {
    background: #0000007a;
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    display: none;
}

.popover-overlay.shown {
    display: block;
}


@media (max-width: 576px) {
    .popover {
        max-width: 100%;
        left: auto !important;
    }
}

.popover.show {
     display: block;
}

.project-chapter-title {
    position: relative;
}

.project-chapter-title:hover .project-chapter-title-edit {
    display: flex;
    left: 10px;
    top: 30px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);;
    z-index: 3;
}

.project-chapter-title:hover .project-chapter-title-edit .edit-product-scene {
    border-radius: 10px 0 0 10px;
}

.project-chapter-title:hover .project-chapter-title-edit .edit-product-scene img {
    position: absolute;
    top: 9%;
    left: 16%;
}

.project-chapter-title:hover .project-chapter-title-edit .delete-product-scene {
    border-radius: 0 10px 10px 0;
}

.project-chapter-title:hover .project-chapter-title-edit .delete-product-scene img {
    position: absolute;
    top: 9%;
    right: 14%;
}

.skeleton-wrapper {
    background: transparent;
    border-color: transparent;
    margin: 10px 15px;
}

.skeleton-wrapper-inner {
    height: 150px;
    padding: 15px;
    position: relative;
}

.skeleton-wrapper-body div {
    -webkit-animation-duration: 1s;
    -webkit-animation-fill-mode: forwards;
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-name: placeholderSkeleton;
    -webkit-animation-timing-function: linear;
    background: #f6f7f8;
    background-image: -webkit-gradient(linear, left center, right center, from(#f6f7f8), color-stop(.2, #edeef1), color-stop(.4, #f6f7f8), to(#f6f7f8));
    background-image: -webkit-linear-gradient(left, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%);
    background-repeat: no-repeat;
    background-size: 800px 104px;
    height: 104px;
    position: relative;
}

.skeleton-wrapper-body {
    -webkit-animation-name: skeletonAnimate;
    background-image: -webkit-gradient(linear, center top, center bottom, from(deg), color-stop(0, red), color-stop(.15, orange), color-stop(.3, yellow), color-stop(.45, green), color-stop(.6, blue), color-stop(.75, indigo), color-stop(.8, violet), to(red));
    background-image: -webkit-linear-gradient(135deg, red 0%, orange 15%, yellow 30%, green 45%, blue 60%, indigo 75%, violet 80%, red 100%);
    background-repeat: repeat;
    background-size: 50% auto;
}


.skeleton-wrapper-body div {
    position: absolute;
    right: 15px;
    left: 15px;
    top: 15px;
}

div.skeleton-avatar {
    height: 60px;
    width: 60px;
    border-radius: 60px;
    right: auto;
}

div.skeleton-author {
    top: 20px;
    height: 20px;
    left: 95px;
    width: 150px;
}

div.skeleton-label {
    left: 95px;
    top: 50px;
    height: 10px;
    width: 100px;
}

div.skeleton-content-1,
div.skeleton-content-2,
div.skeleton-content-3 {
    left: 15px;
    right: 15px;
    height: 10px;
}

div.skeleton-content-1 {
    top: 100px;
}

div.skeleton-content-2 {
    top: 120px;
}

div.skeleton-content-3 {
    top: 140px;
}

@-webkit-keyframes placeholderSkeleton {
    0% {
        background-position: -468px 0;
    }
    100% {
        background-position: 468px 0;
    }
}

@-webkit-keyframes skeletonAnimate {
    from {
        background-position: top left;
    }
    to {
        background-position: top right;
    }
}

.project-item.active #id-top-done-banner {
    display: none;
}

#searchModal .modal-content {
    position: relative;
    background-color: #fff;
    background-clip: padding-box;
    border: none;
    border-radius: 0;
    outline: 0;
    box-shadow: none;
}

#searchModal:before {
    display: none;
}

#searchModal {
    transition: .4s ease-in-out;
}

#shareModal .modal-content {
    max-width: 90vw;
}

#shareModal .modal-title {
    max-width: 80%;
    line-break: anywhere;
}

.notification-setting {
    margin-left: 2px;
    height: 2.5em;
    width: 2.5em;
    border-radius: 50%;
    background-color: #fff;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.notify {
    width: 100%;
    height: 100%;
    padding: 6px 7px 6px 6px;
}

img.notify {
    filter: invert(32%) sepia(8%) saturate(271%) hue-rotate(174deg) brightness(100%) contrast(92%);
}

.notification-setting:hover img {
    filter: none;
}

.no-notification .project-item__general, .sproject.no-notification {
    filter: grayscale(100%) brightness(0.6);
}

.for-export-menu .dropdown-item, .for-export-menu a {
    color: #333333 !important;
    text-decoration: none;
}


.for-export-menu .dropdown-item:hover, .for-export-menu a:hover {
    color: #009ace !important;
    cursor: pointer;
}

.max_scene_edit_btn {
    display: none;
    position: absolute;
    width: 20px;
    height: 20px;
    background-size: 12px 12px;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 50%;
    background-color: white;
    z-index: 13;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.max_scene_edit {
    background-image: url(../../images/edit.svg);
    top: -15px;
    right: -15px;
}

.project-item__progress-total {
    position: relative;
}
.project-item__progress-total:hover .max_scene_edit {
    display: block;
}

.button-staff {
    background-image: url(../../images/icon_info.svg);
    width: 20px;
    height: 20px;
    background-size: 20px;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 50%;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    background-color: white;
}

.button-staff:hover {
    background-image: url(../../images/icon_info_hover.svg);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.15);
}

.project-video-item .right .video-comment-seen {
    left: 0;
}

.project-video-item .video-comment-seen {
    left: auto;
    right: 0;
}

/* .video-pin-time span {
    left: 20%;
    top: 0
} */

.project-item-right-action {
    margin: 0 0 0 auto;
}

.project-item__filter-item {
    border-radius: 20px;
    width: auto;
    min-width: 50px;
    padding: 0;
}

.project-item__filter-item.active {
  background-color: transparent;
  color: #0076a5;
    filter: brightness(.8);
  box-shadow: none;
}

.main-button {
    top: calc(50% - 20px);
    left: calc(50% - 20px);
    right: auto;
    position: absolute;
    color: white;
    width: 40px;
    height: 40px;
    border: 1px solid white;
    border-radius: 50%;
}

.main-button:before {
    content: '';
    height: 22px;
    width: 22px;
    background-size: 22px;
    background-position: center;
    background-repeat: no-repeat;
    position: absolute;
    top: 8px;
    left: 8px;
}

.upload_production_btn.main-button:before {
    background-image: url(../../images/icon-upload.svg);
    filter: invert(99%) sepia(99%) saturate(0%) hue-rotate(301deg) brightness(101%) contrast(100%);
}

.download_production_btn.main-button:before {
    background-image: url(../../images/icon-download-2.svg);
}

.comment_btn.main-button:before {
    background-image: url(../../images/icon-comment.svg);
    filter: invert(99%) sepia(99%) saturate(0%) hue-rotate(301deg) brightness(101%) contrast(100%);
}

.main-button:hover:before {
    filter: invert(51%) sepia(76%) saturate(4279%) hue-rotate(163deg) brightness(95%) contrast(101%);
}


.overlay {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

.uploading_production {
    background: rgba(167, 168, 169, 0.7);
}

.uploaded_production {
    background: rgba(51, 51, 51, 0.8);
}

.project-delivery-item-content:hover .main-button,
.project-delivery-item-content:hover .overlay,
.project-chapter-video-item-content:hover .main-button,
.project-chapter-video-item-content:hover .overlay,
.project-chapter-video-item-content:hover .scenes__rate {
    display: none;
}

.video-pin-time.gray {
    color: #53565A;
}

.video-pin-time.gray span {
    color: #53565A;
}

.product-scene-list-header .button-switch .switch:checked:before {
    background-color: #53565a;
}

.product-scene-list-header .form-group label,
.product-scene-list-header .sumo-select .SumoSelect > .CaptionCont > span,
.product-scene-list-header .asc.active, .desc.active {
    color: #53565a;
}

.video-menu {
    margin-top: -5px;
    opacity: 0;
    height: 0;
    transition: none;
}

.project-chapter-item .video-menu {
    margin-top: 0;
}

.video-menu .menu-button {
    padding: 8px 4px;
    display: flex;
    align-items: center;
    justify-content: left;
    opacity: 0;
    height: 0;
    visibility: hidden;
}

.video-menu .menu-button:hover {
    background: rgba(167, 168, 169, 0.2);
    cursor: pointer;
}

.video-menu .menu-button .left-icon {
    height: 25px;
    width: 25px;
    background-repeat: no-repeat;
    background-size: 20px;
    background-position: center;
}

.video-menu .menu-button span {
    line-height: 1em;
    font-size: .8em;
    margin: 0 0 0 4px;
    color: #53565a;
}

.video-menu .menu-button:hover span {
    color: #0076a5;
}

.video-menu .menu-button.download_production_btn .left-icon {
    background-image: url("../../images/icon-download-3.svg");
}

.video-menu .menu-button:hover.download_production_btn .left-icon {
    background-image: url("../../images/icon-download.svg");
}

.video-menu .menu-button.upload_production_btn .left-icon {
    background-image: url("../../images/icon-upload-g.svg");
}

.video-menu .menu-button:hover.upload_production_btn .left-icon {
    background-image: url("../../images/icon-upload-b.svg");
}

.video-menu .menu-button.project-chapter-video-comment .left-icon {
    background-image: url("../../images/icon-comment-g.svg");
}

.video-menu .menu-button:hover.project-chapter-video-comment .left-icon {
    background-image: url("../../images/icon-comment-b.svg");
}

.video-menu .menu-button.project-chapter-video-undone .left-icon {
    background-image: url("../../images/icon-undone-g.svg");
}

.video-menu .menu-button:hover.project-chapter-video-undone .left-icon {
    background-image: url("../../images/icon-undone-blue.svg");
}

.video-menu .menu-button.project-chapter-video-done .left-icon {
    background-image: url("../../images/icon-heart-undone.svg");
}

.video-menu .menu-button:hover.project-chapter-video-done .left-icon {
    background-image: url("../../images/icon-heart-done.svg");
}

.video-menu .menu-button.project-chapter-video-like .left-icon {
    background-image: url("../../images/icon-like-g.svg");
}

.video-menu .menu-button:hover.project-chapter-video-like .left-icon {
    background-image: url("../../images/icon-like-b.svg");
}

.video-menu .menu-button.project-chapter-video-dislike .left-icon {
    background-image: url("../../images/icon-dislike-g.svg");
}

.video-menu .menu-button:hover.project-chapter-video-dislike .left-icon {
    background-image: url("../../images/icon-dislike-b.svg");
}


.video-menu hr {
    margin: 0 10px;
    border: none;
    background: #ffffff;
    height: 1px;
}

.bottom-menu {
    background: #ffffff;
    position: absolute;
    width: 100%;
    z-index: 2;
}

.bottom-menu .menu-heading {
    margin: 2px;
    display: flex;
    align-items: center;
    justify-content: left;
}

.bottom-menu .menu-heading .left-icon {
    height: 30px;
    width: 30px;
    padding-left: 10px;
    background-size: 25px;
    background-position: center;
    background-repeat: no-repeat;
    cursor: pointer;
}

.bottom-menu .menu-heading .project-chapter-video-undone.left-icon{
    background-image: url('../../images/icon-heart-done.svg');
}

.bottom-menu .menu-heading .project-chapter-video-undone.left-icon:hover {
    background-image: url("../../images/icon-heart-hover.svg");
}

.bottom-menu .menu-heading .project-chapter-video-done.left-icon,
.bottom-menu .menu-heading .video-done.left-icon{
    background-image: url('../../images/icon-heart-hover.svg');
}

.bottom-menu .menu-heading .project-chapter-video-done.left-icon:hover {
    background-image: url("../../images/icon-heart-done.svg");
}

.bottom-menu .menu-heading .download_production_btn.left-icon{
    background-image: url('../../images/icon-download.svg');
}

.bottom-menu .menu-heading .download_production_btn.left-icon:hover {
    filter: brightness(.8);
}



.bottom-menu .menu-heading .project-chapter-video-scence {
    margin-left: 5px;
}

.bottom-menu .menu-heading .project-chapter-video-scence p {
    margin: 0;
    font-size: .7em;
    color: #a7a8a9;
    overflow: hidden;
}

.scene-filter {
    position: absolute;
    right: calc(120px - 1%);
    height: 45px;
    width: 45px;
    background-image: url('../../images/icon-heart-undone.svg');
    background-size: 30px;
    background-position: center;
    background-repeat: no-repeat;
    cursor: pointer;
}

@media screen and (max-width: 400px) {
    .scene-filter {
        right: calc(120px - 10%);
    }
}

.scene-filter.filter-all {
    background-image: url('../../images/icon-heart-undone.svg');
}

.scene-filter.filter-done {
    background-image: url('../../images/icon-heart-done.svg');
}

.scene-filter.filter-notdone {
    background-image: url('../../images/icon-heart-hover.svg');
}

.name-gray {
    color: #a7a8a9;
}

.video-product-scene-name {
    font-weight: bold;
}

.video-variation-name {
    color: #53565a;
}

.dragging {
    cursor: grabbing;
}

.dragging .project-item__info {
    background-color: #00000050;
    background-blend-mode: color;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
    transform: translate(0px, -5px);
}
