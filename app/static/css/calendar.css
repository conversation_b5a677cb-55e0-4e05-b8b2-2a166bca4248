.mcalendar {
    font-size: 11px;
    border-radius: 6px;
    /* stylelint-disable */
    /* stylelint-enable */
}

.mcalendar .table-condensed {
    background-color: #fff;
    border-radius: 6px;
    overflow: hidden;
}

.datepicker {
    cursor: pointer;
}

.mcalendar .datepicker-inline .icon {
    font-size: 13px;
    box-shadow: 0 7px 24px rgba(0, 0, 0, 0.07);
    border-radius: 6px;
    display: inline-block;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
}

.mcalendar .datepicker-inline .next {
    padding-right: 12px;
}

.mcalendar .datepicker-inline .prev {
    padding-left: 12px;
}

.mcalendar .datepicker-inline .next,
.mcalendar .datepicker-inline .prev {
    padding-top: 10px;
}

.mcalendar .datepicker-inline .next:before,
.mcalendar .datepicker-inline .prev:before {
    display: none;
}

.mcalendar .datepicker-inline .next:hover,
.mcalendar .datepicker-inline .prev:hover {
    cursor: pointer;
}

.mcalendar .datepicker-inline .datepicker-switch {
    font-size: 13px;
    padding-top: 10px;
}

.mcalendar .datepicker-inline .day {
    background: none !important;
    /* stylelint-disable-line */
    position: relative;
    z-index: 1;
}

.mcalendar .datepicker-inline .day:before {
    content: '';
    position: absolute;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: -1;
}

.mcalendar .datepicker-inline .day:not(.disabled):hover {
    cursor: pointer;
}

.mcalendar .datepicker-inline .dow {
    border-bottom: none;
    color: #a7a8a9;
    padding: 14px;
}

.mcalendar .datepicker-inline .old,
.mcalendar .datepicker-inline .new {
    color: #f0f0f0;
}

.mcalendar .datepicker-inline .day.today {
    color: #fff !important;
}

.mcalendar .datepicker-inline .day.today:before {
    background-color: #53565a;
}

.mcalendar .datepicker-inline .day.disabled {
    color: #D3D3D3 !important;
}

.mcalendar .datepicker-inline .day.active {
    color: #fff;
}

.mcalendar .datepicker-inline .day.active:before {
    content: '';
    position: absolute;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #009ace;
    border: none;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: -1;
}

.mcalendar .datepicker-inline .day.active:after {
    display: none;
}

.mcalendar .datepicker-inline .day.active-deadline {
    color: #009ace !important;
}

.mcalendar .datepicker-inline .day.active-deadline.active {
    color: #fff !important;
}

.mcalendar tbody tr td:nth-child(7) {
    color: #a7a8a9 !important;
    /* stylelint-disable-line */
}

.mcalendar tfoot {
    display: none;
}

.mcalendar--small .datepicker-inline .dow {
    padding: 8px;
}

@media (max-width: 992px) {
    .mcalendar--small .datepicker-inline .dow {
        padding: 8px;
    }
}

.mcalendar--small .datepicker .table-condensed > tbody > tr > td {
    padding: 8px;
}

@media (max-width: 992px) {
    .mcalendar--small .datepicker .table-condensed > tbody > tr > td {
        padding: 8px;
    }
}

.mcalendar--black .table-condensed {
    background-color: rgba(255, 255, 255, 0.1);
}

.mcalendar--black thead {
    background-color: rgba(255, 255, 255, 0.1);
}

.mcalendar--black .datepicker-inline .icon {
    background-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 7px 24px rgba(255, 255, 255, 0.07);
    color: #a7a8a9;
}

.mcalendar--black .datepicker-inline .prev {
    visibility: visible !important;
}

.mcalendar--black .datepicker-inline .datepicker-switch {
    color: #fff;
}

.mcalendar--black .datepicker-inline .day {
    color: #fff;
}

.mcalendar--black .datepicker-inline .dow {
    color: #d3d3d3;
}

.mcalendar--black .datepicker-inline .day.old,
.mcalendar--black .datepicker-inline .day.new {
    color: #53565a;
}

.mcalendar--black .datepicker-inline .day.today {
    color: #000;
}

.mcalendar--black .datepicker-inline .day.today:before {
    background-color: #fff;
}

.mcalendar--black .datepicker-inline .day.disabled {
    color: #53565a;
}

.mcalendar--black .datepicker-inline .day.active {
    color: #fff;
}

.mcalendar--black .datepicker-inline .day.active:before {
    content: '';
    position: absolute;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #009ace;
    border: none;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: -1;
}

.mcalendar--black .datepicker-inline .day.active:after {
    display: none;
}

.mcalendar--black .datepicker-inline .day.active-deadline {
    color: #009ace;
}

.mcalendar--black .datepicker-inline .day.active-deadline.active {
    color: #fff;
}

.mcalendar--black tbody tr td:nth-child(7) {
    color: #53565a !important;
}

.datepicker-dropdown {
    border: none;
    border-radius: 6px;
    box-shadow: 0 7px 64px rgba(0, 0, 0, 0.07);
    padding: 10px 16px;
    font-size: 11px;
}

.datepicker-dropdown.datepicker-orient-left .day.active {
    color: #fff;
}

.datepicker-dropdown.datepicker-orient-left .day.active:before {
    background-color: #009ace;
    border: 2px solid #009ace;
    z-index: -1;
    width: 32px;
    height: 32px;
}

.datepicker-dropdown .datepicker-switch {
    font-size: 13px;
}

.datepicker-dropdown .prev:before {
    background-image: none !important;
    content: "" !important;
    font-family: 'soremoicons';
    font-size: 16px;
}

.datepicker-dropdown .next:before {
    background-image: none !important;
    content: "" !important;
    font-family: 'soremoicons';
    font-size: 16px;
}

.busy-day {
    color: #afbac8 !important;
}
