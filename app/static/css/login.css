.valid_mail ul li {
    list-style: none;
}

.valid_mail ul {
    padding: 0;
}

.checkbox input[type=checkbox] {
    margin-left: 0;
}

.banner {
    margin-bottom: 0 !important;
}

.auth {
    position: absolute;
    width: 100vw;
    height: 100vh;
    top: 0;
}

.auth__main {
    position: relative;
    width: clamp(320px, 100% - 32px, 360px);
    display: flex;
    justify-content: center;
}

.auth__form {
    width: 100%;
    background: rgba(255, 255, 255, 0.618);
    border-radius: 12px;
    padding: 32px 12px;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(13px);    
}

.join-form__social-list {
    margin: 0 !important;
    padding-bottom: 0 !important;
    border-bottom: none !important;
}

.auth__form-forgotpass {
    color: #000000 !important;
    font-size: 11px;
}

.auth__form-register, .auth__form-login {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 13px;
    line-height: 100%;

    color: #000000;

    a {
        color: #000000;
    }

    a:hover {
        color: #009ace;}
}

.auth__form-note {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-size: 13px;
    line-height: 200%;

    color: #000000;

    a {
        color: #009ace;
    }

    a:hover {
        color: #0076a5;
    }
}




.auth__form-forgotpass a, .auth__form-register a {
    color: #000000 !important;
}

.auth__form-forgotpass a:hover, .auth__form-register a:hover {
    color: #009ace !important;
}
.form-group .input-box {
    border: 1px solid #F0F0F0 !important;
    background-color:  rgba(255, 255, 255, 0.618) !important;
    color: #000000;
    padding: 12px 12px !important;
    border-radius: 4px !important;
    box-shadow: none;
    -webkit-appearance: none;
    appearance: none;
}
.form-group .checkbox input[type='checkbox']:before {
    border: 1px solid #f0f0f0 !important;
    border-radius: 4px;
    box-sizing: border-box;
    background-color:  rgba(255, 255, 255, 0.618) !important;
}

.form-group .checkbox input[type='checkbox']:checked:after {
    border: 1px solid #009ace;
    border-radius: 4px;
    border-width: 0 4px 4px 0;
}

.auth__form-button {
    width: 100% !important;
    background-color: #009ace !important;
    color: #ffffff !important;
    padding: 16px 12px !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border-radius: 4px !important;
    font-family: 'A+mfCv-AXISラウンド 50 M StdN' !important;
    font-size: 18px !important;
    letter-spacing: 2.5px;
}

.auth__form-button:hover {
    background-color: #0076a5 !important;
}

.form-group {
    margin-bottom: 0 !important;
}

.form-bottom {
    margin-bottom: 0 !important;
}

.btn--disabled {
    background: #F0F0F0 !important;
    color: #A7A8A9 !important;
    box-shadow: none;
}

.error-messager, .errorlist {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 11px;
    line-height: 150%;
    color: #2CC84D !important;
    margin-bottom: 0;
    word-break: break-word;
    display: flex;
}

.checkbox label {
    color: #000000;
    font-style: normal;
    font-weight: normal !important;
    font-size: 13px;
    line-height: 150%;
}

.join-form__social-title {
    font-style: normal;
    font-weight: 300;
    font-size: 11px;
    line-height: 150%;
    color: #000000;
    text-align: center;
}

.signin-title {
    font-size: 24px;
    line-height: 150%;
    color: #000000;
}

.auth__form-title {
    font-style: normal;
    margin: 0px 0px 24px 0px !important;
    border-bottom: none !important;
    font-size: 24px;
    line-height: 150%;
    color: #000000;
}


.account__field-hint {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';;
    font-size: 11px;
    line-height: 17px;
    color: #000000;
    font-weight: 300;
    display: block;
    margin-bottom: 8px;
}


hr {
    margin: 16px 0 8px;
    border: 0;
    border-top: 1px solid #F0F0F0;
}