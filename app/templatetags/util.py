from doctest import master
from ntpath import join
import re
import json
import requests
from dateutil.parser import parse
from decimal import Decimal, ROUND_HALF_UP
from xml.etree.ElementTree import tostring

from numpy import prod

from accounts.services import UserDeleteableService
import datetime

from django import template
from django.db.models import Q, Prefetch, Count, Sum, Case, When, QuerySet
from django.urls import reverse_lazy
from django.utils.safestring import mark_safe
from django.conf import settings
from pydub import AudioSegment
from PIL import Image
from io import BytesIO
import imageio
import base64
from pdf2image import convert_from_bytes
from PyPDF2 import PdfReader
from accounts.models import ProductUser, AuthUser, CreatorListCreator, Creator, ItemBlock, Skill
from app import models
from mileages.services import count_artist_in_rank
from app.app_services import upload_contract_services
from voice.constants import FORMAT_DATE, FORMAT_SHORT_DATE, CONST_REGEX_FILE, FORMAT_TIME
from app.models import FormContractAndPlan, OfferProject, Scene, ProductScene, SceneComment, Product, SceneTitle, OfferProduct, PreviewProductComment, \
    ProductComment, MessageReceiver, ProductMessage, OfferCreator, SceneCommentFile, ProductCommentFile, \
    RatingSceneTitle, SceneCommentFolder, ProductCommentFolder, MessageFolder, MessageFile, DownloadedProductComment, \
    ProductMessageFolder, ProductMessageFile, OfferUser, SaleContentSelection, SaleContent, OfferMessageReceiver, ReviewOffer
import random
import logging
register = template.Library()

ROLE_DEFAULT_AVATAR = {
    AuthUser.CREATOR: '/static/images/default-avatar-creator.png',
    AuthUser.MASTERADMIN: '/static/images/default-avatar-master-admin.png',
    AuthUser.MASTERCLIENT: '/static/images/default-avatar-client.png',
}
DEFAULT_AVATAR = '/static/images/default-avt.png'
DEFAULT_OWNER_AVATAR = '/static/images/default-avatar-owner.png'
DEFAULT_ALBUM_BACKGROUND = '/static/images/'

def div_amari(x, y):
    return x % y


register.filter('div_amari', div_amari)


def check_redirect_contact(user, user_creator):
    if AuthUser.get_artist_landing_page().exists() and user_creator.user_creator.first() and \
        user_creator.user_creator.first().user.pk == AuthUser.get_artist_landing_page().first().pk:
        if user and user.is_authenticated.value:
            return 0
        return 1
    return 0


register.filter('check_redirect_contact', check_redirect_contact)

def check_redirect_contact_boolean(user, user_creator):
    if AuthUser.get_artist_landing_page().exists() and user_creator.user_creator.first() and \
        user_creator.user_creator.first().user.pk == AuthUser.get_artist_landing_page().first().pk:
        if user and user.is_authenticated.value:
            return False
        return True
    return False


register.filter('check_redirect_contact_boolean', check_redirect_contact_boolean)


def check_master_producer_is_master_admin(project):
    master_producer = project.get_master_producer_is_artist()
    check_offer = project.product_offers.exists()
    check_owner = project.productuser_set.filter(position=ProductUser.OWNER).exists()
    offer_product = project.offer_product.first()
    status_contract = offer_product.condition
    if master_producer:
        if not offer_product.is_not_approved():
            return False
        if not check_owner:
            return False
        return 'is_producer'
    return 'is_admin'

register.filter('check_master_producer_is_master_admin', check_master_producer_is_master_admin)

def get_usage_fee(project):
    master_producer = project.get_master_producer_is_artist()
    if master_producer is not None:
        return str(master_producer.user.get_usage_fee_for_user())
    return '0'

register.filter('get_usage_fee', get_usage_fee)

def calculate_usage_fee(project):
    offer_product = project.offer_product.first()
    if offer_product.files.first():
        form_contract = get_form_plan_contract(project)
        budget_from_contract = upload_contract_services.get_budget_in_form(form_contract)
        return budget_from_contract/100*float(get_usage_fee(project))
    return 0

register.filter('calculate_usage_fee', calculate_usage_fee)

def get_form_plan_contract(project):
    offer_product = project.offer_product.first()
    form_return = None
    form_contract = offer_product.form_contract_and_plans.filter(form_type__in=[FormContractAndPlan.CONTRACT_TYPE, FormContractAndPlan.BOTH_TYPE]).order_by('-created', '-pk')
    if form_contract.exists():
        form_return = form_contract.first()
        # return
    else:
        form_return = offer_product.form_contract_and_plans.filter(form_type=FormContractAndPlan.PLAN_TYPE).order_by('-created', '-pk').first()
    return form_return

register.filter('get_form_plan_contract', get_form_plan_contract)

def check_show_project_payment_admin(project):
    check_offer = project.product_offers.exists()
    check_owner = project.productuser_set.filter(position=ProductUser.OWNER, user__is_active=True).exists()
    master_producer = project.get_master_producer_is_artist()
    offer_product = project.offer_product.first()
    
    if master_producer is not None:
        if offer_product and            offer_product.is_not_approved() and not check_offer:
            return False
        return True
    else:
        if check_owner:
            return True
        else:
            if check_offer:
                return True
    return False

register.filter('check_show_project_payment_admin', check_show_project_payment_admin)

def get_owner_payment(project):
    owner = project.productuser_set.filter(position=ProductUser.OWNER, user__is_active=True).order_by('order_user').first().user
    if not owner:
        return
    return owner

register.filter('get_owner_payment', get_owner_payment)

def get_master_producer_payment(project):
    return project.get_master_producer_project().user

register.filter('get_master_producer_payment', get_master_producer_payment)

def get_scene_payment(project, type):
    form_contract = get_form_plan_contract(project)
    if type == 'name':
        if form_contract:
            return form_contract.subject
        return ''
    return f'利用料{get_usage_fee(project)}%'

register.filter('get_scene_payment', get_scene_payment)

def get_sum_profit(project, type_sum):
    master_producer = project.get_master_producer_is_artist()
    id_master_producer = master_producer.user.pk if master_producer else 0

    offer_creators_reward = project.product_offers.filter(Q(admin__role=AuthUser.MASTERADMIN) | Q(admin__id=id_master_producer)).values_list('reward', flat=True)
    budget = project.total_budget
    offer_product = project.offer_product.first()

    sum_count = sum(offer_creators_reward)
    if type_sum == 'offer':
        if offer_product and offer_product.is_not_approved():
            return display_currency(get_budget_from_contract(project))
        return display_currency(budget)
    if offer_product and offer_product.is_not_approved():
        budget_from_contract = get_budget_from_contract(project)
        if master_producer is not None:
            budget_from_contract = budget_from_contract - budget_from_contract * float(get_usage_fee(project))/100
        return display_currency(budget_from_contract - sum_count)
    return display_currency(budget - sum_count)

register.filter('get_sum_profit', get_sum_profit)

def get_budget_from_contract(project):
    form_contract = get_form_plan_contract(project)
    if not form_contract:
        return 0
    return upload_contract_services.get_budget_in_form(form_contract)

register.filter('get_budget_from_contract', get_budget_from_contract)

def get_accept_date(project):
    offer_product = project.offer_product.first()
    form_contract_and_plan = get_form_plan_contract(project)
    if offer_product and not offer_product.is_not_approved() and form_contract_and_plan:
        return form_contract_and_plan.modified
    return
register.filter('get_accept_date', get_accept_date)

def get_done_date(project):
    offer_product = project.offer_product.first()
    if offer_product and offer_product.condition not in ['1', '2', '3', '8', '9']:
        return offer_product.modified
    return ''
register.filter('get_done_date', get_done_date)


def get_comment_form(scene, user_id):
    return scene.get_comment_form(user_id)


register.filter('get_comment_form', get_comment_form)


def get_scene_ones(scene_title, scene_ones_param):
    tag = scene_ones_param[0]
    product_id = scene_ones_param[1]
    product_scene_id = scene_ones_param[2]
    return scene_title.get_scene_ones_params(tag, product_id, product_scene_id)


register.filter('get_scene_ones', get_scene_ones)


def filter_scense(value):
    scenes = value.order_by('-created')
    list_title = []
    list_scenes = []
    for scene in scenes:
        if not scene.title:
            continue

        if scene.title.title_id in list_title:
            # get last item in list scene title
            # remove if exist
            index = list_title.index(scene.title.title_id)
            if list_scenes[index].order <= scene.order:
                list_scenes.pop(index)
                list_title.pop(index)
                list_title.append(scene.title.title_id)
                list_scenes.append(scene)
        else:
            list_title.append(scene.title.title_id)
            list_scenes.append(scene)

    return list_scenes


register.filter('filter_scense', filter_scense)


def get_class_scene_by_with(scense):
    if scense.video_width is None or scense.video_height is None:
        return 'lg'

    ratio = calculate_ratio(scense.video_width, scense.video_height)

    if ratio == '16:9':
        return 'md'

    if ratio == '1:2':
        return 'sm'

    if ratio == '4:3':
        return 'lg'

    return 'md'


def get_class_video_by_with(scene):
    video_width = scene.video_width
    video_height = scene.video_height
    if not video_width or not video_height:
        return 'cscene__not-radio cscene__ratio-43'
    video_width = int(video_width)
    video_height = int(video_height)
    ratio = float(video_width) / float(video_height)

    if ratio < 16 / 9:
        return 'cscene__ratio-35'

    return 'cscene__ratio-43'


def get_class_border_video_by_with(scene):
    video_width = scene.video_width
    video_height = scene.video_height

    if not video_width or not video_height:
        return 'cscene__version-horizontal'
    video_width = int(video_width)
    video_height = int(video_height)
    ratio = float(video_width) / float(video_height)

    if ratio < 16 / 9:
        return 'cscene__version-vertical'

    return 'cscene__version-horizontal'


def get_width_for_thumbnail(scene):
    calculated_width = 185.24
    if scene.video_height and scene.video_width:
        calculated_width = round(104.2 * scene.video_width / scene.video_height, 2)
    return calculated_width


def calculate_ratio(width: int, height: int) -> str:
    temp = 0

    def gcd(a, b):
        return a if b == 0 else gcd(b, a % b)

    if width and height:
        if width < height:
            temp = width
            width = height
            height = temp
        divisor = gcd(width, height)
        if divisor:
            w = int(width / divisor) if not temp else int(height / divisor)
            h = int(height / divisor) if not temp else int(width / divisor)
            return f"{w}:{h}"
    return f"{width}:{height}"


register.filter('calculate_ratio', calculate_ratio)
register.filter('get_class_scene_by_with', get_class_scene_by_with)


def round_rating(rating):
    return round(rating)


def make_list(n):
    return list(range(1, n+1))


def pin_time_comment(comment, pin_time) -> str:
    if not pin_time or not comment:
        return comment

    comment = comment.replace(pin_time, '', 1)
    return remove_start_new_line(comment)


def remove_start_new_line(string) -> str:
    if string.startswith('\r\n'):
        return string[2:]
    if string.startswith('\n') or string.endswith('\r'):
        return string[1:]
    return mark_safe(string)


register.filter('pin_time_comment', pin_time_comment)


def check_scene_new(scene, user_id):
    latest_scene = models.Scene.objects.filter(title_id=scene.title_id).order_by('-created').first()
    preview = models.PreviewVideo.objects.filter(scene_id=latest_scene.scene_id, owner_id=user_id)
    return False if preview else True


register.filter('check_scene_new', check_scene_new)


def order_by(preview_comment_list, value):
    return preview_comment_list.order_by(value)


register.filter('order_by', order_by)


@register.simple_tag
def define(val=None):
    return val


def check_rating(scene):
    rating = []
    scenes = models.Scene.objects.filter(product_id=scene.product_id, title_id=scene.title_id,
                                         product_scene_id=scene.title.product_scene_id,
                                         version__isnull=True)
    rating_scene = models.RatingScene.objects.filter(scene__in=scenes)
    if rating_scene:
        rating_avg = 0
        for r in rating_scene:
            rating_avg += r.rating
        rating = range(0, rating_avg // rating_scene.count())
    return rating


register.filter('check_rating', check_rating)


def check_is_rating(user):
    return True if user.productuser_set.first().is_rating else False


register.filter('check_is_rating', check_is_rating)


def check_is_favorite(user):
    return True if user.productuser_set.first().is_favorite else False


register.filter('check_is_favorite', check_is_favorite)


def filter_user_in_project(user_list, product_id):
    user_list = user_list.filter(products=product_id)

    return user_list


register.filter('filter_user_in_project', filter_user_in_project)

def check_owner_product(admins, current_user):
    if not isinstance(admins, QuerySet):
        return []
    master_producer = admins.first()
    check_master_client = current_user.role
    if master_producer and check_master_client == AuthUser.MASTERCLIENT:
        return admins.filter(Q(is_active=True) &
                                            (Q(productuser__position=ProductUser.MASTERADMIN, pk=master_producer.pk) |
                                            Q(productuser__position__in=[ProductUser.PRODUCER, ProductUser.DIRECTOR]))).distinct()
    return admins


register.filter('check_owner_product', check_owner_product)


def filter_user_not_in_project(user_list, product_id):
    user_list = user_list.filter(~Q(products=product_id))

    return user_list


register.filter('filter_user_not_in_project', filter_user_not_in_project)


def product_scene_title(product, scenes):
    if not scenes:
        return
    product_scene = Scene.objects.filter(scene_id__in=scenes, product_id=product,
                                         product_scene__isnull=False).values('product_scene')
    list_product_scene = [str(item['product_scene']) for item in product_scene]
    return ProductScene.objects.filter(product_scene_id__in=list_product_scene)


def filter_scense_act(product_scene, scenes):
    if not scenes:
        return
    scenes = Scene.objects.filter(scene_id__in=scenes, product_scene_id=product_scene).distinct()
    title_list = scenes.order_by('-title__modified').values('title_id').distinct()
    for s in scenes:
        if s.product_scene == product_scene:
            all_scene_list = Scene.objects.filter(Q(product_scene_id=product_scene),
                                                  ~Q(title_id=None), Q(product_id=s.product_id)).distinct()
        scene_list = get_scene_list(title_list, all_scene_list=all_scene_list)

    return scene_list


def get_scene_list(title_list, all_scene_list):
    scene_list = list()
    for title in title_list:
        all_scene = all_scene_list.filter(title_id=title['title_id'])
        top_scene = all_scene.filter(version=None).order_by('order').last()
        if top_scene and top_scene.check_tag() == '2':
            if top_scene.tag == '2':
                top_last_scene = top_scene
            else:
                top_last_scene = Scene.objects.filter(Q(version=top_scene.pk), Q(tag='2')).first()
        else:
            top_last_scene = Scene.objects.filter(version=top_scene.pk).order_by('-created').first()
            if not top_last_scene:
                top_last_scene = top_scene
        scene_list.append(top_last_scene)

    return scene_list


def filter_user(scenes, user_exclude_list):
    time_ago = datetime.datetime.now() - datetime.timedelta(days=1)
    data = []
    for scene in scenes:
        scene_ref = Scene.objects.filter(product=scene.product, title=scene.title, product_scene=scene.title.product_scene)
        item = {
            'title': scene.title.title if scene.title else '',
            'title_id': scene.title.pk if scene.title else '',
            'thumbnail': scene.thumbnail,
            'modified': scene.title.modified_str if scene.title else '',
            'check_new': True if scene_ref.filter(created__gt=time_ago).exclude(
                owner_id__in=user_exclude_list) else False,
            'count_comment': SceneComment.objects.filter(
                Q(scene_id__in=scene_ref) & Q(created__gt=time_ago) & ~Q(owner_id__in=user_exclude_list)).count()
        }
        data.append(item)

    return data


def check_product(product, user_exclude_list):
    time_ago = datetime.datetime.now() - datetime.timedelta(days=1)
    scenes_create = Scene.objects.filter(
        Q(created__gt=time_ago) & Q(product_id=product.product_id) & ~Q(owner_id__in=user_exclude_list))
    scenes_comment = Scene.objects.filter(Q(modified__gt=time_ago) & Q(product_id=product.product_id))

    if scenes_create:
        return product
    elif scenes_comment:
        comments = SceneComment.objects.filter(
            Q(created__gt=time_ago) & ~Q(owner_id__in=user_exclude_list) & Q(scene_id__in=scenes_comment))
        if comments:
            return product


def get_title_mail(products, user_exclude_list):
    time_ago = datetime.datetime.now() - datetime.timedelta(days=1)
    scenes = Scene.objects.filter(
        Q(created__gt=time_ago) & Q(product_id__in=products) & ~Q(owner_id__in=user_exclude_list))
    scenes_comment = Scene.objects.filter(Q(modified__gt=time_ago) & Q(product_id__in=products))
    if scenes_comment:
        comments = SceneComment.objects.filter(
            Q(created__gt=time_ago) & ~Q(owner_id__in=user_exclude_list) & Q(scene_id__in=scenes_comment))

    title = '新しいお知らせ'
    if len(scenes) > 0 and not comments:
        title = '新しい演出が届いています。'
    elif comments and len(comments) > 0 and len(scenes) == 0:
        title = '新しいメッセージが届いています。'
    return title


def filter_user_quarter(scenes, user_exclude_list):
    time_ago = datetime.datetime.now() - datetime.timedelta(minutes=1)
    data = []
    for scene in scenes:
        scene_ref = Scene.objects.filter(product=scene.product, title=scene.title, product_scene=scene.title.product_scene)
        item = {
            'title': scene.title.title if scene.title else '',
            'title_id': scene.title.pk if scene.title else '',
            'thumbnail': scene.thumbnail,
            'modified': scene.title.modified_str if scene.title else '',
            'check_new': True if scene_ref.filter(created__gt=time_ago).exclude(
                owner_id__in=user_exclude_list) else False,
            'count_comment': SceneComment.objects.filter(
                Q(scene_id__in=scene_ref) & Q(created__gt=time_ago) & ~Q(owner_id__in=user_exclude_list)).count()
        }
        data.append(item)

    return data


def check_product_quarter(product, user_exclude_list):
    time_ago = datetime.datetime.now() - datetime.timedelta(minutes=15)
    scenes_create = Scene.objects.filter(
        Q(created__gt=time_ago) & Q(product_id=product.product_id) & ~Q(owner_id__in=user_exclude_list))
    scenes_comment = Scene.objects.filter(Q(modified__gt=time_ago) & Q(product_id=product.product_id))

    if scenes_create:
        return product
    elif scenes_comment:
        comments = SceneComment.objects.filter(
            Q(created__gt=time_ago) & ~Q(owner_id__in=user_exclude_list) & Q(scene_id__in=scenes_comment))
        if comments:
            return product


def get_title_mail_quarter(products, user_exclude_list):
    time_ago = datetime.datetime.now() - datetime.timedelta(minutes=1)
    scenes = Scene.objects.filter(
        Q(created__gt=time_ago) & Q(product_id__in=products) & ~Q(owner_id__in=user_exclude_list))
    scenes_comment = Scene.objects.filter(Q(modified__gt=time_ago) & Q(product_id__in=products))
    comments = []
    if scenes_comment:
        comments = SceneComment.objects.filter(
            Q(created__gt=time_ago) & ~Q(owner_id__in=user_exclude_list) & Q(scene_id__in=scenes_comment))

    title = '新しいお知らせ'
    if len(scenes) > 0 and not comments:
        title = '新しい演出が届いています。'
    elif len(scenes) == 0 and comments:
        title = '新しいメッセージが届いています。'
    return title


def check_project_status(product, user):
    pu = ProductUser.objects.filter(product=product, user=user)
    if product.is_active:
        if product.max_scene == product.current_heart:
            return 'fix'
        elif pu and pu[0].has_new_video and product.current_scene > 0:
            return 'new'
        else:
            return 'all'
    else:
        return 'order'


def get_weekday(date):
    date1 = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    date2 = date.replace(hour=0, minute=0, second=0, microsecond=0)
    if (date1 - date2).days < 7:
        return date.strftime('%a')
    else:
        return get_updated_time(date)

def get_weekday_new(date):
    date1 = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    date2 = date.replace(hour=0, minute=0, second=0, microsecond=0)
    if (date1 - date2).days < 7:
        if date1.day == date2.day and date1.month == date2.month and date1.year == date2.year:
            return date.strftime('%H:%M')
        return date.strftime('%a')
    else:
        return get_updated_time(date)


def get_weekday_new_2(date):
    if not date:
        return ''
    return date.strftime('%y/%m/%d')


def get_avatar(user, avatar_type=None, host=''):
    default_avt = DEFAULT_OWNER_AVATAR
    if type(user) == int:
        user = AuthUser.objects.filter(pk__in=[user]).first()
    try:
        if avatar_type and getattr(user, f'{avatar_type}_avatar'):
            return getattr(user, f'{avatar_type}_avatar').url

        if user.avatar:
            return user.avatar.url
        return f'{host}{ROLE_DEFAULT_AVATAR.get(user.role, default_avt)}'
    except:
        return f'{host}{default_avt}'


def get_banner_creator(creator_profile, public_profile):
    if creator_profile.banner_resized:
        return creator_profile.banner_resized.url
    if creator_profile.banner:
        return creator_profile.banner.url
    if public_profile and public_profile.banner_resized:
        return public_profile.banner_resized.url
    if public_profile and public_profile.banner:
        return public_profile.banner.url
    return '/static/images/video-item-thumbnail.png'


def get_thumbnail(scene, host=''):
    if scene.thumbnail:
        return scene.thumbnail.url
    if host == 'detail-scene':
        return ''
    elif bool(scene.movie.name):
        type_scene = scene.is_audio_file()
        if type_scene == 'audio':
            return host + '/static/images/audio-item-thumbnail.png'
        elif type_scene == 'document':
            return host + '/static/images/pdf-item-thumbnail.png'
    return host + '/static/images/video-item-thumbnail.png'


def get_updated_time(date):
    if not date:
        return ''
    return date.strftime('%-m/%-d') if date.year == datetime.datetime.now().year else date.strftime('%y/%-m/%-d')


def get_updated_time2(date):
    if not date:
        return ''
    return date.strftime('%Y-%m-%d')


def get_updated_full_time(date):
    if not date:
        return ''
    return date.strftime('%-m/%-d  %H:%M') if date.year == datetime.datetime.now().year else date.strftime(
        '%y/%-m/%-d  %H:%M')


def get_updated_datetime(_datetime):
    if not _datetime:
        return ''
    return _datetime.strftime('%Y-%m-%d %H:%M:%S')


def get_range_date(to_date, from_date):
    if to_date and from_date:
        return f'{get_updated_time(to_date)} - {get_updated_time(from_date)}'
    return 'YY/MM/DD - YY/MM/DD'

def get_range_date_format(to_date, from_date):
    data_date = {}
    if to_date and from_date:
        data_date['to_date'] = get_updated_time2(to_date)
        data_date['from_date'] = get_updated_time2(from_date)
        return data_date
    return None


def to_now(date):
    diff_time = datetime.datetime.now() - date
    diff_day = diff_time.days
    if diff_day == 0:
        diff_hour = round(diff_time.seconds / 60 / 60)
        if diff_hour > 0:
            return str(diff_hour) + '時経過'
        else:
            diff_min = round(diff_time.seconds / 60)
            return str(diff_min) + '分経過'
    else:
        return str(diff_day) + '日経過'


def check_side(comment_user_role, user_role):
    return True if (comment_user_role and user_role) in 'admin, master_admin' else False


def get_image(project, host=''):
    if project.image_resized:
        return project.image_resized.url
    if project.image:
        return project.image.url
    return f'{host}/static/images/ProjectBanner_b.png'


def is_seen(offer_creator, user):
    return not offer_creator.check_offer_is_unread(user)


def new_message_count(offer_creator, user):
    return offer_creator.count_unread_message_in_offer(user)

def new_message_count_refactor(offer):
    if offer.unread_message_count is None:
        return 0
    return offer.unread_message_count

def count_unread_offer_message_count(project, user):
    return project.count_unread_offer_message_admin(user)


def count_unread_message(project, user):
    return OfferMessageReceiver.count_unread_offer(str(project.pk).replace("-",""), str(user.pk))

def get_file_url(offer):
    if offer.file:
        return offer.file.url
    else:
        return 'javascript:void(0)'


def get_name(audio):
    return audio.name if audio.name else 'Unknown'


def get_values_list(queryset, string):
    return list(queryset.order_by("-modified").values_list(string, flat=True))


def get_pin_video(comment):
    if comment.pin_video and comment.pin_video != '':
        return comment.pin_video
    else:
        return comment.scene_id


def get_full_name(user):
    if type(user) == int:
        user = AuthUser.objects.filter(pk__in=[user]).first()
    if user.role == 'creator' and user.creator.first().last_published_version:
        return user.user_creator.first().creator.get_public_stage_name()
    else:
        return user.fullname

def check_user(id, user_id):
    return id == int(user_id)

def get_postion(user):
    if type(user) == int or type(user) == str:
        user = AuthUser.objects.filter(pk__in=[user]).first()
    if user.position:
        return user.position
    return ""

def get_display_fullname(user):
    if type(user) == int or type(user) == str:
        user = AuthUser.objects.filter(pk__in=[user]).first()
    if user.role == AuthUser.MASTERCLIENT and user.display_name:
        return user.display_name
    if user.role == AuthUser.CREATOR:
        if user.stage_name:
            return user.stage_name
        if user.stage_name_en:
            return user.stage_name_en
    return user.fullname

def find_other_user_id(user_id, user_ids):
    if int(user_id) == int(user_ids[0]):
        return user_ids[1]
    return user_ids[0]
    


def hide_if_empty(target):
    return target if target else ''


def count_new_update_scene_title(product, user):
    count = 0
    if user.role == 'admin':
        count = models.Scene.objects.filter(product_scene__isnull=False, version=None, title__in=SceneTitle.objects
                                            .filter(product_scene__in=product.scene_list.all(), status='3')) \
            .values('title_id').annotate(num_of_scene=Count('scene_id')).filter(num_of_scene__gt=0).count()
    elif user.role == 'master_client':
        count = models.Scene.objects\
            .filter(
            schedule_date__lt=datetime.datetime.now(),
            product_scene__isnull=False,
            version=None,
            title__in=SceneTitle.objects \
                .exclude(status__in=['5', '6']) \
                .filter(Q(product_scene__in=product.scene_list.all()) & (Q(status__in=['1', '2']) | Q(tag=True)))) \
            .order_by('-created').values('title_id') \
            .annotate(num_of_scene=Count('scene_id')).filter(num_of_scene__gt=0).count()
    return count


def count_process_scene_title(product, user):
    count = 0
    product_scenes = product.scene_list.all()
    if user.role == 'admin':
        count = models.Scene.objects.filter(title__in=SceneTitle.objects.filter(product_scene__in=product_scenes,
                                            production_file__in=['', None]).exclude(is_done=True).exclude(status='3'))\
            .values('title_id').annotate(num_of_scene=Count('scene_id')).filter(num_of_scene__gt=0).count()
    else:
        count = models.Scene.objects.filter(title__in=SceneTitle.objects.filter(product_scene__in=product_scenes,
                                                                                status='3')).values('title_id')\
            .annotate(num_of_scene=Count('scene_id')).filter(num_of_scene__gt=0).count()
    return count


def count_done_scenetitle_without_productionfile(product, user):
    if user.is_anonymous or user.role != 'admin':
        return 0
    product_scenes = product.scene_list.all()
    count = models.Scene.objects.filter(title__in=SceneTitle.objects.filter(product_scene__in=product_scenes,
                                                                            status='5',
                                                                            is_done=True))\
        .values('title_id').annotate(num_of_scene=Count('scene_id')).filter(num_of_scene__gt=0).count()
    return count


def list_ip_product_user(user, product_id):
    try:
        product = Product.objects.get(pk=product_id)
        product_user = ProductUser.objects.get(user=user, product=product)
        ips = product_user.list_ip
        return ips
    except:
        return None


def count_ip_product_user(user, product):
    try:
        product_user = ProductUser.objects.get(user=user, product=product)
        ips = len(product_user.list_ip)
        return ips
    except:
        return 0


def view_only(user, product):
    try:
        product_member = ProductUser.objects.get(product=product, user=user)
        if not product_member.is_rating and not product_member.is_favorite:
            return True
    except:
        pass
    return False


def get_seen(comment, comment_type):
    if comment_type == 'product':
        seen = PreviewProductComment.objects.filter(comment=comment)
    elif comment_type == 'scene':
        seen = comment.preview_comment.all()
    return seen


def get_child_comments(comment, comment_type):
    if comment_type == 'product':
        childs = comment.child_comments.all()
    elif comment_type == 'scene':
        childs = comment.child_comment.all()
    return childs


def get_first_version_id(scene):
    if scene.version:
        parent_scene = scene.version
        return parent_scene.other_versions.order_by('-created').first().pk
    return scene.pk


def check_member_invited(project, member):
    project_users_list = member.productuser_set.all().values_list('product_id', flat=True)
    if project in project_users_list:
        return True


def minus(a, b):
    return a - b


def get_last_version_thumbnail(scene, host):
    if scene.other_versions.exists():
        return get_thumbnail(scene.other_versions.first(), host)
    else:
        return get_thumbnail(scene, host)


def get_last_comment(scene_title, time_ago):
    scenes = Scene.objects.filter(title=scene_title)
    comments = SceneComment.objects.filter(
        Q(created__gt=time_ago) & (Q(scene__in=scenes) | Q(scene_title=scene_title)))
    return comments


register.filter('get_last_comment', get_last_comment)


def get_last_comment_new_scene(user, comments):
    if not comments:
        return None
    comment = comments.exclude(user__role=user.role).order_by('-created').first()
    return comment


register.filter('get_last_comment_new_scene', get_last_comment_new_scene)


def get_last_comment_scene(scene, time_ago):
    comments = SceneComment.objects.filter(Q(created__gt=time_ago) & (Q(scene=scene) | Q(scene_title=scene.title)))
    return comments


register.filter('get_last_comment_scene', get_last_comment_scene)


def is_product_seen(offer_products, user):
    for offer in offer_products:
        if not offer.message_product.exists() or not offer.message_product.first().seen_date and \
                offer.message_product.first().owner != user:
            return False
    return True


def is_project_seen(offer, user):
    if offer.message_product.exists() and MessageReceiver.objects.filter(
                        message=offer.message_product.first() , user=user, seen_date__isnull=True).exists():
        return False
    if not offer.message_product.exists() and offer.master_client and user != offer.master_client:
        return False
    return True


def can_upload_plan(user, offer):
    can = True
    if user.role == 'master_client':
        can = False
    if offer.status in ['3', '4', '5', '6']:
        can = False
    return can


def get_variation(offer, variation):
    if variation:
        return offer.variation_offer.all().exclude(pk=variation.pk).order_by('-created')
    else:
        return offer.variation_offer.all().order_by('-created')


def get_variation_choosed(offer):
    if offer.status != '2':
        variation = offer.variation_offer.filter(is_chosen='2').first()
        return variation


def check_null(scene_titles):
    return scene_titles.filter(scene_title__isnull=False).exists()


def get_product_user(user, product):
    return ProductUser.objects.filter(product=product, user=user).first()


def check_notification_product(user, product):
    try:
        product_user = ProductUser.objects.filter(user=user, product=product, notification='on')
        if product_user.exists():
            return True
    except:
        return False


def check_avt(user):
    class_name = ''
    try:
        if user.role == 'creator':
            creator_profile = user.user_creator.first().last_published_version
            if creator_profile and not creator_profile.avatar:
                class_name = user.role
        elif not user.avatar:
            class_name = user.role
    except:
        pass
    return class_name


def check_avt_creator(creator_profile, public_profile):
    try:
        if not creator_profile.avatar and (not public_profile or (public_profile and not public_profile.avatar)):
            return 'creator'
        else:
            return ''
    except:
        pass
    return 'creator'


def get_member(project):
    pus = ProductUser.objects.filter(product=project, user__is_active=True,
                                     is_invited=False, user__role=AuthUser.MASTERCLIENT).order_by('order_user')
    list_member_ids = pus.values_list('user_id', flat=True)
    preserved = Case(*[When(pk=pk, then=pos) for pos, pk in enumerate(list_member_ids)])
    return project.authuser_set.filter(pk__in=list_member_ids, is_active=True).order_by(preserved)


def get_member_inviting(project):
    pus = ProductUser.objects.filter(product=project,
                                     is_invited=True, user__role=AuthUser.MASTERCLIENT).exists()
    ows = ProductUser.objects.filter(product=project, user__is_active=False, position=ProductUser.OWNER,
                                     user__role=AuthUser.MASTERCLIENT, user__is_verify=False,
                                     user__last_login__isnull=True).exists()
    return pus or ows


def count_undownload_product_comment(product, user):
    comments = ProductComment.objects.filter(project=product, resolved=False, has_file=True).exclude(
        user__role=user.role)
    files = ProductCommentFile.objects.filter(message__in=comments)
    file_download = DownloadedProductComment.objects.filter(user__role=user.role, file__in=files)
    folders = ProductCommentFolder.objects.filter(message__in=comments, parent__isnull=True).count()
    list_folder_ids = set(list(files.filter(folder__isnull=False, file_id__in=file_download.values_list('file_id')) \
                               .values_list('folder_id', flat=True)))
    folder_download = get_parent_folder(list_folder_ids)
    count_comments = files.filter(folder__isnull=True).exclude(file_id__in=file_download.values_list('file_id')).count()
    undownload_count = count_comments + folders - folder_download
    return undownload_count


def count_batch_number_project(product, user):
    product_user = ProductUser.objects.get(product=product, user=user)
    count = OfferMessageReceiver.count_unread_offer(str(product.pk), str(user.pk))
    # count = count_unread_offer_message_count(product, user)
    if product_user.position != ProductUser.STAFF:
        count += count_undownload_product_comment(product, user)
        count += count_new_update_scene_title(product, user)
    return count


def get_parent_folder(list_folder_ids):
    count = 0
    list_folder = []
    for folder_id in list_folder_ids:
        folder = ProductCommentFolder.objects.get(pk=folder_id)
        while True:
            if not folder.parent:
                if folder not in list_folder:
                    list_folder.append(folder)
                    count += 1
                break
            folder = folder.parent
    return count


register.filter('get_parent_folder', get_parent_folder)


def check_product_scene_has_scene(product_scene):
    check_scene = False
    if product_scene.title_product_scene.exists():
        for scene_title in product_scene.title_product_scene.all():
            if scene_title.scene_title.exists():
                check_scene = True
                break
    return check_scene


def get_list(offer):
    list_offer = []
    list_offer.append(offer.id)
    return list_offer


@register.filter(name='chr')
def chr_(value):
    if value < 26:
        return chr(value + 97)
    return chr_(value//26 - 1) + chr_(value % 26)


@register.filter(name='is_latest_version')
def is_latest_version(comment):
    try:
        if comment.scene:
            return comment.scene == comment.scene.get_latest_version()
    except Exception:
        pass
    return False


@register.filter(name='admin_icon_color')
def admin_icon_color(batch_number):
    if batch_number <= 0:
        return ' deepblue'
    return ''


@register.filter(name='client_icon_color')
def client_icon_color(project, batch):
    if batch:
        return ''
    elif project.current_scene == project.max_scene:
        return ' blue'
    return ' deepblue'


@register.filter(name='process_icon_color')
def process_icon_color(product):
    product_scenes = product.scene_list.all()
    num_of_scene_title = get_num_of_scene_in_process_by_status(product_scenes, ['3', '4', '5', '6'])

    if num_of_scene_title == 0:
        return ' blue'
    else:
        return ' deepblue'


@register.filter(name='admin_process_icon_color')
def admin_process_icon_color(product, batch_number):
    product_scenes = product.scene_list.all()
    num_of_scene_title_not_done = get_num_of_scene_in_process_by_status(product_scenes, ['4'])

    if batch_number > 0:
        return ''
    elif num_of_scene_title_not_done == 0:
        return ' deepblue'
    else:
        return ' blue'


def get_num_of_scene_in_process_by_status(product_scenes, status):
    return models.Scene.objects.filter(
        title__in=
        SceneTitle.objects.filter(
            product_scene__in=product_scenes,
            status__in=status))\
        .values('title_id')\
        .annotate(num_of_scene=Count('scene_id'))\
        .filter(num_of_scene__gt=0)\
        .count()


def project_not_file(offer):
    if offer.project and (not offer.plan_offer.exists() or not offer.contact):
        return True


def project_done(offer):
    if offer.project and offer.project.max_scene <= offer.project.current_heart:
        return True


def count_new_message(offer, user):
    if offer.message_product.exists() and MessageReceiver.objects.filter(
            message=offer.message_product.first(), user=user, seen_date__isnull=True).exists():
        not_seen = MessageReceiver.objects.filter(seen_date__isnull=True, user=user).values('message')
        return models.ProductMessage.objects.filter(offer_product=offer, pk__in=not_seen).count()
    if not offer.message_product.exists() and offer.master_client and user != offer.master_client:
        return 1
    return 0



def get_owners_project(project):
    pus = ProductUser.objects.filter(
        Q(product=project) & Q(position=ProductUser.OWNER) & Q(user__is_active=True, is_invited=False)
    ).order_by('order_user')

    list_member_ids = pus.values_list('user_id', flat=True)
    preserved = Case(*[When(pk=pk, then=pos) for pos, pk in enumerate(list_member_ids)])
    return project.authuser_set.filter(pk__in=list_member_ids).order_by(preserved)



def get_admin_project(project):
    return project.authuser_set.filter(role='admin', is_active=True)


def get_current_budget_admin(admin, product):
    offers = OfferCreator.objects.filter(creator__is_active=True, admin=admin, project=product).exclude(status='5')
    if offers.exists():
        current_total = offers.aggregate(Sum('reward'))
        current_total = current_total.get('reward__sum')
    else:
        current_total = 0
    return current_total


def get_current_budget_creator(creator, product):
    offers = OfferCreator.original_objects.filter(creator__is_active=True, admin__is_active=True, creator=creator,
                                                  project=product).exclude(status='5')
    if offers.exists():
        current_total = offers.aggregate(Sum('reward'))
        current_total = current_total.get('reward__sum')
    else:
        current_total = 0
    return current_total


def get_done_budget_creator(creator, product):
    offers = OfferCreator.original_objects.filter(creator__is_active=True, admin__is_active=True, creator=creator,
                                                  project=product, status='4')
    if offers.exists():
        current_total = offers.aggregate(Sum('reward'))
        current_total = current_total.get('reward__sum')
    else:
        current_total = 0
    return current_total


def get_undone_budget_creator(creator, product):
    offers = OfferCreator.original_objects.filter(creator__is_active=True, admin__is_active=True, creator=creator,
                                                  project=product, status__in=OfferCreator.STATUS_IN_PROGRESS)
    if offers.exists():
        current_total = offers.aggregate(Sum('reward'))
        current_total = current_total.get('reward__sum')
    else:
        current_total = 0
    return current_total


def list_viewers_seen(message, user):
    user_ids = message.messagereceiver_set.filter(seen_date__isnull=False).values_list('user', flat=True)
    return AuthUser.objects.filter(pk__in=user_ids).exclude(pk=user.pk)


def is_message_for_old_version(message, offer):
    check = False
    variation = message.variation
    variation_new = offer.variation_offer.filter(is_chosen='2').first()
    if variation_new and variation == variation_new:
        check = True
    elif not variation_new:
        variation_new = offer.variation_offer.order_by('-created').first()
        if variation_new and variation == variation_new:
            check = True
    return check


def add_param(url):
    import urllib.parse as urlparse
    from urllib.parse import urlencode

    params = {'v': '1'}

    url_parts = list(urlparse.urlparse(url))
    query = dict(urlparse.parse_qsl(url_parts[4]))
    query.update(params)
    url_parts[4] = urlencode(query)
    return urlparse.urlunparse(url_parts)


def get_icon_offer(offer, user):
    if offer.status == '4':
        user_role = 'creator' if offer.creator == user else 'admin'
        review_admin_dict = {'1': 'icon--sicon-like', '2': 'icon--sicon-smile', '3': 'icon--sicon-unlike'}
        if user_role in [AuthUser.CREATOR, AuthUser.CREATOR]:
            return review_admin_dict.get(offer.review_admin, '') if user_role == AuthUser.CREATOR \
                                                                 else review_admin_dict.get(offer.review, '')
    return ''


def get_offer_url(offer, host):
    if offer.status in ['1', '2']:
        url = reverse_lazy('app:messenger_waiting') + f'?project_id={offer.project.pk}&offer={offer.pk}'
    else:
        url = reverse_lazy('app:messenger_processing') + f'?project_id={offer.project.pk}&offer={offer.pk}'
    return f"{host}{url}"


def list_latest_seen_message(offer):
    list_message = []
    list_user = []
    master_admins = AuthUser.objects.filter(role='master_admin')
    for master_admin in master_admins:
        list_user.append(master_admin)
    if offer.project:
        project = offer.project
        list_owner = project.authuser_set.filter(position=ProductUser.OWNER)
        for owner in list_owner:
            list_user.append(owner)
    else:
        list_user.append(offer.master_client)

    for user in list_user:
        message = ProductMessage.objects.filter(messagereceiver__seen_date__isnull=False, offer_product=offer,
                                                messagereceiver__user=user).first()
        if message not in list_message:
            list_message.append(message)
    return list_message


def get_last_offer_message(offer):
    return offer.message_offer.filter(real_name__isnull=True).last()


def get_audio_name(album, user):
    audio_name = ''
    try:
        if album.last_published_version and album.last_published_version.file:
            audio_name = album.last_published_version.file.name
    except:
        pass

    if audio_name != '':
        import re
        return re.sub(r"audio\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', audio_name)
    else:
        return ''


def get_file_type(album, user):
    audio_name = ''
    try:
        if album.last_published_version and album.last_published_version.file:
            audio_name = album.last_published_version.file.name
    except:
        pass

    if audio_name != '':
        import re
        file_extension = re.search(".[0-9a-zA-Z]{3,4}$", audio_name).group()
        if file_extension in '.mp3,.wav, .MP3, .WAV':
            return 'audio'
        elif file_extension in '.mp4,.mov, .MP4,.MOV, .avi, .AVI':
            return 'movie'
        elif file_extension in '.pdf,.PDF':
            return 'pdf'
        elif file_extension in '.png, .jpg, .PNG, .JPG':
            return 'image'
    return ''


def get_link(url):
    if url:
        return url
    else:
        return ''


def check_new(is_checker, public_profile):
    return is_checker and public_profile


def check_new_sale_content(sale_content):
    if sale_content.last_version:
        return 'new'
    audios = sale_content.last_published_version.album.all()
    for audio in audios:
        if audio.last_version:
            return 'new'
    return 'editable'


def get_description_sale_content(sale_content):
    try:
        if sale_content.last_published_version:
            return sale_content.last_published_version.desc
    except:
        pass
    return ''


def get_type_sale_content(sale_content):
    try:
        if sale_content.last_published_version and sale_content.last_published_version.sale_type:
            return sale_content.last_published_version.sale_type
    except:
        pass
    return '2'


def get_title_sale_content(sale_content, user):
    try:
        if sale_content.last_published_version:
            return sale_content.last_published_version.title
    except:
        pass
    return 'Untitled Sample'


def get_price_sale_content(sale_content):
    try:
        if sale_content.last_published_version:
            return sale_content.last_published_version.price
    except:
        pass
    return ''


def get_content_type(sale_content):
    try:
        if sale_content.last_published_version:
            return sale_content.last_published_version.content_type
    except:
        pass
    return 'music'


def get_song_attr1_min(sale_content):
    try:
        if sale_content.last_published_version and sale_content.last_published_version.song_attribute1_min:
            return sale_content.last_published_version.song_attribute1_min
    except:
        pass
    return '1'


def get_song_attr2_min(sale_content):
    try:
        if sale_content.last_published_version and sale_content.last_published_version.song_attribute2_min:
            return sale_content.last_published_version.song_attribute2_min
    except:
        pass
    return '1'


def get_song_attr1_max(sale_content):
    try:
        if sale_content.last_published_version and sale_content.last_published_version.song_attribute1_max:
            return sale_content.last_published_version.song_attribute1_max
    except:
        pass
    return '2'


def get_song_attr2_max(sale_content):
    try:
        if sale_content.last_published_version and sale_content.last_published_version.song_attribute2_max:
            return sale_content.last_published_version.song_attribute2_max
    except:
        pass
    return '2'


def get_thumbnail_sale_content(sale_content, user):
    try:
        last_published_version = sale_content.last_published_version
        if last_published_version.show_thumbnail == 'image':
            if last_published_version.image:
                return 'background-image: url(' + last_published_version.image.url + ');'
        else:
            if last_published_version.default_thumbnail:
                return 'background: ' + last_published_version.default_thumbnail
    except:
        pass
    return 'background: #A7A8A9'


def get_thumbnail_sale_content_url(sale_content, user):
    try:
        last_version = sale_content.last_version
        last_published_version = sale_content.last_published_version
        creator = sale_content.profile.creator.user
        if last_version and user and not user.is_anonymous  and (user.role == AuthUser.CURATOR or user == creator):
            if last_version.show_thumbnail == 'image':
                if last_version.image:
                    return last_version.image.url
                elif last_published_version.image:
                    return last_published_version.image.url
            else:
                if last_version.default_thumbnail:
                    return settings.HOST + DEFAULT_ALBUM_BACKGROUND + 'bg_' + last_version.default_thumbnail.replace('#', '').lower() + '.png'
                elif last_published_version.default_thumbnail:
                    return  settings.HOST + DEFAULT_ALBUM_BACKGROUND + 'bg_' + last_published_version.default_thumbnail.replace('#', '').lower() + '.png'
        else:
            if last_published_version.show_thumbnail == 'image':
                if last_published_version.image:
                    return last_published_version.image.url
            else:
                if last_published_version.default_thumbnail:
                    return settings.HOST + DEFAULT_ALBUM_BACKGROUND + 'bg_' + last_published_version.default_thumbnail.replace('#', '').lower() + '.png'
    except:
        pass
    return settings.HOST + DEFAULT_ALBUM_BACKGROUND + 'bg_a7a8a9.png'


def get_show_thumbnail(sale_content, user):
    try:
        last_published_version = sale_content.last_published_version
        return last_published_version.show_thumbnail
    except:
        pass
    return 'color'


def get_hash_tag_in_sale_content(sale_content, user):
    last_published_version = sale_content.last_published_version
    return last_published_version.get_hash_tags_in_sale()


def get_customizable_sale_setting(sale_content, user):
    try:
        last_published_version = sale_content.last_published_version
        return last_published_version.customizable_sale_setting
    except:
        pass
    return ''


def get_default_color(sale_content):
    try:
        if sale_content.last_published_version and sale_content.last_published_version.default_thumbnail:
            return sale_content.last_published_version.default_thumbnail
    except:
        pass
    return '#A7A8A9'


def get_auction_start_time(sale_content):
    try:
        if sale_content.last_published_version:
            return sale_content.last_published_version.start_time.strftime('%Y/%m/%d %H:%M')
    except:
        pass
    return ''


def get_auction_end_time(sale_content):
    try:
        if sale_content.last_published_version:
            return sale_content.last_published_version.end_time.strftime('%Y/%m/%d %H:%M')
    except:
        pass
    return ''


def get_auction_price(sale_content, data_value):
    try:
        if sale_content.last_published_version:
            if sale_content.last_published_version.price:
                return data_value + str(sale_content.last_published_version.price)
    except:
        pass
    return ''


def get_auction_max_price(sale_content, data_value):
    try:
        if sale_content.last_published_version:
            if sale_content.last_published_version.max_price:
                return data_value + str(sale_content.last_published_version.max_price)
    except:
        pass
    return ''


def get_sale_type_text(sale_content, user):
    try:
        if sale_content.last_version and sale_content.last_version.sale_type and user and not user.is_anonymous and (
                user.role == AuthUser.CURATOR or user == sale_content.profile.creator.user):
            if sale_content.last_version.price and sale_content.last_version.max_price:
                return f'{int(sale_content.last_version.price):,}' + '円 - ' + f'{int(sale_content.last_version.max_price):,}' + '円'
            elif sale_content.last_version.price:
                return f'{int(sale_content.last_version.price):,}' + '円 - '
            elif sale_content.last_version.max_price:
                return ' - ' + f'{int(sale_content.last_version.max_price):,}' + '円'
            else:
                return '' if sale_content.last_version.sale_type == '4' else sale_content.last_version.get_sale_type_display()
        elif sale_content.last_published_version and sale_content.last_published_version.sale_type:
            if sale_content.last_published_version.price and sale_content.last_published_version.max_price:
                return f'{int(sale_content.last_published_version.price):,}' + '円 - ' + f'{int(sale_content.last_published_version.max_price):,}' + '円'
            elif sale_content.last_published_version.price:
                return f'{int(sale_content.last_published_version.price):,}' + '円 - '
            elif sale_content.last_published_version.max_price:
                return ' - ' + f'{int(sale_content.last_published_version.max_price):,}' + '円'
            else:
                return '' if sale_content.last_published_version.sale_type == '4' else sale_content.last_published_version.get_sale_type_display()
    except:
        pass
    return 'Contact for price'


def get_sale_type_text_new(sale_content, user):
    try:
        if sale_content.last_version and sale_content.last_version.sale_type and user and not user.is_anonymous and (user.role == AuthUser.CURATOR or user == sale_content.profile.creator.user):
            if sale_content.last_version.price and sale_content.last_version.max_price:
                return f'{int(sale_content.last_version.price):,}' + '円 - ' + f'{int(sale_content.last_version.max_price):,}' + '円'
            elif sale_content.last_version.price:
                return f'{sale_content.last_version.price:,}' + '円 - '
            elif sale_content.last_version.max_price:
                return ' - ' + f'{int(sale_content.last_version.max_price):,}' + '円'
            else:
                return sale_content.last_version.get_sale_type_display()
        elif sale_content.last_published_version and sale_content.last_published_version.sale_type:
            if sale_content.last_published_version.price and sale_content.last_published_version.max_price:
                return f'{int(sale_content.last_published_version.price):,}' + '円 - ' + f'{int(sale_content.last_published_version.max_price):,}' + '円'
            elif sale_content.last_published_version.price:
                return f'{int(sale_content.last_published_version.price):,}' + '円 - '
            elif sale_content.last_published_version.max_price:
                return ' - ' + f'{int(sale_content.last_published_version.max_price):,}' + '円'
            else:
                return sale_content.last_published_version.get_sale_type_display()
    except:
        pass
    return 'お問い合わせ'


def get_type_text_sale_content(sale_content):
    try:
        if sale_content.last_version and sale_content.last_version.sale_type:
            return sale_content.last_version.get_sale_type_display()
        elif sale_content.last_published_version and sale_content.last_published_version.sale_type:
            return sale_content.last_published_version.get_sale_type_display()
    except:
        pass
    return 'Contact to buy'


def get_audio(album, user):
    try:
        if album.last_published_version and album.last_published_version.file:
            return album.last_published_version.file.url
    except:
        pass
    return ''


def get_audios(sale_content, user):
    try:
        if sale_content.last_published_version:
            return sale_content.last_published_version.album.all()
        return None
    except:
        pass

def has_sale_youtube_link(sale_content_version):
    try:
        if sale_content_version:
            return sale_content_version.sale_youtube_link
        return ''
    except:
        return ''


def get_sale_youtube_link(sale_content):
    try:
        if sale_content and sale_content.last_published_version and sale_content.last_published_version.sale_youtube_link:
            return sale_content.last_published_version.sale_youtube_link
        return ''
    except:
        return ''


def get_created_year(sale_content):
    try:
        if sale_content.last_published_version:
            return sale_content.last_published_version.created_year
    except:
        pass
    return ''


def get_credit(sale_content):
    try:
        if sale_content.last_published_version:
            return sale_content.last_published_version.credit
    except:
        pass
    return ''


def get_hashtag(sale_content):
    try:
        if sale_content.last_version:
            return sale_content.last_version.hashtag
        elif sale_content.last_published_version:
            return sale_content.last_published_version.hashtag
    except:
        pass
    return '#hashtag1 #hashtag2'


def get_creator_type(creator):
    if creator.last_published_version:
        return creator.last_published_version.type
    return ''


def get_public_audios(sale_content):
    list_audios = []
    if sale_content.last_published_version:
        for album_variation in sale_content.last_published_version.album.all():
            album_version = album_variation.last_published_version
            if album_version and album_version.file:
                list_audios.append(album_version.file.url)
    return list_audios


def get_modified_time(creator_profile):
    return creator_profile.modified.timestamp()


def get_position(user):
    if user.position:
        return user.position
    elif user.role == 'admin':
        return 'ディレクター'
    return ''


def get_enterprise(user):
    if user.enterprise:
        return user.enterprise
    elif user.role == 'admin':
        return 'SOREMO Co.,ltd.'
    return ''


def get_budget(adminpu):
    return adminpu.budget if adminpu.budget else 0


def get_current_budget_cost(admin, project):
    try:
        offers = OfferCreator.objects.filter(project=project, admin=admin).exclude(status='5')
        total = offers.aggregate(Sum('reward'))
        if total.get('reward__sum'):
            return total.get('reward__sum')
        else:
            return 0
    except:
        return 0


def get_budget_offer(product):
    offers = OfferCreator.objects.filter(project=product, creator__is_active=True, admin__is_active=True).exclude(
        status='5')
    total = 0
    if offers.exists():
        total = offers.aggregate(Sum('reward'))
        total = total.get('reward__sum')
        if not total:
            total = 0
    return total


def get_money_spent(product, total):
    if product.total_budget:
        total_budget = product.total_budget
        if total_budget != 0:
            value = round(total * 100 / product.total_budget)
            if value > 100:
                value = 100
            return value
    return 0


def get_budget_for_admin(product):
    total = 0
    pu = ProductUser.objects.filter(product=product, user__is_active=True, user__role='admin').exclude(
        budget__isnull=True)
    if pu.exists():
        total = pu.aggregate(Sum('budget'))
        total = total.get('budget__sum')
        if not total:
            total = 0
    return total


def get_budget_admins(product, total):
    if product.total_budget:
        total_budget = product.total_budget
        if total_budget != 0:
            value = round(total * 100 / product.total_budget)
            if value > 100:
                value = 100
            return value
    return 0


def get_datetime(_datetime):
    if not _datetime:
        return ''
    return _datetime.strftime('%y/%m/%d')


def get_short_datetime(_datetime):
    if not _datetime:
        return ''
    return _datetime.strftime('%y/%-m/%-d')


def get_reward(__price):
    if not __price:
        return 0
    return f"{int(__price):,}"


def get_owner_name(project):
    s = ', '
    owners = list(
        project.authuser_set.filter(position=ProductUser.OWNER).values_list('fullname', flat=True))
    return s.join(owners)


def get_full_address(user):
    try:
        creator = user.user_creator.first()
        address = []
        s = ' - '
        if creator.mansion:
            address.append(creator.city)
        if creator.city:
            address.append(creator.province)
        if creator.province:
            address.append(creator.mansion)
        return s.join(address)
    except:
        pass
    return ''


def get_budget_admin_offer_done(admin, project):
    pu = ProductUser.objects.get(user=admin, product=project)
    total = pu.budget
    current_total = get_offer_done(admin, project)
    if total and total != 0:
        value = round(current_total * 100 / total)

        return value
    return 0


def get_offer_done(admin, project):
    offers = OfferCreator.objects.filter(creator__is_active=True, admin=admin, project=project, status='4')
    if offers.exists():
        current_total = offers.aggregate(Sum('reward'))
        current_total = current_total.get('reward__sum')
    else:
        current_total = 0
    return current_total
register.filter('get_offer_done', get_offer_done)

def get_current_done_budget_admin(admin, product):
    return f"{get_offer_done(admin, product):,}"

register.filter('get_current_done_budget_admin', get_current_done_budget_admin)


def get_budget_admin_offer(admin, product):
    current_total = get_current_budget_admin(admin, product)
    pu = ProductUser.objects.get(user=admin, product=product)
    total = pu.budget
    if total and total != 0:
        value = round(current_total * 100 / total)
        return value
    return 0


def get_current_spending_budget_admin(admin, product):
    return f"{get_current_budget_admin(admin, product):,}"


def get_current_paid_budget_admin(admin, product):
    offers = None
    if admin.role == 'admin':
        offers = OfferCreator.objects.filter(creator__is_active=True, admin=admin, project=product, status='4')
    if offers.exists():
        current_total = offers.aggregate(Sum('reward'))
        current_total = current_total.get('reward__sum')
    else:
        current_total = 0
    return f"{current_total:,}"


def get_admin_budget(admin, project):
    try:
        pu = ProductUser.objects.get(user=admin, product=project)
        return f"{pu.budget:,}"
    except:
        return 0


def get_scene_name_offer(offer):
    return str(offer.scenes)


def get_infor_user(user):
    if user.role == 'admin':
        if user.position:
            return user.position
        else:
            return 'ディレクター'
    elif user.role == 'creator' and user.user_creator.exists():
        creator = user.user_creator.first()
        profile = creator.last_published_version
        if profile and profile.type and profile.type != '':
            return profile.type
        elif creator.account_type:
            return creator.get_account_type_display()
        else:
            return 'アーティスト'


def get_user_url(user, avatar_type=None):
    host = settings.HOST
    try:
        if avatar_type and getattr(user, f'{avatar_type}_avatar'):
            return getattr(user, f'{avatar_type}_avatar').url

        if user.avatar:
            return user.avatar.url
        return f'{host}{ROLE_DEFAULT_AVATAR.get(user.role, DEFAULT_AVATAR)}'
    except:
        return f'{host}{DEFAULT_AVATAR}'


def get_url_offer(offer, project):
    if offer.status in ['1', '2']:
        url = reverse_lazy('app:messenger_waiting') + f'?project_id={project.pk}&offer={offer.pk}&from_refer=true'
    else:
        url = reverse_lazy('app:messenger_processing') + f'?project_id={project.pk}&offer={offer.pk}&from_refer=true'
    return url


def get_last_time_update_project(project):
    last_time = project.last_update
    if last_time:
        last_time = last_time.strftime("%y/%m/%d")
        return last_time
    return ''

def check_has_audio_file(message):
    if message.has_file:
        for file in message.files.all():
            if file.is_audio_file() == 'audio':
                return True
    return False


def get_infor_file(pk):
    try:
        return Scene.original_objects.get(pk=pk)
    except:
        try:
            return SceneCommentFile.objects.get(pk=pk)
        except:
            try:
                return SceneTitle.original_objects.get(pk=pk)
            except:
                try:
                    return SceneCommentFolder.objects.get(pk=pk)
                except:
                    return None


def get_object_product_comment(key):
    try:
        return ProductCommentFile.objects.get(pk=key)
    except:
        try:
            return ProductCommentFolder.objects.get(pk=key)
        except:
            return None


def get_message_dict_files(message):
    dict_files = {}
    files = message.files.filter(folder_id__isnull=True)
    folders = message.folders.filter(parent_id__isnull=True)
    
    # ファイルをreal_name昇順でソート
    sorted_files = files.order_by('real_name')
    # フォルダをname昇順でソート  
    sorted_folders = folders.order_by('name')
    
    # 最初にファイル、次にフォルダの順番で辞書に追加
    for file in sorted_files:
        dict_files[file.pk] = file
    for folder in sorted_folders:
        dict_files[folder.pk] = folder
    
    return dict_files


def get_message_file_folder(key, type_comment):
    mapping_comment_class = {
        'messenger': (MessageFile, MessageFolder),
        'messenger_owner': (ProductMessageFile, ProductMessageFolder),
    }
    file_model, folder_model = mapping_comment_class.get(type_comment)
    try:
        return file_model.objects.get(pk=key)
    except:
        try:
            return folder_model.objects.get(pk=key)
        except:
            return None

def get_sorted_message_files(message):
    """メッセージに関連するファイルをファイル名昇順で取得
    OfferMessage、ProductMessage、SceneCommentなど、filesリレーションを持つすべてのメッセージモデルに対応"""
    return message.files.all().order_by('real_name')

def get_content_comment_parent(parent_comment):
    if parent_comment.comment and parent_comment.comment != '':
        return parent_comment.comment
    else:
        if parent_comment.files.exists():
            return parent_comment.files.first().real_name


def generate_star(rating):
    from django.utils.safestring import mark_safe
    rated = round(rating)
    unrated = 5 - rated
    return mark_safe(rated*"<span class='selected'></span>" + unrated*"<span></span>")


def project_number_vote(project):
    product_scenes = project.scene_list.all()
    number_of_vote = RatingSceneTitle.objects.filter(title__product_scene__in=product_scenes).count()
    return number_of_vote


def get_list_user_download(file, type_comment):
    users = count_list_user_download(file, type_comment)
    if users:
        return users
    return None

def get_list_user_download_refactor(file, limit=6):
    if file.user_downloaded.exists():
        if limit is None or limit == 0:
            return file.user_downloaded.all()
        return file.user_downloaded.all()[:limit]
    return None

def count_list_user_download(file, type_comment):
    user_downloaded = None
    if type_comment == 'project' or type_comment == 'product':
        if file.product_file.exists():
            user_downloaded = file.product_file.all()
    elif file.user_downloaded.exists():
        user_downloaded = file.user_downloaded.all()
    if user_downloaded:
        list_user = list(user_downloaded.values_list('user_id', flat=True))
        users = models.AuthUser.objects.filter(pk__in=list_user)
        return users
    return None

def get_rating(scene_title, role):
    if role == 'admin':
        return scene_title.rating
    elif scene_title.owner_rating_title.exists():
        return scene_title.owner_rating_title.first().rating
    else:
        return 0


def get_list_ip(pu):
    if pu.list_ip and len(pu.list_ip) > 0:
        return ', '.join(str(x) for x in pu.list_ip)
    return mark_safe("<span class='detail-text'>IP制限なし</span>")


def get_user_seen(message, type):
    users = None
    if type in ['message_owner', 'messenger_owner']:
        users = message.receivers.filter(messagereceiver__seen_date__isnull=False).distinct()
    elif type in ['messenger_artist', 'messenger']:
        users = message.receivers.filter(offermessagereceiver__seen_date__isnull=False).distinct()
    else:
        users = message.receivers.filter(scenecommentreceiver__seen_date__isnull=False).distinct()
    return users


def function_count(users):
    return users.count() if users else 0


def get_items_update(post):
    return post.items.filter(type='update')


register.filter('get_items_update', get_items_update)


def get_items_new(post):
    return post.items.filter(type='new')


def get_url_scene(scene):
    product = scene.product
    return reverse_lazy('app:scene_title_detail', kwargs={"pk": str(product.pk), "pk_scene": str(scene.pk)})


def get_authorization_user(scene, user):
    return  models.ProductUser.objects.get(user=user, product=scene.product).view_only


def get_path_creator(creator, host):
    url = reverse_lazy('accounts:accounts_creator', kwargs={'pk': creator.user.pk})
    path = "{host}{url}".format(host=host, url=url)
    return path


def get_product_id(title):
    return title.product_scene.product_scene.first().pk


def check_child_folder(folder_id, type_message):
    try:
        folder = ""
        if type_message == 'project':
            folder = models.ProductCommentFolder.objects.get(pk=folder_id)
        elif type_message == 'scene' or type_message == 'scene_comment':
            folder = models.SceneCommentFolder.objects.get(pk=folder_id)
        elif type_message in ['message_artist', 'messenger', 'message', 'messenger_owner', 'message_owner']:
            try:
                folder = models.MessageFolder.objects.get(pk=folder_id)
            except:
                folder = models.ProductMessageFolder.objects.get(pk=folder_id)
        if not folder.child_folders.exists():
            if folder.children.exists():
                return True
        return False
    except:
        return False

def check_child_folder_refactor(folder):
    if not folder.child_folders.exists():
        if folder.children.exists():
            return True
    return False

def get_description_show_profile(choise):
    return {
        'public':'※誰でもアクセスできます。オーナー（お客様）と直接取引したり、作品販売もできます。',
        'private':'※SOREMOアカウント保有のメンバーのみアクセスできます。',
        'project': '※参加したプロジェクトのスタッフクレジット経由のみアクセスできます。'
    }[choise]


def option_time(count):
    if count < 10:
        return f'0{count}:00'
    return f'{count}:00'


def deleteable(user):
    return UserDeleteableService(user).process()


def get_other_member_in_project(project, user):
    if user.role == AuthUser.MASTERCLIENT:
        master_producer=project.get_master_producer_project()
        list_admin = project.productuser_set.filter(Q(user__is_active=True) & Q(is_invited=False) &
                                            (Q(position=ProductUser.MASTERADMIN, user__pk=master_producer.user.pk) |
                                            Q(position=ProductUser.PRODUCER)))
        return AuthUser.objects.filter(pk__in=list_admin.values_list('user_id', flat=True)).distinct()
    return project.get_member_project(user)


def get_other_member_in_offer(offer, user):
    return offer.get_other_member_offer(user)

def get_other_member_in_offer_refactor(offer_project, user):
    first_offer_user = offer_project.prefetched_offer_users[0] if offer_project.prefetched_offer_users else None
    if first_offer_user:
        return [
            ou for ou in offer_project.prefetched_same_offer_users 
            if ou.offer == first_offer_user.offer and ou.position != first_offer_user.position
        ]
    return None

register.filter('get_other_member_in_offer', get_other_member_in_offer)
register.filter('get_other_member_in_offer_refactor', get_other_member_in_offer_refactor)


def get_offer_user(offer, user):
    offer_user = OfferUser.objects.filter(offer=offer.offer, user=user).first()
    if offer_user:
        return offer_user.position
    if offer.offer.offer_product:
        if user.role == AuthUser.MASTERCLIENT:
            return 'owner'
        return 'master_admin'
    elif offer.offer.offer_creator:
        if user.role == AuthUser.MASTERADMIN:
            return 'admin'
        return 'creator'
    return

def get_offer_user_offer_id(offer_project, user):
    offer_user = offer_project.prefetched_offer_users[0] if offer_project.prefetched_offer_users else None
    if offer_user:
        return {"role": offer_user.position, "offer_id": str(offer_project.pk)}
    if offer_project.offer_product:
        if user.role == AuthUser.MASTERCLIENT:
            return {"role": 'owner', "offer_id": str(offer_project.pk)}
        return {"role": 'master_admin', "offer_id": str(offer_project.pk)}
    elif offer_project.offer_creator:
        if user.role == AuthUser.MASTERADMIN:
            return {"role": 'admin', "offer_id": str(offer_project.pk)}
        return {"role": 'creator', "offer_id": stroffer_project.pk}
    return

def get_offer_user_by_message(message):
    user = message.user
    offer = message.offer
    return get_offer_user(offer, user)


register.filter('get_offer_user', get_offer_user)
register.filter('get_offer_user_offer_id', get_offer_user_offer_id)
register.filter('get_offer_user_by_message', get_offer_user_by_message)

def get_offer_user_refactor(offer_project, user):
    offer_user = offer_project.prefetched_offer_users[0] if offer_project.prefetched_offer_users else None
    if offer_user:
        return offer_user.position
    if offer_project.offer_product:
        if user.role == AuthUser.MASTERCLIENT:
            return 'owner'
        return 'master_admin'
    elif offer_project.offer_creator:
        if user.role == AuthUser.MASTERADMIN:
            return 'admin'
        return 'creator'
    return

register.filter('get_offer_user_refactor', get_offer_user_refactor)

def get_deadline_full_time(date):
    if not date:
        return ''
    return date.strftime('%y/%-m/%-d  %H:%M')


def get_last_message_in_offer(offer, user):
    return offer.message_offer.filter(owner=user, type_message='1').last()


def get_task_in_progress(creator):
    user = creator.user
    return OfferCreator.objects.filter(
        (Q(admin=user) | Q(creator=user)) & Q(admin__is_active=True) & Q(creator__is_active=True) & Q(
            status__in=OfferCreator.STATUS_PROGRESS) & Q(project__is_active=True)).count()


def get_schedule_list_info(creator):
    return creator.get_schedule_list_info_model()


def get_system_message(message, user):
    if message.owner:
        offer = message.offer
        if user == offer.creator and message.system_message and message.system_message != '':
            return message.system_message
    return message.content


def is_editable_description(user, product_user):
    if user.role == AuthUser.MASTERADMIN or product_user.position in [ProductUser.DIRECTOR, ProductUser.OWNER,
                                                                      ProductUser.PRODUCER]:
        return True
    return False


def is_editable_director(user, product_user):
    if user.role == AuthUser.MASTERADMIN \
            or product_user.position in [ProductUser.MASTERADMIN, ProductUser.PRODUCER]:
        return True
    return False


def is_editable_project(user, product_user):
    if user.role == AuthUser.MASTERADMIN \
            or product_user.position in [ProductUser.MASTERADMIN, ProductUser.PRODUCER, ProductUser.OWNER]:
        return True
    return False


def check_editable_director(product_user, user):
    current_product_user = ProductUser.objects.filter(user=user, product=product_user.product).first()
    if current_product_user.position not in [ProductUser.MASTERADMIN, ProductUser.PRODUCER, ProductUser.DIRECTOR]:
        return False
    if product_user.user == user or product_user:
        return True
    if current_product_user.position in [ProductUser.MASTERADMIN, ProductUser.PRODUCER]:
        return True
    return False


def get_last_message_title(title, user_role):
    return title.get_last_message_scene_title(user_role)

def get_last_message_title_refactor(scene_title, comments):
    if scene_title: 
        for comment in comments:
            if comment.scene_title_id == scene_title.title_id or (comment.scene and comment.scene.title_id == scene_title.title_id):
                return comment
    return None

def display_currency(amount):
    amount = Decimal(amount).quantize(Decimal('1.'), ROUND_HALF_UP)
    return '{:,.0f}'.format(amount)

def calculate_after_tax_payment(amount):
    amount = Decimal(amount)
    if amount <= Decimal('1000000'):
        return amount * (Decimal('1') - Decimal('0.1021'))
    else:
        return amount - (amount - Decimal('1000000')) * Decimal('0.2042') - Decimal('102100')

def get_offer_creator_done(user):
    return OfferCreator.original_objects.filter(Q(creator=user) & Q(status='4'))


def count_offer_done(user):
    offer_done = get_offer_creator_done(user)
    return offer_done.count()


def get_money_offer_done(user):
    offer_done = get_offer_creator_done(user)
    if offer_done.exists():
        current_total = offer_done.aggregate(Sum('reward')).get('reward__sum')
    else:
        current_total = 0
    return current_total


DATE_JP_FORMAT = '%Y 年 %-m 月 %-d 日'


def format_date_jp(date_time):
    return date_time.strftime(DATE_JP_FORMAT)


def format_deadline_with_weekday(date_time):
    if not date_time:
        return ''
    
    # 日本語の曜日マッピング
    weekdays = {
        0: '月', 1: '火', 2: '水', 3: '木', 4: '金', 5: '土', 6: '日'
    }
    
    weekday_jp = weekdays[date_time.weekday()]
    return date_time.strftime(f'%-m月%-d日（{weekday_jp}）%H:%M')


def has_artist_to_add_director(product):
    return product.get_artist_in_project().filter(~Q(pk__in=product.artists_block.all())).exists()


def format_short_date(date_time):
    if not date_time:
        return ''
    return date_time.strftime(FORMAT_SHORT_DATE)


def check_offer_project_status(user, product):
    if product.productuser_set.filter(
            Q(user=user) & Q(is_super_producer=True) | Q(user__role=AuthUser.MASTERADMIN)).exists():
        return True
    return False


def round_usage_fee(usage_fee):
    return Decimal(usage_fee).quantize(Decimal('1.00'), ROUND_HALF_UP).normalize()


def get_link_to_preview_file(file, type_file):
    if type_file != 'document':
        return file.url
    from django.conf import settings
    import boto3

    s3 = boto3.client('s3', aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                      aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)

    import urllib.parse
    url = s3.generate_presigned_url(
        ClientMethod='get_object',
        Params={
            'Bucket': settings.AWS_STORAGE_BUCKET_NAME,
            'Key': file.name,
            'ResponseContentType': 'application/pdf'
        }
    )
    return url


def get_artist_of_sale_content(sale_content):
    try:
        if sale_content.profile:
            return sale_content.profile.creator.user
        elif sale_content.parent and sale_content.parent.profile:
            return sale_content.parent.profile.creator.user
    except:
        pass
    return None


def get_sale_contents_in_selection(selection, user):
    sale_selection = SaleContentSelection.objects.filter(selection=selection)
    return SaleContent.objects.filter(Q(pk__in=sale_selection.values_list('sale_content_id'))).order_by('order')


def get_thumbnail_sale_content_selection(sale_content_selection):
    return 'background: #' + sale_content_selection.default_thumbnail if sale_content_selection.default_thumbnail else 'background: #C4C4C4'


def get_new_sale(sale_content):
    if sale_content.child.exists():
        sale_content = sale_content.child.last()
    return sale_content


def get_stage_name_in_modal(artist):
    if artist.stage_name:
        return artist.stage_name
    if artist.stage_name_en:
        return ''
    return artist.get_display_name()


def render_position_in_pdf(items, index):
    try:
        return items[index].position
    except:
        pass
    return ''


def render_title_in_pdf(items, index):
    return items[index].title


def ratio_mileage(a, b):
    if a == 0 or b == 0:
        return 0
    return Decimal(a*100/b).quantize(Decimal('1.0'), ROUND_HALF_UP).normalize()


def get_usage_fee_next_rank_for_user_to_show(user, next_rank):
    if next_rank:
        creator = user.user_creator.first()
        rank_point = next_rank.usage_fee
        if creator.direct_contact == Creator.AGENT:
            rank_point = next_rank.usage_fee + 5
    return Decimal(rank_point).quantize(Decimal('1.00'), ROUND_HALF_UP).normalize()


def get_artists_in_mileage(mileage_rank):
    return count_artist_in_rank(mileage_rank)


def count_all_artists():
    return AuthUser.objects.filter(role=AuthUser.CREATOR, is_active=True).count()


def get_current_point_rate(user, mileage_rank):
    if user.total_point < 0:
        return 0
    if not mileage_rank or mileage_rank and mileage_rank.point == 0:
        return 100
    else:
        value = round(user.total_point * 100 / mileage_rank.point)
        return value if value < 100 else 100


def get_usage_fee_in_setting(user, type_setting=''):
    mileage_rank = user.get_mileage_for_artist()
    if mileage_rank:
        if type_setting == Creator.AGENT:
            return Decimal(mileage_rank.usage_fee + 5).quantize(Decimal('1.00'), ROUND_HALF_UP).normalize()
        return Decimal(mileage_rank.usage_fee).quantize(Decimal('1.00'), ROUND_HALF_UP).normalize()


def get_button_in_top_menu(creator_profile):
    return creator_profile.blocks.filter(item_block__type_block=ItemBlock.PROFILE_BLOCK,
                                         item_block__profile_block__isnull=False,
                                         item_block__profile_block__is_link_menu=True).first()



def get_type_file_header(header_block, type):
    if type not in ['key_visual_pc', 'key_visual_sp', 'banner']:
        return ''
    if getattr(header_block, type):
        file_name = getattr(header_block, type).name
        file_extension = re.search(CONST_REGEX_FILE, file_name)
        if file_extension:
            file_extension = file_extension.group().lower()
            if file_extension in ['.mp4', '.x-m4v', '.avi', '.webm', '.mov']:
                return 'video'
            elif file_extension in ['.png', '.jpeg', '.bmp', '.gif', '.ppm', '.jpg', '.svg']:
                return 'image'
    return ''


def is_show_icon_payment_done_task(offer, user):
    if offer.creator_payment_request and not offer.admin_payment_request and offer.admin == user:
        return False
    return True


def is_bookmarked_sale(sale, user):
    if user and not user.is_anonymous and \
            user.role in [AuthUser.CREATOR, AuthUser.MASTERCLIENT] and \
            sale.pk in user.bookmarks.all().values_list('sale__pk', flat=True):
        return True
    return False


def check_show_action_topic(user, topic):
    if user and not user.is_anonymous and (
            user.role == AuthUser.CURATOR or (topic.user and user.role == AuthUser.CREATOR and topic.user == user)):
        return True
    return False

def create_option_component_select(names, values):
    array_object = []
    if not names:
        return array_object;
    names_arr = names.split(',')
    if( values):
        value_arr = values.split(',')
        for id, name in enumerate(names_arr):
            array_object.append({'name': name.strip(), 'value': value_arr[id].strip()})
    else:
        for name in names_arr:
            array_object.append(name.strip())
    return array_object

def check_exist_data_on_string(str, data):
    if not data:
        return ''
    if data in str:
        return data;
    return ''

def check_show_sign_in(user, creator_profile):
    condition1 = not user or user.is_anonymous
    return condition1 and creator_profile.creator.user.is_producer_in_project()


def check_show_contact(user, creator):
    if not user or user.is_anonymous or not user.is_authenticated:
        return True
    if user.role == 'master_client':
        return True
    return False

def get_name_by_value_from_array_object(arrs, value):
    for objarr in arrs:
        if objarr.value == value:
            return objarr.name
    return ""

def get_data_array_index(arr, index):
    arr, index, arr[0]
    return arr[index]

def compare_now_with_valid_date(valid_date):
    if not valid_date:
        return False
    now_time = datetime.datetime.now()
    if now_time > valid_date:
        return False
    return True

def check_plan_contract_message(message):
    if message.files.count() == 1:
        if message.files.first().form_object:
            return message.files.first().get_type_file_display()
        elif message.files.first().type_file == '3':
            return message.files.first().get_type_file_display()
    elif message.files.count() == 2:
        if message.files.first().form_object:
            if not message.files.first().get_type_file_display() == message.files.last().get_type_file_display():
                return 'all'
            else:
                return message.files.first().get_type_file_display()
    return 'normal'


register.filter('check_plan_contract_message', check_plan_contract_message)

def get_plan_contract_bill_message(messages, offer):
    offer_last_plan = offer.get_offer_last_plan()
    offer_last_contract = offer.get_offer_last_contract()
    offer_last_bill= offer.get_offer_last_bill()
    lastMessage = None;
    for message in messages:
        typeCheck = check_plan_contract_message(message)
        if typeCheck in ['plan', 'contract', 'all', 'bill']:
            if typeCheck == 'plan' and get_plan_file_status(offer_last_plan, message) != 'approved':
                lastMessage = message
            elif typeCheck == 'contract' and get_contract_file_status(offer_last_contract, message) != 'approved':
                lastMessage = message
            elif typeCheck == 'bill' and get_bill_file_status(offer_last_bill, message) != 'approved':
                lastMessage = message
    return lastMessage

register.filter('get_plan_contract_bill_message', get_plan_contract_bill_message)

def get_plan_file_status(last_plan, message):
    message_last_plan = message.files.filter(type_file='5')
    if last_plan.offer_product.condition in OfferProduct.STATUS_SHOW_MENU:
        if message_last_plan.exists() and message_last_plan.first().form_object.is_approved:
            return 'approved'
        else:
            return 'disabled'
    elif last_plan.offer_product.condition in OfferProduct.STATUS_CONTRACT:
        if message_last_plan.exists() and message_last_plan.first().form_object.is_approved:
            return 'approved'
        else:
            return 'disabled'
    elif last_plan.offer_product.condition in OfferProduct.STATUS_UPLOADED_PLAN:
        # message file is last plan:
        if message_last_plan.exists() and last_plan == message_last_plan.first().form_object:
            if last_plan.valid_date and last_plan.valid_date < datetime.datetime.now():
                return 'disabled'
            return 'waiting'
        else:
            return 'disabled'
    return 'disabled'


register.filter('get_plan_file_status', get_plan_file_status)


def get_contract_file_status(last_contract, message):
    message_last_contract = message.files.filter(type_file='2')
    # project has started
    if last_contract.offer_product.condition in OfferProduct.STATUS_SHOW_MENU:
        if message_last_contract.exists() and message_last_contract.first().form_object.is_approved:
            return 'approved'
        else:
            return 'disabled'
    # project has contract file but not approved contract
    elif last_contract.offer_product.condition in OfferProduct.STATUS_CONTRACT:
        if message_last_contract.exists() and last_contract == message_last_contract.first().form_object:
            return 'waiting'
        else:
            return 'disabled'
    return 'disabled'


register.filter('get_contract_file_status', get_contract_file_status)


def get_bill_file_status(last_bill, message):
    # project has started
    if not message == last_bill.message:
         return 'disabled'
    elif message.offer.condition in OfferProduct.STATUS_PAYMENTED:
        return 'approved'
    else:
        return 'waiting'
      


register.filter('get_bill_file_status', get_bill_file_status)


def get_file_attribute(message):
    try:
        file = message.files.first()
        return 'data-file-id=' + str(file.pk)
    except:
        return ''


register.filter('get_file_attribute', get_file_attribute)


def get_file_bill_attribute(message, offer_amount):
    try:
        file = message.files.first()
        return 'data-file-id=' + str(file.pk) + ' data-bill-amount=' + str(display_currency(offer_amount))
    except:
        return ''


register.filter('get_file_bill_attribute', get_file_bill_attribute)


def get_artist_contract_file_attribute(offer):
    try:
        return 'data-file-id=' + str(offer.pk)
    except:
        return ''


register.filter('get_artist_contract_file_attribute', get_artist_contract_file_attribute)


def get_file_text_bottom(message):
    try:
        file = message.files.first()
        form = file.form_object
        if form.is_approved:
            nearest_system_message = ProductMessage.objects.filter(offer=message.offer, type_message=ProductMessage.SYSTEM_MESSAGE, created__gt=message.created).order_by('created')
            if nearest_system_message.exists():
                nearest_system_message = nearest_system_message.first()
                approved_date = nearest_system_message.created
                formatted_date = approved_date.strftime('%-m/%-d %H:%M') if approved_date.year == datetime.datetime.now().year else approved_date.strftime('%y/%-m/%-d %H:%M')
                if file.type_file == '5':
                    return '注文日： ' + formatted_date
                elif file.type_file == '2':
                    return '締結日： ' + formatted_date
                elif file.type_file == '3':
                    return '支払日： ' + formatted_date
        elif file.form_object.valid_date:
            valid_date = file.form_object.valid_date
            formatted_date = valid_date.strftime('%-m/%-d %H:%M') if valid_date.year == datetime.datetime.now().year else valid_date.strftime('%y/%-m/%-d %H:%M')
            return '有効期限: ' + formatted_date
    except:
        pass
    return ''


register.filter('get_file_text_bottom', get_file_text_bottom)


def get_artist_contract_file_text_bottom(offer):
    try:
        if offer.valid_date:
            valid_date = offer.valid_date
            formatted_date = valid_date.strftime('%-m/%-d %H:%M') if valid_date.year == datetime.datetime.now().year else valid_date.strftime('%y/%-m/%-d %H:%M')
            return '有効期限: ' + formatted_date
    except:
        pass
    return ''


register.filter('get_artist_contract_file_text_bottom', get_artist_contract_file_text_bottom)


def offer_is_valid(offer):
    try:
        if isinstance(offer, str):
            offer = json.loads(offer)[0]['fields']
            if offer['valid_date'] and not parse(offer['valid_date']) < datetime.datetime.now():
                return True
        elif offer.valid_date and not offer.valid_date < datetime.datetime.now():
            return True
    except:
        pass
    return False


register.filter('offer_is_valid', offer_is_valid)


def get_default_deadline_from_offer(real_offer):
    try:
        if real_offer.offer_product:
            return real_offer.offer_product.deadline.strftime(FORMAT_DATE) + ' ' +  real_offer.offer_product.deadline.strftime(FORMAT_TIME) + ';' +\
            real_offer.offer_product.start_time.strftime(FORMAT_DATE) + ' - ' + real_offer.offer_product.end_time.strftime(FORMAT_DATE)
    except:
        pass
    return ''


def get_default_deadline_from_project(project):
    try:
        if project.offer_product.exists():
            real_offer = project.offer_product.first()
            return real_offer.deadline.strftime(FORMAT_DATE) + ' ' +  real_offer.deadline.strftime(FORMAT_TIME) + ';' +\
            real_offer.start_time.strftime(FORMAT_DATE) + ' - ' + real_offer.end_time.strftime(FORMAT_DATE)
    except:
        pass
    return ''

register.filter('get_default_deadline_from_project', get_default_deadline_from_project)

def get_owner_infor_from_project(project):
    try:
        owner = project.get_owner()
        if owner:
            if owner.enterprise:
                return owner.enterprise
            elif owner.display_name:
                return owner.display_name
            else:
                return owner.fullname
    except:
        pass
    return ''

register.filter('get_owner_infor_from_project', get_owner_infor_from_project)


def get_allow_public_val(form_contract_and_plan):
    try:
        if form_contract_and_plan.id:
            return form_contract_and_plan.allow_public_contract
    except:
        pass
    return False

def get_all_scene_name_by_project(project, user):
    list_scene_title = '';
    temp_check_title = [];
    list_director_producer = project.get_director_producer_in_project();
    list_value = get_value_exists(project, user)
    list_scenes = ''
    for index, scene in enumerate(list_value[0]):
        if not scene.strip() == '' and index != len(list_value[0]):
            list_scenes += scene + ','
        elif not scene.strip() == '' and index == len(list_value[0]):
            list_scenes += scene
        

    if len(list_scenes) > 0 and list_scenes[len(list_scenes) - 1] == ',':
        list_scenes = list_scenes[:-1]

    if not user in AuthUser.objects.filter(pk__in=list_director_producer.values_list('id')): 
        return list_scene_title + list_scenes;

    if project:
        list_scene = project.scene_list.all();
        for indScene, scene in enumerate(list_scene):
            list_title = list(scene.title_product_scene.filter().values_list('title', flat=True))
            if list_title:
                for indTitle, title in enumerate(list_title):
                    if title.strip() not in temp_check_title and not title.strip() == '':
                        temp_check_title.append(title)
                        if indScene == len(list_scene) and indTitle == len(list_title):
                            list_scene_title += title
                        else:
                            list_scene_title += title + ','
    if len(list_scene_title) > 0 and list_scene_title[len(list_scene_title) - 1] == ',':
        list_scene_title = list_scene_title[:-1]

    list_scene_title = list_scene_title + ',' + list_scenes
    list_name_text = ''
    for i,t in enumerate(list(set(list_scene_title.split(',')))):
        if i == len(list(set(list_scene_title.split(',')))) - 1:
            list_name_text += t
        else: 
            list_name_text = list_name_text + t + ','
    return list_name_text

def get_value_exists(project, user):
    options = project.product_offers.filter()
    if user:
        options = options.filter(admin=user)
    offer_scenes = list(options.values_list('scenes', flat=True))
    offer_quantity = list(options.filter(quantity__isnull=False).values_list('quantity', flat=True))
    offer_format = list(options.filter(data_format__isnull=False).values_list('data_format', flat=True))

    list_scenes = list(set([i.strip() for i in offer_scenes]))
    list_quantity = list(set([i.strip() for i in offer_quantity]))
    list_data_format = list(set([i.strip() for i in offer_format]))
    list_value = []
    list_value.append(list_scenes)
    list_value.append(list_quantity)
    list_value.append(list_data_format)
    return list_value

def get_all_skills(arg=''):
    skills = Skill.objects.all().values_list('id', 'name', 'group')
    result = {}
    for skill in skills:
        res = [*skill[0:2], skill[2].lower().replace(' ', '_')]
        result.setdefault(skill[2], []).append(res)

    return result.items();

def get_list_contract_type(contract):
    list_contract = [];
    if contract:
        list_contract = contract.split('/')
        for l in list_contract:
            l = l.strip()
    return list_contract

def get_contract_note(project):
    form_contract = get_form_plan_contract(project)
    note_return = None
    if form_contract and form_contract.note:
        note_return = form_contract.note
    else:
        note_return = ''
    return note_return

def check_exist_message(message):
    if message.__class__.__name__ != 'OfferMessage':
        return True
    if message and message.files.exists() or hasattr(message, 'content') and message.content != '' or hasattr(message, 'comment') and message.comment != '':
        return True
    return False

def get_thumbnail_take(scene, host=''):
    last_scene = scene.other_versions.all().last()
    if last_scene:
        if last_scene.thumbnail:
            return last_scene.thumbnail.url
        elif bool(last_scene.movie.name):
            type_scene = last_scene.is_audio_file()
            if type_scene == 'audio':
                return host + '/static/images/audio-item-thumbnail.png'
            elif type_scene == 'document':
                return host + '/static/images/pdf-item-thumbnail.png'
    if scene.thumbnail:
        return scene.thumbnail.url
    elif bool(scene.movie.name):
        type_scene = scene.is_audio_file()
        if type_scene == 'audio':
            return host + '/static/images/audio-item-thumbnail.png'
        elif type_scene == 'document':
            return host + '/static/images/pdf-item-thumbnail.png'
    return host + '/static/images/video-item-thumbnail.png'

def get_schedule_out_of_date(target, type=''):
    now_date = datetime.datetime.today()
    if type == 'last-update':
        updated_at = target.updated_at
        if(updated_at):
            if updated_at.year == now_date.year:
                return updated_at.strftime("%-m/%-d (%a) %H:%M")
            return updated_at.strftime("%y/%-m/%-d (%a) %H:%M")
        return ''
    schedule_date = target.schedule_date
    if(schedule_date):
        if now_date > schedule_date:
            if not bool(target.movie):
                return 'まもなくリリース'
            return False
        elif schedule_date.year == now_date.year:
            return schedule_date .strftime("%-m/%-d (%a) %H:%M")
        return schedule_date .strftime("%y/%-m/%-d (%a) %H:%M")
    return False


def get_date_and_time(date, type):
    if not date:
        return ''
    if type == 'date':
        return date.strftime('%Y/%m/%d')
    if type == 'time':
        return date.strftime('%H:%M')
    return date.strftime('%Y/%m/%d - %H:%M')
        

def check_color_take(index):
    if int(index) in range(1,4):
        return index
    if int(index) % 3 != 0:
        return int(index) % 3
    return 3


def get_date_detail_for_select_take(date, type):
    date1 = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    date2 = date.replace(hour=0, minute=0, second=0, microsecond=0)
    if type == 'day-less':
        if date1.year != date2.year:
            return date.strftime('%y/%-m/')
        else:
            return ''
    if type == 'day':
        return date1.day
    if type == 'time':
        if date1.day == date2.day and date1.month == date2.month and date1.year == date2.year:
            return date.strftime('%H:%M')
        return date.strftime('%a')
    return get_updated_time(date)

def get_list_variant(scene, type=''):
    if not scene:
        return ''
    list_id = ''
    list_id = list_id + str(scene.pk)

    other_version = scene.other_versions.all()
    if other_version:
        for ver in other_version:
            list_id = list_id + ', ' + str(ver.pk)
    if type == 'list':
        return list_id.split(', ')
    return str(list_id)

def check_show_list_take(scenes, type=''):
    if len(scenes) == 1 and len(scenes.first().other_versions.all()) == 0:
        return False
    if type == 'take_check' and len(scenes) == 1:
        return False
    return True


def sizeof_fmt(num, suffix="b"):
    for unit in ("", "k", "m", "g", "t", "p", "e", "z"):
        if abs(num) < 1024.0:
            return f"{num:3.1f}{unit}{suffix}"
        num /= 1024.0
    return f"{num:.1f}Yi{suffix}"


def get_total_file_in_folder(folder):
    if folder is None:
        return 0
    total_files = folder.children.all().count()
    for child_folder in folder.child_folders.all():
        total_files += get_total_file_in_folder(child_folder)
    return total_files


def get_size_pdf(pdf_url):
    try:
        response = requests.get(pdf_url)
        if response.status_code == 200:
            pdf_data = BytesIO(response.content)
            pdf_reader = PdfReader(pdf_data)
            # pages = pdf_reader.pages
            first_page = pdf_reader.pages[0]
            page_width = first_page.mediabox[2]
            page_height = first_page.mediabox[3]
            return str(int(page_width)) + ' x ' + str(int(page_height)) + 'px'
        else:
            print(f'Failed to download the PDF from the URL.')
            return None
    except Exception as e:
        print("Cannot get info of pdf: ", e)
        return None


def get_image_pdf_file(url_pdf):
    try:
        response = requests.get(url_pdf)
        pdf_data = response.content
        images = convert_from_bytes(pdf_data, first_page=1, last_page=1)

        if images:
            buffered = BytesIO()
            images[0].save(buffered, format="PNG")
            image_base64 = base64.b64encode(buffered.getvalue()).decode()
            image_url = f"data:image/png;base64,{image_base64}"
            return image_url
        else:
            print("Page not found.")
            return None
    except Exception as e:
        print('cannot get pdf file: ', e)
        return None

def parse_json_message_info(info, type):
    try:
        if info is None:
            return None
        json_data = json.loads(info)
        if type == 'image' or type == 'document':
            result = {
                'width': json_data['width'],
                'height': json_data['height']
            }
            if type == 'document':
                result['url_image'] = json_data['url_image']

            return result
        elif type == 'audio':
            result = {
                'sample_rate': f"{round(int(json_data['sample_rate']) / 1000)}K" if json_data['sample_rate'] else '',
                'bit_depth': f"{json_data['bit_depth']}bit" if json_data['bit_depth'] else '',
                'loudness': f"{json_data['loudness']}LUFS" if json_data['loudness'] else '',
            }
            if json_data['channel_type']:
                result['channel_type'] = "mono" if int(json_data['channel_type']) == 1 else "stereo"
            else:
                result['channel_type'] = ''
            return result
        elif type == 'video':
            # is video file
            return {
                'width': json_data['width'],
                'height': json_data['height'],
                'fps': json_data['fps']
            }
        else:
            # other file
            return {
                'size': json_data['size'],
                'size_converted': json_data['size_converted'],
            }
    except Exception as e:
        print('error parse_json_message_info: ', e)
        return None


def show_user_seen_max(is_pc_device):
    if is_pc_device:
        return 7
    return 4


def random_number(a, b):
    return random.randint(a, b)



def get_presigned_url_message(file_name):
    try:
        from django.conf import settings
        import boto3

        s3 = boto3.client('s3', aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                          aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)

        import urllib.parse
        url = s3.generate_presigned_url(
            ClientMethod='get_object',
            Params={
                'Bucket': settings.AWS_STORAGE_BUCKET_NAME,
                'Key': file_name,
            }
        )
        return url
    except Exception as e:
        logging.info(f'create file message failed: {e.args}')
        return None

def master_admin_get_offer_role(offer):
    if offer.type == "2" and offer.admin.role == AuthUser.MASTERCLIENT and offer.admin != offer.creator:
        return "プロデューサー"
    else:
        return offer.custom_contract_dsp()


def get_data_offer_by_role(role, offer):
    if 'role' in role:
        if role["role"] == 'creator':
            return get_data_offer_by_role_creator(offer, role["offer_id"])
        elif role["role"] == 'admin':
            return get_data_offer_by_role_admin(offer, role["offer_id"]) 
    else:
        if role == 'creator':
            return get_data_offer_by_role_creator(offer)
        elif role == 'admin':
            return get_data_offer_by_role_admin(offer)
    return None


# nguoi nhan offer
def get_data_offer_by_role_creator(offer, offer_id=None):
    review = offer.review_admin
    if offer_id:
        review = ReviewOffer.objects.filter(Q(offer_id=str(offer_id).replace("-", "")) & Q(user_id=str(offer.creator.pk)))
    result = {}
    offer_status = offer.status
    number_of_day = number_of_day_to_deadline(offer.deadline)
    if offer_status == '1':
        result['color'] = '#009ace'
        result['text'] = 'オファーを確認 '
        return result
    if offer_status == '2':
        result['color'] = '#009ace'
        if number_of_day == 0:
            result['text'] = '本日納期'
        elif number_of_day < 0:
            result['text'] = f'納期を{abs(number_of_day)}日超過'
        else:
            result['text'] = f'納期まであと{number_of_day}日'
        return result
    elif offer_status == '3':
        result['color'] = '#a7a8a9'
        result['text'] = '検収待ち'
        return result
    elif offer_status == '4':
        # sau khi nghiem thu
        result['color'] = '#a7a8a9'
        result['text'] = 'レビューを書く'
        if review:
            # sau khi review
            result['text'] = 'お疲れさまでした'
        return result
    else:
        return None


# nguoi gui offer
def get_data_offer_by_role_admin(offer, offer_id=None):
    review = offer.review_admin
    if offer_id:
        review = ReviewOffer.objects.filter(Q(offer_id=str(offer_id).replace("-", "")) & Q(user_id=offer.admin.pk))
    result = {}
    offer_status = offer.status
    number_of_day = number_of_day_to_deadline(offer.deadline)
    if offer_status == '1':
        result['color'] = '#a7a8a9'
        result['text'] = 'オファー済み'
        return result
    if offer_status == '2':
        result['color'] = '#a7a8a9'
        if number_of_day == 0:
            result['text'] = '本日納期'
        elif number_of_day < 0:
            result['color'] = '#009ace'
            result['text'] = f'納期を{abs(number_of_day)}日超過'
        else:
            result['text'] = f'納期まであと{number_of_day}日'
        return result
    elif offer_status == '3':
        result['color'] = '#009ace'
        result['text'] = '検収しよう '
        return result
    elif offer_status == '4':
        # sau khi nghiem thu
        result['color'] = '#a7a8a9'
        result['text'] = 'レビューを書く'
        if review:
            # sau khi review
            result['text'] = 'お疲れさまでした'
        return result
    else:
        return None


def get_data_project_order(role, offer):
    print('1111111')
    if role == 'master_client':
        return get_data_project_order_by_owner(offer)
    elif role in ['master_admin', 'admin']:
        return get_data_project_order_by_role(offer)
    else:
        return None

# order nguoi gui
def get_data_project_order_by_owner(offer):
    offer_status = offer.condition
    number_of_day = number_of_day_to_deadline(offer.deadline)
    current_heart = offer.project.get_current_heart_rate()
    current_date = datetime.datetime.now()
    result = {}
    print('222222')
    if offer_status == '1':
        result['color'] = '#a7a8a9'
        result['text'] = '見積り待ち'
        return result
    elif offer_status == '8':
        result['color'] = '#009ace'
        result['text'] = '見積から注文'
        return result
    elif offer_status == '9':
        result['color'] = '#a7a8a9'
        if number_of_day == 0:
            result['text'] = '本日納期'
        elif number_of_day < 0:
            result['text'] = ''
        else:
            result['text'] = f'納期まであと{number_of_day}日'
        return result
    elif offer_status == '2':
        result['color'] = '#009ace'
        result['text'] = '契約を締結'
        return result
    elif offer_status == '3':
        result['color'] = '#a7a8a9'
        if number_of_day == 0:
            result['text'] = '本日納期'
        elif number_of_day < 0:
            result['color'] = '#009ace'
            result['text'] = f'納期を{abs(number_of_day)}日超過'
        else:
            result['text'] = f'納期まであと{number_of_day}日'
        if current_heart < 100 and offer.deadline and current_date > offer.deadline:
            result['color'] = '#009ace'
            result['text'] = f'納期を{abs(number_of_day)}日超過'
        elif current_heart == 100:
            result['color'] = '#009ace'
            result['text'] = '検収する'
        return result
    elif offer_status == '4':
        result['color'] = '#a7a8a9'
        result['text'] = '請求待ち'
        return result
    elif offer_status == '5':
        result['color'] = '#009ace'
        result['text'] = '支払い'
        return result
    elif offer_status == '6':
        result['color'] = '#a7a8a9'
        result['text'] = 'お疲れさまでした'
        return result
    else:
        return None

# order nguoi nhan
def get_data_project_order_by_role(offer):
    offer_status = offer.condition
    number_of_day = number_of_day_to_deadline(offer.deadline)
    current_heart = offer.project.get_current_heart_rate()
    current_date = datetime.datetime.now()
    result = {}
    print('33333')
    if offer_status == '1':
        result['color'] = '#009ace'
        result['text'] = '見積書を提示'
        return result
    elif offer_status == '8':
        result['color'] = '#a7a8a9'
        result['text'] = '注文待ち'
        return result
    elif offer_status == '9':
        result['color'] = '#009ace'
        result['text'] = '契約書を提示'
        return result
    elif offer_status == '2':
        result['color'] = '#009ace'
        if number_of_day == 0:
            result['text'] = '本日納期'
        elif number_of_day < 0:
            result['text'] = ''
        else:
            result['text'] = f'納期まであと{number_of_day}日'
        return result
    elif offer_status == '3':
        result['color'] = '#009ace'
        if number_of_day == 0:
            result['text'] = '本日納期'
        elif number_of_day < 0:
            result['text'] = f'納期を{abs(number_of_day)}日超過'
        else:
            result['text'] = f'納期まであと{number_of_day}日'
        if current_heart < 100 and offer.deadline and current_date > offer.deadline:
            result['color'] = '#009ACE'
            result['text'] = f'納期を{number_of_day}日超過'
            if number_of_day < 0:
                result['color'] = '#009ACE'
                result['text'] = f'納期を{abs(number_of_day)}日超過'
        elif current_heart == 100:
            result['color'] = '#a7a8a9'
            result['text'] = '検収待ち'
        return result
    elif offer_status == '4':
        result['color'] = '#009ace'
        result['text'] = '請求書を提出'
        return result
    elif offer_status == '5':
        result['color'] = '#a7a8a9'
        result['text'] = '支払い待ち'
        return result
    elif offer_status == '6':
        result['color'] = '#a7a8a9'
        result['text'] = 'お疲れさまでした'
        return result
    else:
        return None


def number_of_day_to_deadline(deadline):
    if deadline:
        current_day = datetime.datetime.now() - datetime.timedelta(days=1)
        return (deadline - current_day).days
    return None

def number_of_day_to_deadline2(deadline):
    if deadline:
        current_day = datetime.datetime.now() - datetime.timedelta(days=1)
        return (current_day - deadline).days
    return None



register.filter('check_show_list_take', check_show_list_take)
register.filter('get_list_variant', get_list_variant)
register.filter('get_date_detail_for_select_take', get_date_detail_for_select_take)
register.filter('check_color_take', check_color_take)
register.filter('get_thumbnail_take', get_thumbnail_take)
register.filter('get_date_and_time', get_date_and_time)
register.filter('get_schedule_out_of_date', get_schedule_out_of_date)
register.filter('check_exist_message', check_exist_message)
register.filter('get_contract_note', get_contract_note)
register.filter('get_value_exists', get_value_exists)
register.filter('get_list_contract_type', get_list_contract_type)
register.filter('get_all_skills', get_all_skills)
register.filter('get_all_scene_name_by_project', get_all_scene_name_by_project)
register.filter('get_allow_public_val', get_allow_public_val)
register.filter('master_admin_get_offer_role', master_admin_get_offer_role)


def class_name(item):
    try:
        return item.__class__.__name__
    except:
        return ''

register.filter('class_name', class_name)
register.filter('compare_now_with_valid_date', compare_now_with_valid_date)
register.filter('get_default_deadline_from_offer', get_default_deadline_from_offer)
register.filter('check_exist_data_on_string', check_exist_data_on_string)
register.filter('check_show_contact', check_show_contact)
register.filter('check_show_sign_in', check_show_sign_in)
register.filter('get_data_array_index', get_data_array_index)
register.filter('create_option_component_select', create_option_component_select)
register.filter('get_name_by_value_from_array_object', get_name_by_value_from_array_object)
register.filter('check_show_action_topic', check_show_action_topic)
register.filter('is_bookmarked_sale', is_bookmarked_sale)
register.filter('is_show_icon_payment_done_task', is_show_icon_payment_done_task)
register.filter('get_type_file_header', get_type_file_header)
register.filter('render_position_in_pdf', render_position_in_pdf)
register.filter('render_title_in_pdf', render_title_in_pdf)
register.filter('get_stage_name_in_modal', get_stage_name_in_modal)
register.filter('get_new_sale', get_new_sale)
register.filter('get_thumbnail_sale_content_selection', get_thumbnail_sale_content_selection)
register.filter('get_sale_contents_in_selection', get_sale_contents_in_selection)
register.filter('get_artist_of_sale_content', get_artist_of_sale_content)
register.filter('get_link_to_preview_file', get_link_to_preview_file)
register.filter('round_usage_fee', round_usage_fee)
register.filter('count_offer_done', count_offer_done)
register.filter('get_money_offer_done', get_money_offer_done)
register.filter('format_date_jp', format_date_jp)
register.filter('format_deadline_with_weekday', format_deadline_with_weekday)
register.filter('format_short_date', format_short_date)
register.filter('is_editable_director', is_editable_director)
register.filter('is_editable_project', is_editable_project)
register.filter('check_editable_director', check_editable_director)
register.filter('display_currency', display_currency)
register.filter('calculate_after_tax_payment', calculate_after_tax_payment)
register.filter('is_editable_description', is_editable_description)
register.filter('get_items_new', get_items_new)
register.filter('get_user_seen', get_user_seen)
register.filter('function_count', function_count)
register.filter('get_list_user_download', get_list_user_download)
register.filter('get_list_user_download_refactor', get_list_user_download_refactor)
register.filter('count_list_user_download', count_list_user_download)
register.filter('get_list', get_list)
register.filter('product_scene_title', product_scene_title)
register.filter('filter_scense_act', filter_scense_act)
register.filter('filter_user', filter_user)
register.filter('check_product', check_product)
register.filter('get_title_mail', get_title_mail)
register.filter('filter_user_quarter', filter_user_quarter)
register.filter('check_product_quarter', check_product_quarter)
register.filter('check_project_status', check_project_status)
register.filter('get_title_mail_quarter', get_title_mail_quarter)
register.filter('get_width_for_thumbnail', get_width_for_thumbnail)
register.filter('get_weekday', get_weekday)
register.filter('get_weekday_new', get_weekday_new)
register.filter('get_avatar', get_avatar)
register.filter('get_image', get_image)
register.filter('get_thumbnail', get_thumbnail)
register.filter('get_updated_time', get_updated_time)
register.filter('to_now', to_now)
register.filter('check_side', check_side)
register.filter('is_seen', is_seen)
register.filter('new_message_count', new_message_count)
register.filter('new_message_count_refactor', new_message_count_refactor)
register.filter('get_file_url', get_file_url)
register.filter('get_name', get_name)
register.filter('get_values_list', get_values_list)
register.filter('get_pin_video', get_pin_video)
register.filter('get_full_name', get_full_name)
register.filter('check_user', check_user)
register.filter('get_postion', get_postion)
register.filter('get_display_fullname', get_display_fullname)
register.filter('find_other_user_id', find_other_user_id)
register.filter('hide_if_empty', hide_if_empty)
register.filter('count_new_update_scene_title', count_new_update_scene_title)
register.filter('count_process_scene_title', count_process_scene_title)
register.filter('count_done_scenetitle_without_productionfile', count_done_scenetitle_without_productionfile)
register.filter('list_ip_product_user', list_ip_product_user)
register.filter('view_only', view_only)
register.filter('get_seen', get_seen)
register.filter('get_child_comments', get_child_comments)
register.filter('get_first_version_id', get_first_version_id)
register.filter('check_member_invited', check_member_invited)
register.filter('minus', minus)
register.filter('get_last_version_thumbnail', get_last_version_thumbnail)
register.filter('is_product_seen', is_product_seen)
register.filter('can_upload_plan', can_upload_plan)
register.filter('get_variation', get_variation)
register.filter('check_null', check_null)
register.filter('get_updated_datetime', get_updated_datetime)
register.filter('get_product_user', get_product_user)
register.filter('check_notification_product', check_notification_product)
register.filter('check_avt', check_avt)
register.filter('get_member', get_member)
register.filter('count_undownload_product_comment', count_undownload_product_comment)
register.filter('check_product_scene_has_scene', check_product_scene_has_scene)
register.filter('get_variation_choosed', get_variation_choosed)
register.filter('is_project_seen', is_project_seen)
register.filter('project_not_file', project_not_file)
register.filter('project_done', project_done)
register.filter('count_new_message', count_new_message)
register.filter('get_owners_project', get_owners_project)
register.filter('get_admin_project', get_admin_project)
register.filter('get_current_budget_admin', get_current_budget_admin)
register.filter('is_message_for_old_version', is_message_for_old_version)
register.filter('add_param', add_param)
register.filter('get_icon_offer', get_icon_offer)
register.filter('get_offer_url', get_offer_url)
register.filter('list_viewers_seen', list_viewers_seen)
register.filter('list_latest_seen_message', list_latest_seen_message)
register.filter('get_last_offer_message', get_last_offer_message)
register.filter('get_banner_creator', get_banner_creator)
register.filter('check_avt_creator', check_avt_creator)
register.filter('get_audio_name', get_audio_name)
register.filter('get_link', get_link)
register.filter('check_new', check_new)
register.filter('check_new_sale_content', check_new_sale_content)
register.filter('get_thumbnail_sale_content', get_thumbnail_sale_content)
register.filter('get_hash_tag_in_sale_content', get_hash_tag_in_sale_content)
register.filter('get_audio', get_audio)
register.filter('get_audios', get_audios)
register.filter('get_creator_type', get_creator_type)
register.filter('get_description_sale_content', get_description_sale_content)
register.filter('get_type_sale_content', get_type_sale_content)
register.filter('get_title_sale_content', get_title_sale_content)
register.filter('get_price_sale_content', get_price_sale_content)
register.filter('get_type_text_sale_content', get_type_text_sale_content)
register.filter('get_public_audios', get_public_audios)
register.filter('get_modified_time', get_modified_time)
register.filter('get_content_type', get_content_type)
register.filter('get_song_attr1_min', get_song_attr1_min)
register.filter('get_song_attr2_min', get_song_attr2_min)
register.filter('get_song_attr1_max', get_song_attr1_max)
register.filter('get_song_attr2_max', get_song_attr2_max)
register.filter('get_default_color', get_default_color)
register.filter('get_auction_start_time', get_auction_start_time)
register.filter('get_auction_end_time', get_auction_end_time)
register.filter('get_auction_price', get_auction_price)
register.filter('get_auction_max_price', get_auction_max_price)
register.filter('get_sale_type_text', get_sale_type_text)
register.filter('get_position', get_position)
register.filter('get_enterprise', get_enterprise)
register.filter('get_budget', get_budget)
register.filter('get_current_budget_cost', get_current_budget_cost)
register.filter('get_money_spent', get_money_spent)
register.filter('get_budget_admins', get_budget_admins)
register.filter('get_current_budget_creator', get_current_budget_creator)
register.filter('get_done_budget_creator', get_done_budget_creator)
register.filter('get_undone_budget_creator', get_undone_budget_creator)
register.filter('get_datetime', get_datetime)
register.filter('get_short_datetime', get_short_datetime)
register.filter('get_reward', get_reward)
register.filter('get_owner_name', get_owner_name)
register.filter('get_full_address', get_full_address)
register.filter('get_budget_offer', get_budget_offer)
register.filter('get_budget_for_admin', get_budget_for_admin)
register.filter('get_budget_admin_offer_done', get_budget_admin_offer_done)
register.filter('get_budget_admin_offer', get_budget_admin_offer)
register.filter('get_current_spending_budget_admin', get_current_spending_budget_admin)
register.filter('get_current_paid_budget_admin', get_current_paid_budget_admin)
register.filter('get_admin_budget', get_admin_budget)
register.filter('get_scene_name_offer', get_scene_name_offer)
register.filter('get_infor_user', get_infor_user)
register.filter('get_user_url', get_user_url)
register.filter('get_url_offer', get_url_offer)
register.filter('count_ip_product_user', count_ip_product_user)
register.filter('get_last_time_update_project', get_last_time_update_project)
register.filter('check_has_audio_file', check_has_audio_file)
register.filter('get_infor_file', get_infor_file)
register.filter('get_class_video_by_with', get_class_video_by_with)
register.filter('get_class_border_video_by_with', get_class_border_video_by_with)
register.filter('get_content_comment_parent', get_content_comment_parent)
register.filter('generate_star', generate_star)
register.filter('project_number_vote', project_number_vote)
register.filter('get_rating', get_rating)
register.filter('get_list_ip', get_list_ip)
register.filter('get_object_product_comment', get_object_product_comment)
register.filter('get_message_dict_files', get_message_dict_files)


def render_video_with_hls(file_obj, **kwargs):
    """
    Render video element with HLS support if MediaConvert conversion is available
    Args:
        file_obj: File object (Scene, SceneCommentFile, ProductCommentFile, etc.)
        **kwargs: Additional attributes for video tag (width, height, poster, etc.)
    Returns:
        Safe HTML string with video element
    """
    from django.utils.safestring import mark_safe
    from app.models import MediaConvertJob
    import html

    # Get file path
    if hasattr(file_obj, 'movie') and file_obj.movie:
        file_path = str(file_obj.movie.name)
        file_url = file_obj.movie.url
    elif hasattr(file_obj, 'file') and file_obj.file:
        file_path = str(file_obj.file.name)
        file_url = file_obj.file.url
    else:
        return mark_safe('')

    # Check for MediaConvert job
    mediaconvert_job = MediaConvertJob.get_by_file_path(file_path)

    # Build video attributes, escaping values properly
    attrs = []
    video_id = kwargs.get('id', f'video_{abs(hash(file_path))}')

    for key, value in kwargs.items():
        if value and value != '':
            # Handle special attributes
            if key == 'controls' and value == 'controls':
                attrs.append('controls')
            elif key == 'data-setup' and value:
                attrs.append(f'{key}=\'{html.escape(str(value))}\'')
            else:
                attrs.append(f'{key}="{html.escape(str(value))}"')

    if mediaconvert_job and mediaconvert_job.is_completed() and mediaconvert_job.converted_media_key:
        # Use HLS if available
        from django.conf import settings
        # Use AWS S3 URL format
        hls_url = f"https://{settings.AWS_STORAGE_BUCKET_NAME}.s3.{settings.AWS_S3_HOST.replace('s3.', '')}/{mediaconvert_job.converted_media_key}"

        video_html = f'''<video {' '.join(attrs)}
               data-video-src="{html.escape(hls_url)}"
               data-is-hls="true"
               data-original-src="{html.escape(file_url)}">
        </video>
        <script>
        document.addEventListener('DOMContentLoaded', function() {{
            const video = document.getElementById('{video_id}');
            if (video && typeof initializeHLSVideo === 'function') {{
                initializeHLSVideo(video);
            }}
        }});
        </script>'''
    else:
        # Fallback to original video
        video_html = f'''<video {' '.join(attrs)}>
            <source src="{html.escape(file_url)}" type="video/mp4">
        </video>'''

    return mark_safe(video_html)


register.filter('render_video_with_hls', render_video_with_hls)
register.filter('get_sorted_message_files', get_sorted_message_files)
register.filter('get_message_file_folder', get_message_file_folder)
register.filter('round_rating', round_rating)
register.filter('make_list', make_list)
register.filter('get_url_scene', get_url_scene)
register.filter('get_authorization_user', get_authorization_user)
register.filter('get_path_creator', get_path_creator)
register.filter('get_product_id', get_product_id)
register.filter('check_child_folder', check_child_folder)
register.filter('check_child_folder_refactor', check_child_folder_refactor)
register.filter('count_unread_offer_message_count', count_unread_offer_message_count)
register.filter('count_unread_message', count_unread_message)
register.filter('get_member_inviting', get_member_inviting)
register.filter('count_batch_number_project', count_batch_number_project)
register.filter('get_description_show_profile', get_description_show_profile)
register.filter('option_time', option_time)
register.filter('deleteable', deleteable)
register.filter('get_other_member_in_project', get_other_member_in_project)
register.filter('get_updated_full_time', get_updated_full_time)
register.filter('get_range_date', get_range_date)
register.filter('get_deadline_full_time', get_deadline_full_time)
register.filter('get_last_message_in_offer', get_last_message_in_offer)
register.filter('get_task_in_progress', get_task_in_progress)
register.filter('get_schedule_list_info', get_schedule_list_info)
register.filter('get_system_message', get_system_message)
register.filter('get_last_message_title', get_last_message_title)
register.filter('get_last_message_title_refactor', get_last_message_title_refactor)
register.filter('has_artist_to_add_director', has_artist_to_add_director)
register.filter('check_offer_project_status', check_offer_project_status)
register.filter('get_file_type', get_file_type)
register.filter('get_created_year', get_created_year)
register.filter('get_credit', get_credit)
register.filter('get_hashtag', get_hashtag)
register.filter('get_show_thumbnail', get_show_thumbnail)
register.filter('get_customizable_sale_setting', get_customizable_sale_setting)
register.filter('ratio_mileage', ratio_mileage)
register.filter('get_artists_in_mileage', get_artists_in_mileage)
register.filter('count_all_artists', count_all_artists)
register.filter('get_current_point_rate', get_current_point_rate)
register.filter('get_usage_fee_in_setting', get_usage_fee_in_setting)
register.filter('get_usage_fee_next_rank_for_user_to_show', get_usage_fee_next_rank_for_user_to_show)
register.filter('get_button_in_top_menu', get_button_in_top_menu)
register.filter('get_thumbnail_sale_content_url', get_thumbnail_sale_content_url)
register.filter('get_weekday_new_2', get_weekday_new_2)
register.filter('get_total_file_in_folder', get_total_file_in_folder)
register.filter('get_size_pdf', get_size_pdf)
register.filter('get_image_pdf_file', get_image_pdf_file)
register.filter('parse_json_message_info', parse_json_message_info)
register.filter('show_user_seen_max', show_user_seen_max)
register.filter('random_number', random_number)
register.filter('get_presigned_url_message', get_presigned_url_message)
register.filter('has_sale_youtube_link', has_sale_youtube_link)
register.filter('get_sale_youtube_link', get_sale_youtube_link)
register.filter('get_range_date_format', get_range_date_format)
register.filter('get_updated_time2', get_updated_time2)
register.filter('get_data_offer_by_role', get_data_offer_by_role)
register.filter('get_data_project_order', get_data_project_order)

def get_sorted_folder_files(folder):
    """フォルダ内のファイルを名前順でソートして返す"""
    return folder.children.all().order_by('real_name')

def get_sorted_child_folders(folder):
    """フォルダ内のサブフォルダを名前順でソートして返す"""
    return folder.child_folders.all().order_by('name')

def get_message_files_count(message):
    """メッセージに関連付けられたファイルの数を返す"""
    return message.files.all().count()

register.filter('get_sorted_folder_files', get_sorted_folder_files)
register.filter('get_sorted_child_folders', get_sorted_child_folders)
register.filter('get_message_files_count', get_message_files_count)