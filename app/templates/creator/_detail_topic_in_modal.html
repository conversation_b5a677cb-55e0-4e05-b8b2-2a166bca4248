{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load i18n %}

<div class="popup-body">
  <div class="topic-container">
    <div class="topic-container-content topic-container-content__left mscrollbar">
      <div class="topic-content__media">
        {% if topic.video %}
          {% render_video_with_hls topic width="100%" height="100%" poster=topic.thumbnail.url preload="auto" id="modal_topic_video_"|add:topic.pk as video_html %}
          {% if video_html %}
            {{ video_html }}
          {% else %}
            <video width="100%" height="100%"
                   poster="{% if topic.thumbnail %}{{ topic.thumbnail.url }}{% endif %}"
                   preload="auto">
              <source src="{{ topic.video.url }}"
                      type="video/mp4"/>
            </video>
          {% endif %}
        {% else %}
          <img src="{% if topic.thumbnail %}{{ topic.thumbnail.url }}{% endif %}" alt="">
        {% endif %}
      </div>
      <div class="topic-content__title">
        <span>{{ topic.title }}</span>
      </div>

      {#            Category #}
      <div class="topic-content__sub-title">
        {% for category in topic.categories.all %}
          <span>{{ category.category_name }}</span>
        {% endfor %}
      </div>

{#      <div class="topic-content__description">#}
{#        {{ topic.description }}#}
{#      </div>#}
      <div class="topic-content__description"><div>{{ topic.overview }}</div><div>{{ topic.description }}</div>
      </div>
      <div class="topic-content__action-dowload">
        {% if topic.file %}
            <div class="btn btn--secondary btn-download-topic-file">{% trans "Download materials" %}</div>
        {% endif %}
      </div>
      <div class="topic-content__hashtag">
        {% for tag in topic.tags.all %}
          <span>#{{ tag.tag_name }}</span>
        {% endfor %}
      </div>
    </div>
    <div class="topic-container-content topic-container-content__right mscrollbar">
      {#          <form>#}

      {% for selection in topic.selections.all %}
        <div class="section-container" data-selection="{{ selection.pk }}">
          <div class="section-content__title">
            {{ selection.title }}
          </div>
          <div class="section-content__sub-title">
            {{ selection.description }}
          </div>

          {#              Show sale content #}

          <div class="section-content__list-media mscrollbar">
            {% for sale_content_selection in selection.salecontentselection_set.all %}
              {% with sale_content_selection.sale_content as sale_content %}
                {% with sale_content|get_artist_of_sale_content as artist %}
                  {% for album_variation in sale_content|get_audios:user %}
                    {% with album_variation|get_file_type:user as file_type %}
                      <div class="list-circle__component {% if file_type != 'audio' %}btn-preview-album{% endif%}"
                          data-type="{{ sale_content.last_published_version.content_type }}"
                          data-artist="{{ artist.get_display_name }}"
                          process-data="0"
                          process-status="pause">
                        <div class="section-content_sub-meida gallery__item list-circle__sub-component"
                            data-type="{{ sale_content.last_published_version.content_type }}"
                            style="{{ sale_content_selection|get_thumbnail_sale_content_selection }}"
                            data-artist="{{ artist.get_display_name }}" data-sale="{{ sale_content.pk }}">
                          <div class="list-search__item-playpause opacity-0"></div>
                          <audio preload="none"
                                class="gallery__item-banner"
                                src="{{ album_variation|get_audio:user }}"
                                data-name="{{ sale_content|get_title_sale_content:user }}"
                                data-album="{{ album_variation.pk }}"
                                data-file-type="{{ file_type }}"></audio>
                        </div>

                        <div class="section-content__title-artist hide">
                          <div class="item-sale-content-name">{{ sale_content|get_title_sale_content:user }}</div>
                          <div class="item-artist-name">{{ artist.get_display_name }}</div>
                        </div>
                      </div>
                    {% endwith %}
                  {% endfor %}
                {% endwith %}
              {% endwith %}
            {% endfor %}
          </div>

          {#              Show selections#}

          <div class="section-content__radio {% if not create_offer %}disabledbutton{% endif %}">
            {% for option in selection.get_selection_content %}
              <label class="input-radio">
                <input type="radio" name="content-{{ selection.pk }}"
                       value=""
                       index="" required="true"
                       {% if option.status == 'on' %}checked{% endif %}/><span class="detail-radio">{{ option.detail }}</span>
                <div class="check-mark"></div>
              </label>
            {% endfor %}
          </div>

          {#                Show toggle #}
          <div>
            {% for option in selection.get_toggle_content %}
              <div class="section-content__toggle {% if not create_offer %}disabledbutton{% endif %}">
                <div class="form-check custom-switch">
                  <label class="form-check-label">
                    <div class="form-check-group">
                                        <span>
                                            <input class="form-check-input switch-checkbox switch-toggle-account"
                                                   type="checkbox" name="switch-toggle-account"
                                                   {% if option.status == 'on' %}checked{% endif %}><span
                                                class="switch-slider"></span>
                                        </span>
                    </div>
                    <span style="margin-left: 10px;" class="toggle-text-topic">{{ option.detail }}</span>
                  </label>
                </div>
              </div>
            {% endfor %}
          </div>
        </div>
      {% endfor %}

      {#          </form>#}
    </div>
  </div>
</div>
{% if create_offer %}
  <div class="popup-footer" style="text-align: left; padding: 24px 0; border-top: 1px solid #f0f0f0;">
    <button type="button" class="btn btn--tertiary" data-dismiss="modal"
            aria-label="Close">{% trans "return" %}</button>
    <button type="button" class="btn btn--primary btn-popup-send" id="btn__next_1">{% trans "to the next" %}</button>
  </div>
{% else %}
  <div class="popup-footer" style="text-align: left; padding: 24px 0; border-top: 1px solid #f0f0f0;">
    <button type="button" class="btn btn--tertiary" data-dismiss="modal"
            aria-label="Close">{% trans "return" %}</button>
  </div>
{% endif %}
