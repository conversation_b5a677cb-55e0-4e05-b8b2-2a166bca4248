{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load util %}
{% load widget_tweaks %}
{% load static compress %}

{% block extrahead %}
    {% compress css %}
    <style>
        textarea.form-control {
            height: 60px;
        }

        .comments {
            width: 100%;
        }

        .button_summit{
            border: 0;
            background: 0;
            outline: none;
        }
        .movieblock {
            padding: 10px;
            border-bottom: 1px solid #999;
        }

        .massenger__video {
            background-color: white;
            border: none;
        }

        video {
            width: 100%;
        }

        .massenger__config-number {
            bottom: 12px;
        }
        .massenger__videodone .star.hover{
            color: #f0ea0c;
        }
        .massenger__videoicon.ic-null .null .hover {
            color: #e50914;
        }

        a {
            text-decoration: none !important;
        }
        .comment__pintime-time:hover{
            color: #009ace;
        }
        a[aria-disabled="true"] {
            color: currentColor;
            display: inline-block;  /* For IE11/ MS Edge bug */
            pointer-events: none;
            text-decoration: none;
        }
    </style>
    {% endcompress %}
{% endblock %}

{% block content %}
    <main class="massenger">
        <div class="banner">
            <div class="banner__img">
                <a class="link-project" href="{% url 'app:scene_index' %}?product_id={{ product.pk }}">
                   <img src="{{ product|get_image }}" alt="">
                </a>
                <div class="action">
                    {% if user.role != 'client' %}
                        <a href="{% url 'app:product_order' product.pk %}">
                            <button class="action__order" type="button" aria-hidden="true">ORDER</button>
                        </a>
                    {% endif %}

                    {% if product.information %}
                        <a href="{% url 'app:product_information' product.pk %}">
                            <button class="action__detail" type="button" aria-hidden="true">詳細</button>
                        </a>
                    {% endif %}
                </div>
            </div>

            <div class="progressbar">
                <div class="progress">
                    <div class="progress-bar bg-success" style="width: {{ product.get_current_heart_rate }}%"></div>
                    <div class="progress-bar bg-warning" style="width: {{ product.get_current_scene_rate }}%"></div>
                </div>
                <span class="progressbar__title">{{ product.get_current_heart_rate }}%</span>
            </div>
        </div>
        <div class="massenger__content">
            <div class="container">
                <div class="massenger__header">
                    <a class="massenger__header-icon disable-click">
                        <i class="fas fa-caret-left"></i>
                    </a>

                    <div class="massenger__nav">
                        <div class="massenger__breadcrumbs">
                        <a href="{% url 'app:scene_index' %}?product_id={{ product.pk }}">{{ product_scene.name }}</a>
                        &gt; {{ scene_title.title }}
                    </div>

                    {% if user.role in "admin, master_admin" %}
                        <div class="setting-config">
                            <i class="fas fa-cog"></i>
                        </div>
                    {% endif %}
                    </div>

                    <a class="massenger__header-icon massenger__header--icon-prev disable-click">
                        <i class="fas fa-caret-right"></i>
                    </a>
                </div>

                <div class="massenger__main">
                    {% for obj in object_list %}
                        <div class="row massenger__column">
                            <div class="col-md-6 col-sm-6">
                                <div class="massenger__left">
                                    <div class="massenger__video">
                                        {% if obj.check_tag != '2' %}
                                            <span class="massenger__video-addvideo">
                                                <a href="{% url 'app:scene_upload' %}?product_id={{ product.pk }}&product_scene_id={{ product_scene.pk }}&title_id={{ scene_title.pk }}&scene_id={{obj.pk}}">+</a>
                                            </span>
                                        {% endif %}

                                        <div class="carousel slide" id="videoCarousel-{{obj.scene_id}}" data-ride="carousel" data-interval="false">
                                            {% if obj.check_tag == '2' %}
                                                <ol class="carousel-indicators">
                                                    {% if obj.movie %}
                                                        <li class="{%if not obj.carousel_scene_list_index %}hide {% endif %}{% if obj.flag_tag %}disable-click{% else %}heart-active{% endif %}"
                                                            data-target="#videoCarousel-{{obj.scene_id}}" data-slide-to="0"
                                                            data-scene='{{obj.pk}}' data-fps-movie='{{obj.fps_movie}}'>a</li>
                                                    {% endif %}
                                                    {% for o in obj.carousel_scene_list_index %}
                                                        <li data-target="#videoCarousel-{{obj.scene_id}}"
                                                            data-slide-to="{% if obj.movie %}{{forloop.counter}}{%else%}{{forloop.counter0}}{%endif%}"
                                                            class="{% if obj.movie.name == '' and obj.carousel_scene_list_index|length == 1 %}hide {% endif %}{% if o.flag_tag %}disable-click{% else %}heart-active{%endif%}"
                                                            data-scene='{{o.pk}}' data-fps-movie='{{o.fps_movie}}'>
                                                                {{o.index}}
                                                        </li>
                                                    {% endfor %}
                                                </ol>
                                                <div class="carousel-inner">
                                                    {% if obj.movie %}
                                                        {% if not obj.flag_tag %}
                                                            <div class="item active">
                                                                <span class="text-center"
                                                                      id="new-{{ obj.pk }}"></span>
                                                        {% else%}
                                                            <div class="item">
                                                        {% endif %}
                                                            <video id="movie-{{ obj.pk }}" class="video-js" controls
                                                                   preload="automatic" data-scene="{{ obj.pk }}"
                                                                   poster="" data-setup="{}" onplay="playing(this)"
                                                                   onpause="pausing(this)"
                                                                   onseeked="seek(this, this.currentTime)"
                                                                   playsinline>
                                                                <source src="{{ obj.movie.url }}" type='video/mp4'>
                                                                <p class="vjs-no-js">
                                                                    To view this video please enable JavaScript, and consider upgrading to a
                                                                    web browser that
                                                                    <a href="http://videojs.com/html5-video-support/" target="_blank">
                                                                        supports HTML5 video
                                                                    </a>
                                                                </p>
                                                            </video>
                                                        </div>
                                                    {% endif %}
                                                    {% for o in obj.carousel_scene_list_index %}
                                                        {% if not o.flag_tag %}
                                                            <div class="item active">
                                                                <span class="text-center"
                                                                      id="new-{{ o.pk }}"></span>
                                                        {% else %}
                                                            <div class="item">
                                                        {% endif %}
                                                            <video id="movie-{{ o.pk }}" class="video-js" controls
                                                                   preload="automatic" data-scene="{{ o.pk }}"
                                                                   poster="" data-setup="{}" onplay="playing(this)"
                                                                   onpause="pausing(this)"
                                                                   onseeked="seek(this, this.currentTime)" playsinline>
                                                                <source src="{{ o.movie }}" type='video/mp4'>
                                                                <p class="vjs-no-js">
                                                                    To view this video please enable JavaScript, and consider upgrading to a
                                                                    web browser that
                                                                    <a href="http://videojs.com/html5-video-support/" target="_blank">
                                                                        supports HTML5 video
                                                                    </a>
                                                                </p>
                                                            </video>
                                                        </div>
                                                    {% endfor %}
                                                </div>
                                            {% else %}
                                                <ol class="carousel-indicators">
                                                    {% if obj.movie %}
                                                        <li class="{%if not obj.carousel_scene_list_index %}hide {% endif %} {% if not obj.have_child %}active{% endif %}"
                                                            data-target="#videoCarousel-{{obj.scene_id}}" data-slide-to="0"
                                                            data-scene='{{obj.pk}}' data-fps-movie='{{obj.fps_movie}}'>a</li>
                                                    {% endif %}
                                                    {% for o in obj.carousel_scene_list_index %}
                                                        <li data-target="#videoCarousel-{{obj.scene_id}}"
                                                            data-slide-to="{% if obj.movie %}{{forloop.counter}}{%else%}{{forloop.counter0}}{%endif%}"
                                                            class="{% if forloop.last %}active{%endif%}{% if obj.movie.name == '' and obj.carousel_scene_list_index|length == 1 %} hide{% endif %}"
                                                            data-scene='{{o.pk}}' data-fps-movie='{{o.fps_movie}}'>
                                                                {{o.index}}
                                                        </li>
                                                    {% endfor %}
                                                </ol>
                                                <div class="carousel-inner">
                                                    {% if obj.movie %}
                                                        <div class="item {% if not obj.have_child %}active{% endif %}">
                                                            <span class="text-center"
                                                                  id="new-{{ obj.pk }}"></span>
                                                            <video id="movie-{{ obj.pk }}" class="video-js" controls
                                                                   preload="automatic" data-scene="{{ obj.pk }}"
                                                                   poster="" data-setup="{}" onplay="playing(this)"
                                                                   onpause="pausing(this)"
                                                                   onseeked="seek(this)" playsinline>
                                                                <source src="{{ obj.movie.url }}" type='video/mp4'>
                                                                <p class="vjs-no-js">
                                                                    To view this video please enable JavaScript, and consider upgrading to a
                                                                    web browser that
                                                                    <a href="http://videojs.com/html5-video-support/" target="_blank">
                                                                        supports HTML5 video
                                                                    </a>
                                                                </p>
                                                            </video>
                                                        </div>
                                                    {% endif %}
                                                    {% for o in obj.carousel_scene_list_index %}
                                                        <div class="item {% if forloop.last %}active{% endif %}">
                                                            <span class="text-center"
                                                                  id="new-{{ o.pk }}"></span>
                                                            <video id="movie-{{ o.pk }}" class="video-js" controls
                                                                   preload="automatic" data-scene="{{ o.pk }}"
                                                                   poster="" data-setup="{}" onplay="playing(this)"
                                                                   onpause="pausing(this)"
                                                                   onseeked="seek(this, this.currentTime)" playsinline>
                                                                <source src="{{ o.movie }}" type='video/mp4'>
                                                                <p class="vjs-no-js">
                                                                    To view this video please enable JavaScript, and consider upgrading to a
                                                                    web browser that
                                                                    <a href="http://videojs.com/html5-video-support/" target="_blank">
                                                                        supports HTML5 video
                                                                    </a>
                                                                </p>
                                                            </video>
                                                        </div>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                        <div class="massenger__videodone">
                                            {% if not is_favorite %}
                                                <span class="videoicon link-icon {% if obj.check_tag == '2' %}ic-done{% else%}ic-null disable-click{% endif %}">
                                                    <i class="fas fa-heart icon done"></i>
                                                    <i class="far fa-heart icon half" style="display:none; color:red"></i>
                                                    <i class="far fa-heart icon null"></i>
                                                </span>
                                            {% else %}
                                                <a class="videoicon link-icon {% if obj.check_tag == '2' %}ic-done heart-active {%else%}ic-null{% endif %}"
                                                    href="javascript:void(0)"
                                                      onclick="{% if is_favorite %}updateTag(this){% else %}return true{% endif %}"
                                                      data-tag='{{obj.check_tag}}'>
                                                    <i class="fas fa-heart icon done"></i>
                                                    <i class="far fa-heart icon half" style="display:none; color:red"></i>
                                                    <i class="far fa-heart icon null"></i>
                                                </a>
                                            {% endif %}
                                            {% if is_rating %}
                                                <span class="massenger__videodone-rate" value='{{obj.owner_rating_videos.first.rating}}' style='font-size:25px'>
                                                    <ul class="stars">
                                                        {% for i in "1"|rjust:"5"%}
                                                        <li class="star {% if forloop.counter <= obj.owner_rating_videos.first.rating %}selected{%endif%}" data-value="{{forloop.counter}}">
                                                            <i class="{% if forloop.counter <= obj.owner_rating_videos.first.rating %}fas{%else%}far{%endif%} fa-star "></i>
                                                        </li>
                                                        {% endfor%}
                                                    </ul>
                                                </span>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="massenger__taskbar hidden">
                                        <div class="massenger__taskbar-content">
                                            <div class="massenger__taskbar-item"></div>
                                            <div class="massenger__taskbar-pin" style="left: 30%">
                                                <a href="javascript:void(0)">
                                                    <i class="fas fa-thumbtack"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="massenger__config">
                                        <div class="massenger__config-content">
                                            <div class="massenger__config-box">
                                                {% if not forloop.first %}
                                                    <form method="post" action="{% url 'app:scene_order' obj.pk %}"
                                                          enctype="multipart/form-data">
                                                        {% csrf_token %}
                                                        <input name="type" value="1" type="hidden">
                                                        <div class="massenger__config-up">
                                                            <a href="javascript:void(0)" onclick="submit(this)">
                                                                <i class="fas fa-caret-up"></i>
                                                            </a>
                                                        </div>
                                                    </form>
                                                {% endif %}

                                                <div class="massenger__config-hr"></div>

                                                {% if not forloop.last %}
                                                    <form method="post" action="{% url 'app:scene_order' obj.pk %}"
                                                          enctype="multipart/form-data">
                                                        {% csrf_token %}
                                                        <input name="type" value="2" type="hidden">
                                                        <div class="massenger__config-down">
                                                            <a href="javascript:void(0)" onclick="submit(this)">
                                                                <i class="fas fa-caret-down"></i>
                                                            </a>
                                                        </div>
                                                    </form>
                                                {% endif %}
                                            </div>

                                            <div class="massenger__config-number">
                                                <span>#{{ forloop.counter }}</span>
                                            </div>
                                            <div class="massenger__config-edit">
                                                <a class="massenger__config-edit-link" href="#"
                                                   onclick="editScene(this)">
                                                    <i class="fas fa-cog"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal fade" id="editThumbModal_{{ forloop.counter }}" role="dialog">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <button class="close close_edit" data-dismiss="modal" type="button">&times;</button>
                                                </div>
                                                <div class="modal-body">
                                                    <label for="id_thumbnail">サムネイル</label>
                                                        <input type="file" id="id_thumbnail" name="thumbnail" accept=".jpg, .png, .jpeg, image/*">
                                                    <img class="edit-thumb__content" style="max-width: 30vw;max-height: 30vh;"></img>
                                                    <div class="edit-thumb__submit">
                                                        <button class="btn button button--submit button-edit">OK
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-sm-6">
                                <div class="comment">
                                    <div class="comment__form">
                                        {% with obj.scene_comment_scene.first.created_en as comment_time %}
                                            {% for comment in obj.scene_comment_scene.all %}
                                                {% if forloop.counter == 1 %}
                                                    <div class="comment__date">
                                                        <span>{{ comment.created_en }}</span>
                                                    </div>
                                                {% elif comment_time != comment.created_en %}
                                                    <div class="comment__date">
                                                        <span>{{ comment.created_en }}</span>
                                                    </div>
                                                    {% define comment.created_en as comment_time %}
                                                {% endif %}
                                                {% if comment.user.role|check_side:user.role %}
                                                    <div class="comment__reply">
                                                        <div class="comment__reply-bgr">
                                                            <div class="comment__reply-right">
                                                                <div class="comment__reply-view">
                                                                    <ul class="comment__reply-list unstyled">
                                                                        {% if comment.preview_comment.count > 3 %}
                                                                            {% for pre in comment.preview_comment|order_by:"-created" %}
                                                                                {% if forloop.counter <= 2 %}
                                                                                    <li class="comment__view-item">
                                                                                        <img src="
                                                                                            {% if pre.owner.avatar %}{{ pre.owner|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                                            alt="" title="{{pre.owner}}">
                                                                                    </li>
                                                                                {% endif %}
                                                                            {% endfor %}

                                                                             <li class="comment__view-item comment__view-item--add">
                                                                                <a class="dropdown-link" data-toggle="dropdown">
                                                                                    <span>+{{ comment.preview_comment.count|add:"-2" }}</span>
                                                                                </a>
                                                                                <ul class="comment__view-dropdown dropdown-menu unstyled">
                                                                                    {% for pre in comment.preview_comment|order_by:"-created" %}
                                                                                        {% if forloop.counter > 2 %}
                                                                                            <li class="dropdown-item">
                                                                                                <a href="#">
                                                                                                    <img src="
                                                                                                         {% if pre.owner.avatar %}{{ pre.owner|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                                                         alt="" title="{{pre.owner}}">
                                                                                                    <p class="comment__view-name">{{ pre.owner }}</p>
                                                                                                </a>
                                                                                            </li>
                                                                                        {% endif %}
                                                                                    {% endfor %}
                                                                                </ul>
                                                                             </li>

                                                                        {% else %}
                                                                            {% for pre in comment.preview_comment|order_by:"-created" %}
                                                                                {% if forloop.counter <= 5 %}
                                                                                    <li class="comment__view-item">
                                                                                        <img src="
                                                                                            {% if pre.owner.avatar %}{{ pre.owner|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                                            alt="" title="{{pre.owner}}">
                                                                                    </li>
                                                                                {% endif %}
                                                                            {% endfor %}
                                                                        {% endif %}
                                                                    </ul>
                                                                </div>
                                                                {% if comment.stamp %}
                                                                    <div class="comment__reply-content wd-auto" style="border: none; margin-bottom: 0px">
                                                                        <div class="comment__emoticon">
                                                                          <img src="{{comment.comment}}" alt="">
                                                                        </div>
                                                                        {% if comment.user == user %}
                                                                            <div class="comment__reply-setting">
                                                                                <div class="comment__reply-link">
                                                                                    <span class="delete-emoticon">...</span>
                                                                                    <a href="{% url 'app:scene_comment_delete' comment.pk %}"
                                                                                       class="delete-emoticon-trash fa-sm hide">
                                                                                        <i class="far fa-trash-alt"
                                                                                           style="background: #757575;color: white;padding: 4px 5px;border-radius: 50%;">
                                                                                        </i>
                                                                                    </a>
                                                                                </div>
                                                                            </div>
                                                                        {% endif %}
                                                                    </div>
                                                                {% else %}
                                                                    <div class="comment__reply-content">
                                                                        {% if comment.check_pin_time == 1 %}
                                                                            <div class="comment__pintime comment__reply-pintime"
                                                                                onclick="seekToTime('{{ obj.pk }}', '{{ comment.pin_time }}', '{{comment.pin_video}}', this)">
                                                                                <i class="fas fa-thumbtack"></i>
                                                                                <a href="javascript:void(0)" class="comment__pintime-time">{{ comment.pin_time }}</a>
                                                                            </div>
                                                                        {% elif comment.check_pin_time == 2 %}
                                                                            <div class="comment__top-pin">
                                                                                <div class="comment__pintime comment__reply-pintime {% if obj.check_tag == '2'%}{% if obj.audio_pin == comment.pk|slugify %}fixed-item{% else %}disabled-item{% endif %}{% else %}pin-{{ comment.pin_video }}{% endif %}"
                                                                                     onclick="seekToTime('{{ obj.pk }}', '{{ comment.pin_time }}', '{{comment.pin_video}}', this)">
                                                                                    <i class="fas fa-play-circle"></i>
                                                                                    <i class="fas fa-pause-circle hide"></i>
                                                                                    <a class="comment__pintime-time" href="javascript:void(0)">{% if not comment.pin_time %}0:00.00{% else %}{{ comment.pin_time }}{% endif %}</a>
                                                                                    <div class="comment__download">
                                                                                        <a class="comment__download-icon-down" value="{{ comment.file.url }}" href="javascript:void(0)">{{ comment.get_file_name }}</a>
                                                                                        <div class="hide">
                                                                                            <audio src="{{ comment.file.url }}"
                                                                                                   id="{{ comment.pk }}"
                                                                                                   oncanplaythrough="canplaythrough('{{ obj.pk }}', '{{ comment.pin_video }}', '{{ comment.pin_time }}', '{{ obj.fps_movie }}', this)"
                                                                                                   onended="end_audio(this)"
                                                                                                   controls></audio>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>

                                                                                <a class="comment__download-top" target="_blank" href="{{ comment.file.url }}">
                                                                                    <i class="fas fa-download" data-comment-id="{{ comment.pk }}"></i>
                                                                                </a>
                                                                            </div>
                                                                        {% endif %}
                                                                        <div class="comment__reply-desc">
                                                                            {{ comment.comment | pin_time_comment:comment.pin_time | linebreaks }}
                                                                        </div>

                                                                        <div class="comment__reply-setting">
                                                                            {% if comment.user == user %}
                                                                                <div class="comment__reply-link">
                                                                                    <span>...</span>
                                                                                </div>
                                                                            {% endif %}
                                                                            <div class="comment__reply-edit-box">
                                                                                <div class="comment__reply-edit"><a
                                                                                     href="{% url 'app:scene_comment_change' comment.pk %}"><i
                                                                                     class="fas fa-pencil-alt"> </i></a></div>
                                                                                <div class="comment__reply-remove"><a
                                                                                     href="{% url 'app:scene_comment_delete' comment.pk %}"><i
                                                                                     class="far fa-trash-alt"></i></a></div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                {% endif %}
                                                            </div>
                                                            <div class="comment__reply-avatar">
                                                                <img src="{% if comment.user.avatar %}{{ comment.user|get_avatar:'small' }}{% else %} {% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                     alt="" title="{{ comment.user }}">
                                                            </div>
                                                        </div>
                                                        {% if comment.file and comment.check_pin_time != 2 %}
                                                            <div class="comment__download comment__download--reply">
                                                                <i class="fas fa-download" data-comment-id="{{ comment.pk }}"></i>
                                                                <a class="comment__download-icon-down" href="{{ comment.file.url }}">{{ comment.get_file_name }}</a>
                                                            </div>
                                                        {% endif %}
                                                    </div>
                                                {% else %}
                                                    <div class="comment__item">
                                                        <div class="comment__item-bgr">
                                                            <div class="comment__avatar">
                                                                <img src="{% if comment.user.avatar %}{{ comment.user|get_avatar:'small' }}{% else %} {% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                     alt="" title="{{ comment.user }}">
                                                            </div>
                                                            <div class="comment__item-right {% if comment.stamp %}wd-auto{%endif%}">
                                                                {% if comment.stamp %}
                                                                    <div class="comment__emoticon">
                                                                      <img src="{{comment.comment}}" alt="">
                                                                    </div>
                                                                {% else %}
                                                                    <div class="comment__content">
                                                                        {% if comment.check_pin_time == 1 %}
                                                                            <div class="comment__pintime"
                                                                                onclick="seekToTime('{{ obj.pk }}', '{{ comment.pin_time }}', '{{comment.pin_video}}', this)">
                                                                                <i class="fas fa-thumbtack"></i>
                                                                                <a href="javascript:void(0)" class="comment__pintime-time">{{ comment.pin_time }}</a>
                                                                            </div>
                                                                        {% elif comment.check_pin_time == 2 %}
                                                                            <div class="comment__top-pin">
                                                                                <div class="comment__pintime {% if obj.check_tag == '2'%}{% if obj.audio_pin == comment.pk|slugify  %}fixed-item{% else %}disabled-item{% endif %}{% else %}pin-{{ comment.pin_video }}{% endif %}"
                                                                                     onclick="seekToTime('{{ obj.pk }}', '{{ comment.pin_time }}', '{{comment.pin_video}}', this)">
                                                                                    <i class="fas fa-play-circle"></i>
                                                                                    <i class="fas fa-pause-circle hide"></i>
                                                                                    <a class="comment__pintime-time" href="javascript:void(0)">{{ comment.pin_time }}</a>
                                                                                    <div class="comment__download">
                                                                                        <a class="comment__download-icon-down" value="{{ comment.file.url }}" href="javascript:void(0)">{{ comment.get_file_name }}</a>
                                                                                        <div class="hide">
                                                                                            <audio src="{{ comment.file.url }}"
                                                                                                   id="{{ comment.pk }}"
                                                                                                   oncanplaythrough="canplaythrough('{{ obj.pk }}', '{{ comment.pin_video }}', '{{ comment.pin_time }}', '{{ obj.fps_movie }}', this)"
                                                                                                   onended="end_audio(this)"
                                                                                                   controls></audio>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>

                                                                                <a href="{{ comment.file.url }}"
                                                                                   target="_blank"
                                                                                   class="comment__download-top">
                                                                                    <i class="fas fa-download" data-comment-id = {{ comment.pk }}></i>
                                                                                </a>
                                                                            </div>
                                                                        {% endif %}
                                                                        <div class="comment__content-desc">
                                                                            {{ comment.comment | pin_time_comment:comment.pin_time | linebreaks }}
                                                                        </div>
                                                                    </div>
                                                                {% endif %}
                                                                <div class="comment__view">
                                                                    <ul class="comment__view-list unstyled">
                                                                     {% if comment.preview_comment.count > 3 %}
                                                                        {% for pre in comment.preview_comment|order_by:"-created" %}
                                                                            {% if forloop.counter <= 2 %}
                                                                                <li class="comment__view-item">
                                                                                    <img src="
                                                                                        {% if pre.owner.avatar %}{{ pre.owner|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                                        alt="" title="{{pre.owner}}">
                                                                                </li>
                                                                            {% endif %}
                                                                        {% endfor %}

                                                                         <li class="comment__view-item comment__view-item--add">
                                                                            <a class="dropdown-link" data-toggle="dropdown">
                                                                                <span>+{{ comment.preview_comment.count|add:"-2" }}</span>
                                                                            </a>
                                                                            <ul class="comment__view-dropdown dropdown-menu unstyled">
                                                                                {% for pre in comment.preview_comment|order_by:"-created" %}
                                                                                    {% if forloop.counter > 2 %}
                                                                                        <li class="dropdown-item">
                                                                                            <a href="#">
                                                                                                <img src="
                                                                                                     {% if pre.owner.avatar %}{{ pre.owner|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                                                     alt="" title="{{pre.owner}}">
                                                                                                <p class="comment__view-name">{{ pre.owner }}</p>
                                                                                            </a>
                                                                                        </li>
                                                                                    {% endif %}
                                                                                {% endfor %}
                                                                            </ul>
                                                                         </li>
                                                                    {% else %}
                                                                        {% for pre in comment.preview_comment|order_by:"-created" %}
                                                                            {% if forloop.counter <= 5 %}
                                                                                <li class="comment__view-item">
                                                                                    <img src="
                                                                                        {% if pre.owner.avatar %}{{ pre.owner|get_avatar:'small' }}{% else %}{% static 'images/avatar-user.jpg' %} {% endif %}"
                                                                                        alt="" title="{{pre.owner}}">
                                                                                </li>
                                                                            {% endif %}
                                                                        {% endfor %}
                                                                    {% endif %}
                                                                </ul>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        {% if comment.file and comment.check_pin_time != 2 %}
                                                            <div class="comment__download comment__download--bottom">
                                                                <i class="fas fa-download" data-comment-id="{{ comment.pk }}"></i>
                                                                <a class="comment__download-icon-down" href="{{ comment.file.url }}">{{ comment.get_file_name }}</a>
                                                            </div>
                                                        {% endif %}
                                                    </div>
                                                {% endif %}
                                            {% endfor %}
                                        {% endwith %}
                                        <div class="comment__textarea">
                                            <form method="post" action="{% url 'app:scene_comment' obj.pk %}"
                                                  enctype="multipart/form-data" class="comments">
                                                {% csrf_token %}
                                                {% with obj|get_comment_form:request.user.id as form %}
                                                    <div class="comment__textarea-item">
                                                        <div class="comment__textarea-link">
                                                            <a class="comment__textarea-upload"
                                                               onclick="pinComment('{{ obj.pk }}', this)"
                                                               href="javascript:void(0)">
                                                                <i class="fas fa-thumbtack"></i>
                                                            </a>
                                                            <label class="comment__textarea-pin"
                                                                   style="cursor: pointer;">
                                                                <i class="fas fa-paperclip"></i>
                                                                <input type="file" class="hidden file-{{obj.pk}}" name="file" id="id_file">
                                                            </label>
                                                        </div>
                                                        <textarea name="comment" cols="40" rows="1"
                                                                  class="textarea movie-{{ obj.pk }} comment__textarea-box"
                                                                  required maxlength="255"></textarea>
                                                        <a class="comment__textarea-emoticon" href="#">
                                                            <i class="far fa-smile"></i>
                                                        </a>
                                                        {{ form.user}}{{ form.owner_id }}
                                                        <input type="hidden" name="pin_time" class="pin-{{ obj.pk }}"
                                                               maxlength="20">
                                                        <input type="hidden" name="pin_video" class="pin_video-{{ obj.pk }}"
                                                               maxlength="20">
                                                        {{ form.scene }}
                                                        <div class="comment__textarea-submit">
                                                            <button type="submit" class="submit-link button_summit"  style="padding: 0" onclick="this.disabled=true,this.form.submit();">
                                                                <i class="fas fa-paper-plane"></i></button>
                                                        </div>
                                                        <div class="comment__textarea-emoticoninfo dropdown-menu">
                                                            <a class="comment__textarea-emoticonimg" href="#">
                                                                <img src="{% static 'images/heart.png' %}" alt="">
                                                            </a>
                                                            <a class="comment__textarea-emoticonimg" href="#">
                                                                <img src="{% static 'images/kasikomari.png' %}" alt="">
                                                            </a>
                                                            <a class="comment__textarea-emoticonimg" href="#">
                                                                <img src="{% static 'images/ryoukai.png' %}" alt="">
                                                            </a>
                                                            <a class="comment__textarea-emoticonimg" href="#">
                                                                <img src="{% static 'images/kakunin.png' %}" alt="">
                                                            </a>
                                                            <a class="comment__textarea-emoticonimg" href="#">
                                                                <img src="{% static 'images/odoroki.png' %}" alt="">
                                                            </a>
                                                            <a class="comment__textarea-emoticonimg" href="#">
                                                                <img src="{% static 'images/ikaga.png' %}" alt="">
                                                            </a>
                                                        </div>
                                                    </div>
                                                {% endwith %}
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <div class="row massenger__column">
                    <div class="col-md-4">
                        <div class="massenger__add">
                            <a class="massenger__add-link" href="{% url 'app:scene_upload' %}?product_id={{ product.pk }}&product_scene_id={{ product_scene.pk }}&title_id={{ scene_title.pk }}">
                                <i class="fa fa-plus"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% include 'scene/js/list.html' %}
{% endblock content %}
