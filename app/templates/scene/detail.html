{% extends "base.html" %}
{% load bootstrap3 %}
{% load util %}

{% block title %}{% endblock title %}

{% block extrahead %}
    <link href="http://vjs.zencdn.net/6.4.0/video-js.css" rel="stylesheet">
    <style>
    </style>
    <script src="http://vjs.zencdn.net/6.4.0/video.min.js"></script>
{% endblock %}

{% block content %}
    <main class="row p10 text-center">
        <div class="col-xs-12">
            <video id="movie{{ scene.pk }}" class="video-js auto" controls preload="auto" width="640" height="264"
                   poster="" data-setup="{}">
                <source src="{{ scene.movie.url }}" type='video/mp4'>
                <p class="vjs-no-js">
                    To view this video please enable JavaScript, and consider upgrading to a web browser that
                    <a href="http://videojs.com/html5-video-support/" target="_blank">supports HTML5 video</a>
                </p>
            </video>
            <div class="m10">
                {% if user.is_staff or obj.owner_id == user.id %}
                    <a class="btn btn-default btn-xs" href="{% url 'app:scene_change' scene.pk %}">動画更新</a>
                {% endif %}
                <a class="btn btn-default btn-xs" href="{% url 'app:scene_comment' scene.pk %}">コメント投稿</a>
            </div>
        </div>
    </main>
    <div class="m10 row">
        <div class="col-xs-6">
            <form method="post" action="{% url 'app:scene_comment' scene.pk %}" enctype="multipart/form-data">
                {% csrf_token %}
                {% bootstrap_form form %}
                {% buttons %}
                    <input type="submit" value="登録" class="btn btn-primary" />
                {% endbuttons %}
            </form>
        </div>
    </div>
    <div class="text-left p10">
        {% for obj in object_list %}
            <div class="panel {% if obj.owner_id == user.id %}panel-success{% else %}panel-default{% endif %}">
                <div class="panel-heading">
                    {{ obj.created_str }}&nbsp;{{ obj.user }}
                </div>
                <div class="panel-body">
                    {{ obj.comment }}
                    <div class="text-right">
                        {% if obj.owner_id == user.id %}
                        <a class="btn btn-default btn-xs" href="{% url 'app:scene_comment_change' obj.pk %}">編集</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
    <div class="text-center">
        {% bootstrap_pagination page_obj extra=request.GET.urlencode %}
    </div>
{% endblock content %}
