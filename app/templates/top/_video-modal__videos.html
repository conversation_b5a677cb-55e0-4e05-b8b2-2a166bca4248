{% load util %}
<div class="video-modal__videos">
    <div class="project-item__video-list">
        <div class="project-video-item">
            <div class="video-item-wrap">
                <div class="video-item-list">
                    {% for scene in scenes %}
                        <div class="video-item-component {% if forloop.first %}active{% endif %}"
                             data-variation="{{ forloop.counter0 }}" data-variation-id="{{ scene.pk }}">
                            <div class="video-modal-component-content">
                                <div class="video-modal-component-content-video">
                                    {% with forloop.counter0 as parent_index %}
                                        {% for other_version in scene.other_versions.all %}
                                            <div class="video-modal-version-item {% if forloop.first %}version-active{% endif %}"
                                                 data-variation="{{ parent_index }}"
                                                 data-index="{{ forloop.revcounter }}"
                                                 data-max-version="{{ scene.other_versions.count|add:1 }}"
                                                 data-scene-id="{{ other_version.pk }}">
                                                <div class="video-modal-info-detail">
                                                    <div class="video-modal-breadcrumbs">
                                                        <span class="video-modal-chapter">{{ scene.title.product_scene.name }}</span>
                                                        <span>></span>
                                                        <span class="video-modal-scene">{{ scene.title.title }}</span>
                                                    </div>
                                                    <div class="video-modal-version-text">{{ other_version.get_file_name }}</div>
                                                </div>
                                                <div class="video-modal-version {% if not forloop.first %}gray-background{% endif %}">{{ forloop.revcounter|add:1 }}</div>
                                              {% with  other_version.is_audio_file as type_scene %}
                                                {% if type_scene in 'audio,video' %}
                                                  {% render_video_with_hls other_version width="auto" height="90%" crossOrigin="anonymous" poster=other_version|get_thumbnail|add_param preload="none" controls="controls" id="modal_video_"|add:other_version.pk as video_html %}
                                                  {% if video_html %}
                                                    {{ video_html }}
                                                  {% else %}
                                                    <video width="auto" height="90%" crossOrigin="anonymous"
                                                           poster="{{ other_version|get_thumbnail|add_param }}"
                                                           preload="none" controls>
                                                      <source src="{{ other_version.movie.url|add_param }}"
                                                          type="video/mp4">
                                                    </video>
                                                  {% endif %}

                                                {% elif type_scene == 'document' %}
                                                  <iframe width="100%" height="400px" class="scrollbar"
                                                          src="{{ other_version.movie.url }}"></iframe>
                                                {% endif %}
                                              {% endwith %}
                                            </div>
                                        {% endfor %}
                                        <div class="video-modal-version-item {% if scene.other_versions.count == 0 %}version-active{% endif %}"
                                             data-variation="{{ parent_index }}"
                                             data-index="0"
                                             data-max-version="{{ scene.other_versions.count|add:1 }}"
                                             data-scene-id="{{ scene.pk }}">
                                            <div class="video-modal-info-detail">
                                                <div class="video-modal-breadcrumbs">
                                                    <span class="video-modal-chapter">{{ scene.title.product_scene.name }}</span>
                                                    <span>></span>
                                                    <span class="video-modal-scene">{{ scene.title.title }}</span>
                                                </div>
                                                <div class="video-modal-version-text">{{ scene.get_file_name }}</div>
                                            </div>
                                            <div class="video-modal-version {% if scene.other_versions.count != 0 %}gray-background{% endif %}">1</div>
                                          {% with  scene.is_audio_file as type_scene %}
                                            {% if type_scene in 'video, audio' %}
                                              {% render_video_with_hls scene width="auto" height="90%" crossOrigin="anonymous" poster=scene|get_thumbnail|add_param preload="none" controls="controls" id="modal_video_main_"|add:scene.pk as video_html %}
                                              {% if video_html %}
                                                {{ video_html }}
                                              {% else %}
                                                <video width="auto" height="90%" crossOrigin="anonymous"
                                                       poster="{{ scene|get_thumbnail|add_param }}" preload="none"
                                                       controls>
                                                  <source src="{{ scene.movie.url|add_param }}" type="video/mp4">
                                                </video>
                                              {% endif %}

                                            {% elif type_scene == 'document' %}
                                              <iframe width="100%" height="400px" class="scrollbar"
                                                      src="{{ scene.movie.url }}"></iframe>

                                            {% endif %}
                                          {% endwith %}
                                        </div>
                                    {% endwith %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
                <div class="video-item-control">
                    <div class="video-item-bullets-wrap">
                        <div class="video-item-bullets">
                            <div class="video-item-bullet-prev disable" data-current_variation="0"></div>
                            <div class="video-item-bullet-list">
                                {% for scene in scenes %}
                                    <div class="video-item-bullet {% if forloop.counter0 == 0 %}active{% endif %}"
                                         data-variation="{{ forloop.counter0 }}"></div>
                                {% endfor %}
                            </div>
                            <div class="video-item-bullet-next" data-current_variation="0"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
