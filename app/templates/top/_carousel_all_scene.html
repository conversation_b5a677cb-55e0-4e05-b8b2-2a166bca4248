{% load static %}
{% load util compress %}

{% block extrahead %}
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,400,0,0" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,<PERSON><PERSON><PERSON>@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@24,400,0,0" />
    {% compress css %}
        {% if request.resolver_match.url_name == 'scene_title_detail' %}
          <link rel="stylesheet" href="{% static 'css/soremo_style_2024.css' %}" />
            <link rel="stylesheet" type="text/css" href="{% static 'css/scene_detail/scene_detail_new.css' %}"/>
        {% else %}
            <link rel="stylesheet" type="text/css" href="{% static 'css/scene_detail_new.css' %}"/>
            <link rel="stylesheet" href="{% static 'css/soremo_style_2024.css' %}" />
        {% endif %}
    {% endcompress %}
{% endblock %}


<div class="cscene cscene--video cscene--video--new mg-bt-video-new">
    <div class="cscene-heading d-none" style="display: none">
        <div class="cscene-heading__product sheading sheading--16"
             data-product-scene-id="{{ scene.title.product_scene.pk }}">
            {{ scene.title.product_scene.name }}
        </div>
        <div class="cscene-heading__title">
            {{ scene.title.title }}
        </div>
    </div>
    <div class="cscene-meta">
        <div class="cscene-variation" style="height: 0;">
            <div class="sselect-wrapper select-white hide">
                <select class="select" id="variation-select"
                        placeholder="Variation" data-search="false"
                        data-search-text="Enter the keyword">
                    {% for scene in scenes %}
                        <option value="{{ forloop.counter0 }}">
                            {% if scene.other_versions.exists %}
                                {{ scene.other_versions.first.get_file_name }}
                                ({{ forloop.counter0|add:1 }}/{{ scenes.count }})
                            {% else %}
                                {{ scene.get_file_name }}
                                ({{ forloop.counter0|add:1 }}/{{ scenes.count }})
                            {% endif %}
                        </option>
                    {% endfor %}
                </select>
            </div>
        </div>
        {#    <div class="cscene-meta__date">{{ scene.other_versions.first.created|get_updated_time }}</div>#}
    </div>
    <div class="cscene-horizontal">
        {% for scene in scenes %}
            <div class="cscene__variation" data-scene-id="{{ scene.pk }}"
                 data-index="{{ forloop.counter0 }}">
                <div class="cscene-vertical dfgdfgfg">
                    <div class="cscene__version" data-scene-id="{{ scene.pk }}"
                         data-index="{{ scene.other_versions.count }}">
                        {% with scene.is_audio_file as type_scene %}
                            {% if type_scene == 'audio' %}
                                <div class="cscene__version-horizontal ccscene__thumb scene-type-audio">
                                    <div class="mmessenger mmessenger--audio-wave mmessenger--gray">
                                        <div class="messenger-content d-flex flex-direction-column">
                                            <div class="s-audio s-audio--audio-wave s-audio--white audio-pc u-relative"
                                                 data-scene-id="{{ scene.pk }}">
                                                <div class="s-audio-source"
                                                     data-link="{{ scene.movie.url }}"
                                                     title="{{ scene.real_name }}"
                                                     data-waveColor="#d3d3d3"
                                                     data-progressColor="#53565a"></div>
                                                <div class="s-audio-waveform"></div>
                                                <div class="s-audio-time new-position"></div>
                                                <div class="s-audio-time-total"></div>
                                            </div>
                                            <div class="audio-control-custom">
                                                <a href="javascript:void(0);"
                                                   class="backward-audio">
                                                    <span class="material-symbols-rounded load-replay">replay_5</span>
                                                </a>
                                                <div class="s-audio-control pin-time-audio nav-play-sp">
                                                    <span class="material-symbols-rounded c-icon-play-audio material-symbol-play">
                                                        play_circle
                                                      </span>
                                                      <span class="material-symbols-rounded c-icon-pause-audio material-symbol-pause">pause</span>
                                                </div>
                                                <a href="javascript:void(0);" class="forward-audio">
                                                    <span class="material-symbols-rounded load-replay">forward_5</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% elif type_scene == 'document' %}
                                <div class="cscene__version-horizontal cscene__version-horizontal-document ccscene__thumb scene-type-document">
                                    <iframe width="100%" height="100%" class="scrollbar pdf-component"
                                            src="/static/pdfjs/web/viewer.html?file={{ scene.movie.url|urlencode }}#zoom=page-width">
                                    </iframe>
                                </div>
                            {% elif type_scene == 'video' %}
                                <div class="{{ scene|get_class_border_video_by_with }} ccscene__thumb scene-type-video"
                                     style="width: 100%;">
                                    {#             <div class="{{ scene|get_class_border_video_by_with }} ccscene__thumb">#}
                                    <video poster="{{ scene|get_thumbnail }}"
                                           style="border-radius: 12px"
                                           preload="metadata"
                                           data-width="{{ scene.video_width }}"
                                           data-height="{{ scene.video_height }}"
                                           data-ratio="{{ scene.video_width|calculate_ratio:scene.video_height }}"
                                           class="{{ scene|get_class_video_by_with }}"
                                           controls>
                                        <source src="{{ scene.movie.url }}" type="video/mp4">
                                    </video>
                                </div>
                            {% elif type_scene == 'image' %}
                                <div class="{{ scene|get_class_border_video_by_with }} ccscene__thumb scene-type-video"
                                     style="width: 100%;">
                                    <video poster="{{ scene|get_thumbnail }}"
                                           style="border-radius: 12px"
                                           preload="metadata"
                                           data-width="{{ scene.video_width }}"
                                           data-height="{{ scene.video_height }}"
                                           data-ratio="{{ scene.video_width|calculate_ratio:scene.video_height }}"
                                           class="{{ scene|get_class_video_by_with }}">
                                    </video>
                                </div>
                            {% endif %}
                        {% endwith %}
                    </div>

                    {% for other_version in scene.other_versions.all %}
                        <div class="cscene__version"
                             data-scene-id="{{ other_version.pk }}"
                             data-index="{{ forloop.counter0 }}">
                            {% with other_version.is_audio_file as type_scene %}
                                {% if type_scene == 'audio' %}
                                    <div class="cscene__version-horizontal ccscene__thumb scene-type-audio">
                                        <div class="mmessenger mmessenger--audio-wave mmessenger--gray">
                                            <div class="messenger-content d-flex flex-direction-column">
                                                <div class="s-audio s-audio--audio-wave s-audio--white audio-pc u-relative"
                                                     data-scene-id="{{ other_version.pk }}">
                                                    <div class="s-audio-source"
                                                         data-link="{{ other_version.movie.url }}"
                                                         title="{{ other_version.real_name }}"
                                                         data-waveColor="#d3d3d3"
                                                         data-progressColor="#53565a">
                                                    </div>
                                                    <div class="s-audio-waveform"></div>
                                                    <div class="s-audio-time new-position"></div>
                                                    <div class="s-audio-time-total"></div>
                                                </div>
                                                <div class="audio-control-custom">
                                                    <a href="javascript:void(0);" class="backward-audio">
                                                        <span class="material-symbols-rounded load-replay">replay_5</span>
                                                    </a>
                                                    <div class="s-audio-control pin-time-audio nav-play-sp">
                                                        <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                                                            play_circle
                                                        </span>
                                                        <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                                                    </div>
                                                    <a href="javascript:void(0);" class="forward-audio">
                                                        <span class="material-symbols-rounded load-replay">forward_5</span>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% elif type_scene == 'document' %}
                                    <div class="cscene__version-horizontal cscene__version-horizontal-document ccscene__thumb scene-type-document">
                                        <iframe width="100%" height="100%" class="scrollbar pdf-component"
                                                src="/static/pdfjs/web/viewer.html?file={{ other_version.movie.url|urlencode }}#zoom=page-width">
                                        </iframe>
                                    </div>
                                {% elif type_scene == 'video' %}
                                    <div class="{{ other_version|get_class_border_video_by_with }} ccscene__thumb scene-type-video"
                                         style="width: 100%;">
                                        {#                      <div class="{{ other_version|get_class_border_video_by_with }} ccscene__thumb">#}
                                        <video poster="{{ other_version|get_thumbnail }}"
                                               style="border-radius: 12px"
                                               preload="metadata"
                                               data-width="{{ other_version.video_width }}"
                                               data-height="{{ other_version.video_height }}"
                                               data-ratio="{{ other_version.video_width|calculate_ratio:other_version.video_height }}"
                                               class="{{ other_version|get_class_video_by_with }}"
                                               controls>
                                            <source src="{{ other_version.movie.url }}" type="video/mp4">
                                        </video>
                                    </div>
                                {% elif type_scene == 'image' %}
                                    <div class="{{ other_version|get_class_border_video_by_with }} ccscene__thumb scene-type-video"
                                         style="width: 100%;">
                                        <video poster="{{ other_version|get_thumbnail }}"
                                               style="border-radius: 12px"
                                               preload="metadata"
                                               data-width="{{ other_version.video_width }}"
                                               data-height="{{ other_version.video_height }}"
                                               data-ratio="{{ other_version.video_width|calculate_ratio:other_version.video_height }}"
                                               class="{{ other_version|get_class_video_by_with }}">
                                        </video>
                                    </div>
                                {% endif %}
                            {% endwith %}
                        </div>
                    {% endfor %}
                </div>
                <div class="cscene-vertical-dots hide">
                    <div class="cscene__version-dot hide" data-date="{{ scene.created|get_updated_time }}"
                         data-scene_id="{{ scene.pk }}" data-index="{{ scene.other_versions.count }}">
                    </div>

                    {% for other_version in scene.other_versions.all %}
                        <div class="cscene__version-dot hide" data-date="{{ other_version.created|get_updated_time }}"
                             data-scene_id="{{ other_version.pk }}" data-index="{{ forloop.counter0 }}">
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endfor %}
    </div>
    <div class="cscene-horizontal-dots-wrap">
        <div class="cscene-horizontal-dots">
            {% for scene in scenes %}
                <div class="cscene__variation-dot hide" data-index="{{ forloop.counter0 }}">
                </div>
            {% endfor %}
        </div>

    </div>
</div>

{% if scenes|check_show_list_take %}
    <div class="list-variation mscrollbar" {% if selected_take %} data-selected-take="{{ selected_take }}" {% endif %}>
        <div class="block-list-variation">
            {% with scene.title.last_version.pk as last_version_pk %}
                {% for scene in scenes %}
                    <div class="list-variation-container take-{{ forloop.counter0 }} hide"
                         data-take="{{ forloop.counter0 }}"
                         data-scene-id="{{ scene.pk }}"
                         data-list-variant-id="{{ scene|get_list_variant }}">
                        <div class="list-scene-horizontal" style="width: 100%;">
                            <div class="variation-button-container {% if scene.pk == last_version_pk %}active{% endif %}"
                                 data-variant="0"
                                 data-take="{{ forloop.counter0 }}"
                                 data-scene-id="{{ scene.pk }}">

                                {% if scene|get_thumbnail:'detail-scene' and scene.movie.name %}
                                    <img src="{{ scene|get_thumbnail:'detail-scene' }}" 
                                         alt="{{ scene.real_name }}" 
                                         style="height: 80px; width: 100%; border-radius: 6px;">
                                {% else %}
                                    <div class="variation-button-name txt-variation">
                                        {{ scene.real_name }}
                                    </div>
                                {% endif %}

                                {% if scene.real_name and scene|get_thumbnail:'detail-scene' %}
                                    <div class="variation-button-name-tooltip-container">
                                        <div class="variation-button-name-tooltip-content txt-variation">
                                            {{ scene.real_name }}
                                        </div>
                                    </div>
                                {% endif %}
                            </div>

                            {% for other_version in scene.other_versions.all %}
                                <div class="variation-button-container {% if other_version.pk == last_version_pk %}active{% endif %}"
                                     data-variant="{{ forloop.counter }}"
                                     data-take="{{ forloop.parentloop.counter0 }}"
                                     data-scene-id="{{ other_version.pk }}">

                                    {% if other_version|get_thumbnail:'detail-scene' and other_version.movie.name %}
                                        <img src="{{ other_version|get_thumbnail:'detail-scene' }}" 
                                             alt="{{ other_version.real_name }}" 
                                             style="height: 80px; width: 100%; border-radius: 6px;">
                                    {% else %}
                                        <div class="variation-button-name txt-variation">
                                            {{ other_version.real_name }}
                                        </div>
                                    {% endif %}

                                    {% if other_version.real_name and other_version|get_thumbnail:'detail-scene' %}
                                        <div class="variation-button-name-tooltip-container">
                                            <div class="variation-button-name-tooltip-content txt-variation">
                                                {{ other_version.real_name }}
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>

                        <div class="take-detail-container{% if not scenes|check_show_list_take:'take_check' %} hide{% endif %} take-color-{{ forloop.counter|check_color_take }}"
                             data-index-take="{{ forloop.counter }}">
                            <div class="take-detail-text">テイク</div>
                            <div class="take-detail-number">
                                {{ forloop.counter }}
                            </div>
                        </div>
                    </div>
                {% endfor %}
            {% endwith %}
        </div>
    </div>

    <div class="select-take-back"></div>
    <div class="choose-take-overlay mscrollbar choose-take-overlay-hidden">
        {% for scene in scenes %}
            <div class="take-overlay-container"
                 data-index="{{ forloop.counter0 }}"
                 data-list-variant-id="{{ scene|get_list_variant }}">
                <div class="input-radio">
                    <input type="radio" name="take-select_{{ forloop.counter0 }}"
                            {#   {% if scene.title.last_version.pk|stringformat:"s" in scene|get_list_variant:'list' %}checked{% endif %} #}
                           value="public" index="0" required="True"
                           id="id_choose_take_{{ forloop.counter0 }}">
                    <div class="check-mark"></div>
                </div>

                <div class="take-detail-select-container">
                    {{ scene.created|get_weekday_new }}
                </div>

                <div class="take-name-container take-color-{{ forloop.counter|check_color_take }}">
                    {% if forloop.counter != scenes.count %}
                        <div class="arrow-container">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16"
                                 height="16" fill="currentColor" class="bi bi-arrow-up"
                                 viewBox="0 0 16 16">
                                <path fill-rule="evenodd"
                                      d="M8 15a.5.5 0 0 0 .5-.5V2.707l3.146 3.147a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V14.5a.5.5 0 0 0 .5.5z"/>
                            </svg>
                        </div>
                    {% endif %}

                    <div class="take-text"> テイク</div>
                    <div class="take-number">
                        {{ forloop.counter }}
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% endif %}

{% block extra_script %}
    {% compress js inline %}
        <script type="text/javascript" src="{% static 'js/lib/bootstrap.min.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/jquery.scrollintoview.min.js' %}"></script>
    {% endcompress %}
{% endblock %}