{% load static %}
{% load util %}

<div class="project-chapter-video-item ui-sortable-handle {% if deleted %} deleted{% endif %}" data-scene-title-id="{{ st.pk }}"
     data-modified="{{ st.modified|get_updated_datetime }}" data-status="{{ st.status }}"
     data-rating="{{ st.rating }}" data-title="{{ st.title }}">
  <div class="project-chapter-video-item-content" data-scene-id="{{ scene.pk }}" data-scene-title="{{ st.pk }}">

    {% render_video_with_hls scene width="auto" height="auto" poster=scene|get_thumbnail preload="none" id="process_video_"|add:scene.pk as video_html %}
      {% if video_html %}
        {{ video_html }}
      {% else %}
        <video width="auto" height="auto" poster="{{ scene|get_thumbnail }}" preload="none">
          <source src="{{ scene.movie.url }}" type="video/mp4">
        </video>
      {% endif %}
    {% endwith %}

    {% if is_done %}
      {% for user in st.downloaded %}
        <div class="project-chapter-video-user">
          <img src="{{ user|get_avatar:'small' }}" alt="">
        </div>
      {% endfor %}
    {% endif %}

    {% if role == 'admin' %}
      {% include 'top/_rating.html' with rating=st.rating %}
    {% endif %}

    <div class="bottom-menu">
      <div class="menu-heading">
        {% if st.status == '5' %}
          <div class="left-icon project-chapter-video-undone"
               title="進行中に戻す"></div>
        {% elif st.status == '6' %}
          <div class="left-icon download_production_btn"
               title="ダウンロード"></div>
        {% else %}
          <div class="left-icon video-done {% if role != 'admin' %}project-chapter-video-done{% endif %}"
               title="OK"></div>
        {% endif %}
        <div class="project-chapter-video-scence">
          <span title="{{ st.title }}">{{ st.title }}</span>
          <p>{{ st.modified|date:"y/m/d H:i" }}</p>
        </div>
      </div>

      <div class="video-menu">
        {% if st.status == '6' and role == 'admin' %}
          <div class="menu-button upload_production_btn">
            <div class="left-icon"></div>
            <span>納品データを編集</span>
          </div>
        {% elif st.status == '5' and role == 'admin' %}
          <div class="menu-button upload_production_btn">
            <div class="left-icon"></div>
            <span>アップロード</span>
          </div>
        {% endif %}

        {% if st.status == '6' %}
          <div class="menu-button project-chapter-video-undone">
            <div class="left-icon"></div>
            <span>進行中に戻す</span>
          </div>
        {% endif %}

        {% if role == 'master_client' %}
          <div class="menu-button project-chapter-video-like">
            <div class="left-icon"></div>
            <span>好き</span>
          </div>
          <div class="menu-button project-chapter-video-dislike">
            <div class="left-icon"></div>
            <span>好きじゃない</span>
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>
