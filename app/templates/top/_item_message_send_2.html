{% load static %}
{% load util %}
<div class="mmessage-main" {% if user.pk != message.user.pk %} title="{{message.user.get_display_name}}" {% endif %}>
  {#  {% if not message.is_near %}#}
  {#    <div class="avatar avatar--image avatar--32 avatar--round" title="{{ message.user.fullname }}">#}
  {#      <div class="avatar-image" style="background-image: url({{ message.user|get_avatar:'medium' }})"></div>#}
  {#    </div>#}
  {#  {% endif %}#}
  <div class="mmessage-content messages-sent-comment {% if message|get_message_files_count > 0 %}border-audio-message{% endif %} {% if message|get_message_files_count == 1 %}single-file{% endif %} {% if message.comment and message.comment != '' %}has-message{% endif %}">
    {% if message.parent %}
      {% if message.comment and message.comment != '' %}
        <div class="s-filedisable-wrap">
          <a class="s-filedisable s-filedisable--filedisable s-filedisable--black txt-message-reply"
             href="javascript:void(0)">{{ message.parent|get_content_comment_parent }}</a>

          {% if type not in 'product, messenger_owner, messenger' and message.pin_time %}
            <div class="s-audio s-audio--audio s-audio--black" data-scene-id="{{ message|get_pin_video }}">
              <div class="s-audio-icon">
                <div class="s-audio-control video-pin-time">
                  <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                    play_circle
                  </span>
                  <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                </div>
                <div class="s-audio-time video-pin-start">{{ message.pin_time }}</div>
              </div>
              <div class="s-filetext">{{ message.comment }}</div>
            </div>
          {% else %}
            <div class="s-filetext txt-below-message-reply">{{ message.comment }}</div>
          {% endif %}
        </div>
      {% endif %}

      {% for file in message|get_sorted_message_files %}
        {% if not file.folder %}
          {% with file.is_audio_file as type_file %}
            {% if forloop.counter0 == 0 and not message.comment and message.comment == '' %}
                <div class="comments-audio-block">
                    <div class="s-filedisable-wrap">
                        <a class="s-filedisable s-filedisable--filedisable s-filedisable--black"
                           href="javascript:void(0) ">{{ message.parent|get_content_comment_parent }}</a>
                        {% include 'top/_item_send_2.html' with type_file=type_file user=user message=message file=file type=type file_name=file.real_name type_comment=type %}
                    </div>
                </div>
            {% else %}
                <div class="comments-audio-block">
                    {% include 'top/_item_send_2.html' with type_file=type_file user=user message=message file=file type=type file_name=file.real_name type_comment=type %}
                </div>
            {% endif %}
          {% endwith %}
        {% endif %}
      {% endfor %}
      {% for folder in message.folders.all %}
        {% if not folder.parent %}
            {% include 'top/_item_send_2.html' with type_file='folder' user=user message=message file=folder type=type file_name=folder.name %}
        {% endif %}
      {% endfor %}

    {% else %}
      {% if message.comment and message.comment != '' %}
        <div class="mmessenger mmessenger--text mmessenger--black">
          <div class="messenger-content">
            <div class="messenger-content">
              {% if type not in 'product, messenger_owner, messenger' and message.pin_video %}
                <div class="s-audio s-audio--audio s-audio--black" data-scene-id="{{ message.scene_id }}">
                  <div class="s-audio-icon">
                    <div class="s-audio-control video-pin-time">
                      <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                        play_circle
                      </span>
                      <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                    </div>
                    <div class="s-audio-time video-pin-start">{{ message.pin_time }}</div>
                  </div>

                  <div class="s-audio-text">{{ message.comment }}</div>
                </div>
              {% else %}
                <div class="bodytext-13 u-line-height-150" style="white-space: pre-wrap;">{{ message.comment }}</div>
              {% endif %}
            </div>

          </div>
        </div>
      {% endif %}

      {% for file in message|get_sorted_message_files %}
        {% if not file.folder %}
          {% with file.is_audio_file as type_file %}
            {% include 'top/_item_send_2.html' with file=file message=message user=user type_file=type_file type=type file_name=file.real_name type_comment=type %}
          {% endwith %}
        {% endif %}
      {% endfor %}

      {% for folder in message.folders.all %}
        {% if not folder.parent %}
          {% include 'top/_item_send_2.html' with file=folder message=message user=user type_file='folder' type=type file_name=folder.name type_comment=type %}
        {% endif %}
      {% endfor %}

    {% endif %}
  </div>
</div>

<div class="mmessage-info {% if message|check_has_audio_file %}message-info-audio{% endif %}">
    {% if request.resolver_match.url_name != 'scene_show' %}
        <div class="dropdown dropdown-comment-new dropdown-comment">
            <div class="dropdown-toggle show-more-comment show-more-action-message" id="dropdownMenu1" data-toggle="dropdown"
                 aria-expanded="false" aria-haspopup="true">
                <img src="{% static 'images/scene-detail/icon-more-horiz.svg' %}" class="more-action-hoz" alt="more horiz comment">
            </div>
            <ul class="dropdown-menu dropdown-menu-comment" aria-labelledby="dropdownMenu1">
                <li class="li-resolve-message">
                    <a class="dropdown-item mmessage-resolve {% if message.resolved %}mmessage-resolved{% endif %}"
                       href="javascript:void(0);">
                        <span class="txt-item-comment">{% if message.resolved %}進行中に戻す{% else %}
                            解決済みにする{% endif %}</span>
                            <span class="material-symbols-rounded img-resolve-comment">
                              check_circle
                            </span>
                    </a>
                </li>
                <li class="li-reply-message">
                    <a class="dropdown-item mmessage-reply" href="javascript:void(0);">
                        <span class="txt-item-comment txt-reply-comment">返信</span>
                        <span class="material-symbols-rounded img-reply-comment">
                          reply
                        </span>
                    </a>
                </li>
                {% if message.user.role == user.role and message.user.pk == user.pk %}
                    <li class="li-edit-message">
                        <a class="dropdown-item mmessage-edit" href="javascript:void(0);">
                            <span class="txt-item-comment txt-edit-comment">コメントを編集</span>
                            <span class="material-symbols-rounded img-edit-comment">
                              edit
                            </span>
                        </a>
                    </li>
                    <li class="last-action-comment li-reply-message">
                        <a class="dropdown-item mmessage-delete" href="javascript:void(0);">
                            <span class="txt-item-comment txt-green">削除</span>
                            <span class="material-symbols-rounded">
                              delete
                              </span>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </div>
    {% endif %}
    {% include 'top/_message_infor.html' with type=type message=message user=user type_infor='send' first_message=first_message new_layout=True%}
</div>