{% load static %}
{% load util %}

{% if new_layout is None %}
    {% if type_comment == 'messenger' and message.user_id != user.pk or type_comment == 'messenger_owner' and message.user_id != user.pk %}
    {% else %}
        {% if type_infor == 'received' %}
            <div class="{% if offer and type_comment != 'messenger' %} dp-none {% endif %} dropdown dropdown-comment-new dropdown-comment actions-received dropdown-comment-received">
                <div class="dropdown-toggle show-more-comment-received show-more-action-message"
                     id="dropdownMenuButton2" data-toggle="dropdown"
                     aria-haspopup="true" aria-expanded="false">
                    <img src="{% static 'images/scene-detail/icon-more-horiz.svg' %}" class="more-action-hoz"
                         alt="more horiz comment">
                </div>
                <ul class="dropdown-menu dropdown-menu-comment" aria-labelledby="dropdownMenuButton2">
                    {% if offer is None %}
                        <li class="li-resolve-message">
                            <a class="dropdown-item mmessage-resolve {% if message.resolved %}mmessage-resolved{% endif %}"
                               href="javascript:void(0);">
                                <span class="txt-item-comment">{% if message.resolved %}進行中に戻す{% else %}
                                    解決済みにする{% endif %}</span>
                                    <span class="material-symbols-rounded img-resolve-comment">
                                        check_circle
                                      </span>
                            </a>
                        </li>

                        <li class="last-action-comment">
                            <a class="dropdown-item mmessage-reply" href="javascript:void(0);">
                                <span class="txt-item-comment txt-reply-comment">返信</span>
                                <span class="material-symbols-rounded img-reply-comment">
                                    reply
                                  </span>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>

        {% else %}
            <div class="dropdown dropdown-comment-new dropdown-comment">
                <div class="dropdown-toggle show-more-comment show-more-action-message" id="dropdownMenuButton2"
                     data-toggle="dropdown"
                     aria-haspopup="true" aria-expanded="false">
                    <img src="{% static 'images/scene-detail/icon-more-horiz.svg' %}" class="more-action-hoz"
                         alt="more horiz comment">
                </div>
                <ul class="dropdown-menu dropdown-menu-comment" aria-labelledby="dropdownMenuButton2">
                    {% if offer is None %}
                        <li class="li-resolve-message">
                            <a class="dropdown-item mmessage-resolve {% if message.resolved %}mmessage-resolved{% endif %}"
                               href="javascript:void(0);">
                                <span class="txt-item-comment">{% if message.resolved %}進行中に戻す{% else %}
                                    解決済みにする{% endif %}</span>
                                    <span class="material-symbols-rounded img-resolve-comment">
                                        check_circle
                                      </span>
                            </a>
                        </li>

                        <li class="li-reply-message">
                            <a class="dropdown-item mmessage-reply" href="javascript:void(0);">
                                <span class="txt-item-comment txt-reply-comment">返信</span>
                                <span class="material-symbols-rounded img-reply-comment">
                                    reply
                                  </span>
                            </a>
                        </li>
                    {% endif %}
                    {% if message.user.role == user.role and message.user.pk == user.pk %}
                        <li class="li-edit-message">
                            <a class="dropdown-item mmessage-edit" href="javascript:void(0);">
                                <span class="txt-item-comment txt-edit-comment">コメントを編集</span>
                                <span class="material-symbols-rounded img-edit-comment">
                                    edit
                                  </span>
                            </a>
                        </li>
                        <li class="last-action-comment last-action-comment">
                            <a class="dropdown-item mmessage-delete" href="javascript:void(0);">
                                <span class="txt-item-comment txt-green">削除</span>
                                <span class="material-symbols-rounded">
                                    delete
                                    </span>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        {% endif %}
    {% endif %}


    {#<div class="message-actions-container {% if type_infor == 'received' %} actions-received {% endif %}">#}
    {#  <div class="mmessage-action hide">#}
    {#    {% if user == message.user %}#}
    {#      {% if type != 'messenger_owner' or type == 'messenger_owner' and not message.is_first_message %}#}
    {#        {% if type not in 'messenger_artist, messenger' or type in 'messenger_artist, messenger' and not first_message %}#}
    {#          <a class="mmessage-delete" href="#">#}
    {#            <i class="icon icon--sicon-trash"></i>#}
    {#          </a>#}
    {#          <a class="mmessage-edit" href="#">#}
    {#            <i class="icon icon--sicon-pencil"></i>#}
    {#          </a>#}
    {#        {% else %}#}
    {#          <a href="#" class="button-edit_offer message-first__message" data-toggle="modal"#}
    {#             data-target="#modal-edit-offer">#}
    {#            <i class="icon icon--sicon-pencil"></i>#}
    {#          </a>#}
    {#        {% endif %}#}
    {#      {% endif %}#}
    {#    {% else %}#}
    {#      {% if type not in 'messenger_owner, messenger, messenger_artist' %}#}
    {#        <a class="mmessage-reply" href="#">#}
    {#          <i class="icon icon--sicon-reply"></i>#}
    {#        </a>#}
    {#        {% if type_infor == 'received' %}#}
    {#          <a class="mmessage-resolve {% if message.resolved %} mmessage-resolved {% endif %}" href="javascript:void(0)">#}
    {#            <i class="icon icon--sicon-tick"></i>#}
    {#          </a>#}
    {#        {% endif %}#}
    {#      {% endif %}#}
    {#    {% endif %}#}
    {#  </div>#}
    {#</div>#}
{% endif %}
<div class="message-info-container {% if type_infor == 'received' %} actions-received {% endif %} {% if type_comment == 'messenger' and message.user_id != user.pk or type_comment == 'messenger_owner' and  message.user_id != user.pk %}height-init{% endif %}">
    <div class="mmessage-status">
        <div class="mmessage-time">{{ message.created|get_weekday_new }}</div>
        <div class="mmessage-user">
            {% with is_pc_device|show_user_seen_max as max_loop %}
                {% with message|get_user_seen:type as users %}
                    {% if type_infor != 'received' %}
                        {% include 'top/_count_user_seen.html' with users=users %}
                    {% endif %}
                    {% for user in users %}
                        {% if forloop.counter0 < max_loop %}
                            {% include 'top/_user_seen_message.html' with user=user %}
                        {% endif %}
                    {% endfor %}
                    {% if type_infor == 'received' %}
                        {% include 'top/_count_user_seen.html' with users=users %}
                    {% endif %}
                {% endwith %}
            {% endwith %}
        </div>
    </div>
</div>