{% load static %} {% load i18n %}
<link rel="stylesheet" type="text/css" href="{% static 'css/create_new_chapter.css' %}" />
<div class="modal-container chapter__modal-container modal-container-create" style="display: none" tabindex="-1"
  role="dialog">
  <div class="popup" id="create-chapter" data-project-id="{{ project.pk }}">
    <div class="popup-inner row">
      <div class="u-col u-w100 u-gap16">
        <div class="heading-18-spacing">チャプターを作成</div>
        <input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf }}" />
        <div class="select-container select_role sumo-select u-w100">
          <input type="text" class="form-control input-chapter-name" placeholder="チャプター名" required />
        </div>
        <div class="u-row-end u-w100">
          <a class="smodal-close" href="#" data-dismiss="modal" aria-label="Close">
            <button type="button smodal-close" class="btn btn--tertiary btn-link" data-dismiss="modal">
              キャンセル
            </button>
          </a>
          <button type="button" class="btn btn--primary btn-create-product-scene">
            追加
          </button>
        </div>
      </div>
    </div>
  </div>
</div>