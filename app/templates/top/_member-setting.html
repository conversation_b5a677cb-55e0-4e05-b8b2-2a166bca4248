{% load util %}
{% load static %}

<div class="modal-header">
  <div class="member-item">
    <div class="member-item__avatar">
      <div class="avatar avatar--image avatar--48 avatar--round">
        <div class="avatar-image" style="background-image: url({{ member|get_avatar:'medium' }})"></div>
      </div>
    </div>
    <div class="member-item__info">
      <div class="member-item__name sheading sheading--13">{{ member.get_display_name }}</div>
      <div class="member-item__role scaption">Role</div>
<!--      {{ member.position|hide_if_empty }}-->
    </div>
  </div>
  <a class="smodal-close smodal-close--prev" href="#" data-dismiss="modal" aria-label="Close">
    <i class="icon icon--sicon-prev"></i>
  </a>
</div>
<div class="modal-content" style="border-top: 1px solid #F0F0F0; margin-bottom: 100px;" data-user="member.pk" data-product-user="{{ product_user.pk }}">
  <div class="modal-body mscrollbar">
    <div class="member-ip">
      <div class="member-ip__content">
        <div class="member-ip__list">
          <p class="member-ip__text">アクセスを許可するIPアドレスを入力してください。</p>
          {% for ip in ips %}
            <div class="member-ip__item">
              <div class="member-ip__input">
                <input class="sform-control sform-control--input" type="text" data-value="{{ ip }}" value="{{ ip }}"/>
              </div>
              <a class="member-ip__delete" href="#">
                <i class="icon icon--sicon-trash"></i>
              </a>
            </div>
          {% endfor %}
        </div>
        <div class="member-ip__new">
          <div class="member-ip__new-ip">
            <input class="sform-control sform-control--input" type="text" placeholder="***********"/>
          </div>
          <a class="member-ip__new-btn" href="#">
            <i class="icon icon--sicon-add-circle-o"></i>
          </a>
        </div>
      </div>
      <div class="member-ip__action" style="text-align: right;">
        <a class="btn btn--blue btn--lg member-ip__send" href="javascript:void(0)">
          <span class="btn-text">IPアドレス制限を設定</span>
        </a>
      </div>
    </div>
  </div>
</div>
