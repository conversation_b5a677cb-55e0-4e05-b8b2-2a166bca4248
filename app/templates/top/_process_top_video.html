{% load static %}
{% load util %}

<div class="project-delivery-item">
    <div class="project-delivery-item-content" data-scene-id="{{scene.pk}}"
         data-scene-title="{{ st.pk }}">
        {% if st.status == '5' %}
            <div class="overlay uploading_production"></div>
        {% elif st.status == '6' %}
            <div class="overlay uploaded_production"></div>
        {% endif %}
        {% render_video_with_hls scene width="100%" height="auto" poster=scene|get_thumbnail preload="none" id="process_top_video_"|add:scene.pk as video_html %}
        {% if video_html %}
          {{ video_html }}
        {% else %}
          <video width="100%" height="auto" poster="{{ scene|get_thumbnail }}"
                 preload="none">
              <source src="{{ scene.movie.url }}" type="video/mp4">
          </video>
        {% endif %}
        <div class="project-delivery-chapter">
            {{ scene.title.product_scene.name }} &gt; {{ st.title }}</div>
        <div class="bottom-menu">
            <div class="video-menu">
        {% if role == 'admin' and st.status == '5' %}
            <div class="menu-button upload_production_btn">
                <div class="left-icon"></div>
                <span>アップロード</span>
            </div>
        {% elif role == 'admin' and st.status == '6' %}
            <div class="menu-button upload_production_btn">
                <div class="left-icon"></div>
                <span>納品データを編集</span>
            </div>
        {% elif role != 'admin' %}
            {% if st.status == '6' %}
            <div class="menu-button download_production_btn">
                <div class="left-icon"></div>
                <span>ダウンロード</span>
            </div>
            {% endif %}
        {% endif %}

        {% if st.is_done %}
            <div class="menu-button project-chapter-video-undone">
                <div class="left-icon"></div>
                <span>進行中に戻す</span>
            </div>
        {% elif role != 'admin' %}
            <div class="menu-button project-chapter-video-done">
                <div class="left-icon"></div>
                <span>OK</span>
            </div>
        {% endif %}
        </div>
        </div>

    </div>
    <div class="project-delivery-time">{{ scene.modified|to_now }}</div>
</div>
