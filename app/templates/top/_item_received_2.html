{% load static %}
{% load util %}

{% if type_file == 'audio' %}
    <div class="comment-audio-content {% if message.comment and message.comment != '' %}border-audio-message{% endif %} ">
        <div class="mmessenger mmessenger--audio-wave mmessenger--gray message-audio-block"
             data-file-id="{{ file.pk }}">
            <div class="messenger-content messenger-content-file">
                <div class="s-audio s-audio--audio-wave s-audio--gray" style="border: none"
                     {% if type not in 'product, messenger_owner, messenger' %}data-scene-id="{{ message.scene_id }}{% endif %}">
                    <div class="s-audio-control
                            {% if type not in 'product, messenger_owner, messenger' %}{% if message.pin_video %}video-pin-time{% endif %}{% endif %}">
                        <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                            play_circle
                        </span>
                        <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                    </div>
                    <div class="hide video-pin-start">{% if type not in 'product, messenger_owner, messenger' %}
                        {{ message.pin_time }}{% endif %}</div>
                    <div class="s-audio-source" data-link="{{ file.file.url }}"
                         data-title="{{ file_name }}" data-waveColor="#d3d3d3"
                         data-progressColor="#53565a" data-peaks-loaded="{{ file.peaks }}"></div>
                    <div class="s-audio-waveform"></div>
                    <div class="s-audio-time"></div>
                </div>
            </div>
        </div>
        <div class="info-item-audio-comment {% if scene.production_file %}tfile-producttion-file {% endif %}block-download-file"
             data-scene-title-id="{{ scene.pk }}"
             data-file-id="{{ file.pk }}">
            <div class="block-name-action-audio">
                <p class="file-name-message file-name-cmt">{{ file_name }}</p>
                <div class="block-btn-action-audio-msg">
                    {% if file.is_audio_file in 'audio,video' and file.acr_status in '1,2,3' %}
                        {% if file.acr_status == '3' %}
                            <a href="javascript:void(0);" class="acr-result-icon btn-finger-print active" data-file-id="{{ file.pk }}">
                                <img src="{% static 'images/scene-detail/icon-finger-print-active.svg' %}" class=""
                                     alt="finger print">
                            </a>
                        {% else %}
                            <a href="javascript:void(0);" class="acr-result-icon btn-finger-print">
                                <img src="{% static 'images/scene-detail/icon-finger-print.svg' %}" class=""
                                     alt="finger print">
                            </a>
                        {% endif %}
                    {% endif %}
                        <a href="javascript:void(0);"
                           class="btn-download-file"
                        >
                        <span class="material-symbols-rounded scene-file-download">
                            download
                        </span>
                        </a>
{#                    {% endwith %}#}
                </div>
            </div>
            <div class="audio-type-info-msg">
                {% with file.file_info|parse_json_message_info:'audio' as result %}
                    {% if result is not None %}
                        <div class="file-info-message">
                            <span>{{ result.sample_rate }} </span>
                            <span>{{ result.bit_depth }} </span>
                            <span>{{ result.channel_type }} </span>
                            <span>{{ result.loudness }}</span>
                        </div>
                    {% endif %}
                {% endwith %}
                <div class="peoples-downloaded-audio-msg">
                    {% with file|get_list_user_download:type_comment as user_dowloadeds %}
                        <div class="has_user_downloaded">
                            {% include 'top/_sview_user.html' with file=file type_comment=type_comment user_dowloadeds=user_dowloadeds %}
                        </div>
                    {% endwith %}
                </div>
            </div>
        </div>
    </div>
{% else %}
  {% if type not in 'product, messenger_owner, messenger' and message.pin_video %}
    <div class="s-audio s-audio--audio s-audio--gray" data-scene-id="{{ message.scene_id }}">
      <div style="display: flex">
        <div class="s-audio-control video-pin-time">
          <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
            play_circle
          </span>
          <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
        </div>
        <div class="s-audio-time video-pin-start">{{ message.pin_time }}</div>
      </div>
      <div class="s-audio-text s-audio-file">
        <div class="mmessenger mmessenger--file item-comment mmessenger--gray {% if type_file == 'folder' %}messager-folder{% endif %}"
             data-toggle="modal"
             data-target="#modal-{{ type_file }}-popup" data-link="{{ file.file.url }}"
             data-name="{{ file_name }}" data-type="{{ type_file }}" data-file-id="{{ file.pk }}">
          <div class="messenger-content messenger-content-file">
            <div class="s-file s-file--file s-file--gray">
              <i class="{% if type_file == 'folder' %}icon icon--sicon-storage{% else %}icon icon--sicon-clip{% endif %}"></i>{{ file_name }}
            </div>
          </div>
        </div>
      </div>
    </div>
  {% else %}
      <div class="mmessenger mmessenger--file mmessenger--gray {% if type_file == 'folder' %}messager-folder{% endif %} {% if type_file == 'image' and message|get_message_files_count == 1 and not message.comment or message|get_message_files_count == 1 and message.comment == '' %}
    single-file-image {% endif %} {% if type_file == 'document' or type_file == 'other' %}messager-document-other{% endif %}
{% if scene.production_file %}tfile-producttion-file {% endif %}block-download-file"
         data-toggle="modal"
         data-target="#modal-{{ type_file }}-popup" data-link="{{ file.file.url }}"
         data-name="{{ file_name }}" data-type="{{ type_file }}" data-file-id="{{ file.pk }}"
         {% if file.has_hls %}data-hls-url="{{ file.hls_url }}" data-has-hls="true"{% endif %}
         {% if file.converted_file %}data-converted-link="{{ file.converted_file }}" data-converted-path="true"{% endif %}>
      <div class="messenger-content messenger-content-file">
          {% if type_file == 'document' %}
              <div class="block-pdf-image">
                   {% with file.file_info|parse_json_message_info:'document' as result %}
                      {% if result is not None %}
                          <img src="{{ result.url_image|get_presigned_url_message }}" class="pdf-image"
                               alt="pdf image">
                      {% endif %}
                  {% endwith %}
              </div>
          {% endif %}
        <div class="s-file s-file--file s-file--gray {% if type_file == 'image' %}messenger-image-preview-content{% endif %} {% if type_file == 'video' %}message-video{% endif %}">
            {% if type_file == 'image' %}
                <div class="info-message-2">
                    {% with file.file_info|parse_json_message_info:'image' as result %}
                        {% if result is not None %}
                            <div class="size-file-message">
                                <span>{{ result.width }} x {{ result.height }}px </span>
                            </div>
                        {% endif %}
                    {% endwith %}
                    <div class="users-downloaded-file">
                    {% with file|get_list_user_download:type_comment as user_dowloadeds %}
                        <div class="has_user_downloaded">
                            {% include 'top/_sview_user.html' with file=file type_comment=type_comment user_dowloadeds=user_dowloadeds %}
                        </div>
                    {% endwith %}
                </div>
                </div>
                <div class="comment-file-content">
                    <p style="word-break: break-all;" class="file-name-cmt">{{ file_name }}</p>
                    <a href="javascript:void(0);"
                       data-scene-title-id="{{ scene.pk }}"
                       data-file-id="{{ file.pk }}"
                       class="btn-download-file {% if scene.production_file %} tfile-producttion-file {% endif %}block-download-file"
                    >
                    <span class="material-symbols-rounded scene-file-download">
						download
					</span>
                    </a>
                </div>
                <div class="image-preview-comment {% if type_file == 'image' %}active-view{% endif %}">
                    <img src="{{ file.file.url }}" alt="{{ file_name }}" loading="lazy">
                </div>
                {% elif type_file == 'video' %}
                <div class="block-info-video-message">
                    <div class="block-video-cmt" style="width: 100%">
                        {% render_video_with_hls file height="144px" preload="metadata" loading="lazy" id="received_video_"|add:file.pk as video_html %}
                          {% if video_html %}
                            {{ video_html }}
                          {% else %}
                            <video height="144px"
                                   preload="metadata" loading="lazy">
                                <source src="{{ file.file.url }}"
                                        type="video/mp4"/>
                            </video>
                          {% endif %}
                        {% endwith %}
                    </div>
                    <div class="action-video-message">
                        <p style="word-break: break-all;" class="file-name-cmt">{{ file_name }}</p>
                        <div class="action-right-video">
                            {% if file.is_audio_file in 'audio,video' and file.acr_status in '1,2,3' %}
                                {% if file.acr_status == '3' %}
                                    <a href="javascript:void(0);" class="acr-result-icon btn-finger-print active" data-file-id="{{ file.pk }}">
                                        <img src="{% static 'images/scene-detail/icon-finger-print-active.svg' %}"
                                             class=""
                                             alt="finger print">
                                    </a>
                                {% else %}
                                    <a href="javascript:void(0);" class="acr-result-icon btn-finger-print">
                                        <img src="{% static 'images/scene-detail/icon-finger-print.svg' %}" class=""
                                             alt="finger print">
                                    </a>
                                {% endif %}
                            {% endif %}
                            <a href="javascript:void(0);"
                               class="btn-download-file"
                            >
                            <span class="material-symbols-rounded scene-file-download">
                                download
                            </span>
                            </a>
                        </div>
                    </div>
                </div>
          {% else %}
                {% if type_file != 'folder' %}
                    <p style="word-break: break-all;">{{ file_name }}</p>
                    <a href="javascript:void(0);"
                       data-scene-title-id="{{ scene.pk }}"
                       data-file-id="{{ file.pk }}"
                       class="btn-download-file {% if scene.production_file %} tfile-producttion-file {% endif %}block-download-file"
                    >
                    <span class="material-symbols-rounded scene-file-download">
						download
					</span>
                    </a>
                {% else %}
                    <div class="left-folder-message">
                        <img src="{% static 'images/scene-detail/icon-folder.svg' %}"
                             class="scene-folder-message" alt="folder">
                        <p style="word-break: break-all;">{{ file_name }}</p>
                    </div>
                    <img src="{% static 'images/scene-detail/icon-navigate-next.svg' %}"
                         class="scene-navigation-next" alt="navigation next">
                {% endif %}
            {% endif %}
        </div>
          {% if type_file != 'folder' and type_file != 'image' %}
              <div class="info-message-2">
                  {% if type_file == 'video' %}
                      <div class="size-file-message">
                          {% with file.file_info|parse_json_message_info:'video' as result %}
                              {% if result is not None %}
                                  <span>{{ result.width }} x {{ result.height }}px </span>
                                  <span>{{ result.fps }}fps</span>
                              {% endif %}
                          {% endwith %}
                      </div>
                      {% elif type_file == 'document' %}
                      <div class="size-file-message">
                          {% with file.file_info|parse_json_message_info:'document' as result %}
                              {% if result is not None %}
                                  <span>{{ result.width }} x {{ result.height }}px</span>
                              {% endif %}
                          {% endwith %}
                      </div>
                  {% else %}
                      {% with file.file_info|parse_json_message_info:'other' as result %}
                          {% if result is not None %}
                              <span class="size-file-message">{{ result.size_converted }}</span>
                          {% endif %}
                      {% endwith %}
                {% endif %}
                  <div class="users-downloaded-file">
                      {% with file|get_list_user_download:type_comment as user_dowloadeds %}
                          <div class="has_user_downloaded">
                              {% include 'top/_sview_user.html' with file=file type_comment=type_comment user_dowloadeds=user_dowloadeds %}
                          </div>
                      {% endwith %}
                  </div>
              </div>
          {% endif %}
      </div>
      </div>
      {% if type_file == 'folder' %}
          <div class="block-bellow-message-file">
          {% with  file|get_total_file_in_folder as total_files %}
              <p class="txt-file-message">{{ total_files }}{% if total_files > 1 %} files{% else %} file{% endif %}</p>
              {% endwith %}
          </div>
      {% endif %}
  {% endif %}
{% endif %}
