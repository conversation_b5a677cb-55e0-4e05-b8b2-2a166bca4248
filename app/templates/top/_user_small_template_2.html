{% load static %}
{% load util %}
{% load i18n %}

<div class="user-project-template-2 produce-template-2" data-id="{{ owner.id }}">
    <div class="block-user-project-avatar">
        <img src="{{ owner|get_avatar:'medium' }}" alt="" class="user-project-avatar produce-avatar">
        {% if owner in online_users %}
            <span class="material-symbols-rounded user-project-status">circle</span>
        {% endif %}
    </div>
    <div class="item-project-content produce-content">
        <p class="heading-13-spacing u-text-black u-mb0">{{ owner.get_display_name }}</p>
        <p class="bodytext-11 u-line-height-100 u-mb0">{{ owner.position|hide_if_empty }}</p>
    </div>
    <div class="block-user-nav">
        <span class="material-symbols-rounded">more_horiz</span>
    </div>
</div>