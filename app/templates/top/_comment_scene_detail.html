{% load static %}
{% load util %}
{% load i18n %}
{% block extrahead %}
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,400,0,0" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,<PERSON><PERSON><PERSON>@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@24,400,0,0" />
{% endblock %}
{#<div class="pd-comment__heading">メッセージ</div>#}
<div class="pd-comment__main">
{#  <div class="pd-comment__top">#}
{#    <div class="pd-comment__attach">#}
{#      <div class="form-check custom-switch">#}
{#        <label class="form-check-label">#}
{#          <div class="form-check-group">#}
{#            <input class="form-check-input switch-checkbox" type="checkbox" name="switch-checkbox-comment"#}
{#                   id="switch-checkbox-comment"/><span class="switch-slider"></span>#}
{#          </div>#}
{#          <span class="switch-label">{% trans "See also resolved" %}</span>#}
{#        </label>#}
{#      </div>#}
{#    </div>#}
{#    <div class="pd-file active">#}
{#      <div class="pd-file-hide-heading">#}
{#        <i class="icon icon--sicon-storage"></i>#}
{#      </div>#}
{#    </div>#}
{#  </div>#}
<div class="block-tab-comment align-self-center">
{#    <ul class="nav nav-pills" id="comment-tab" role="tablist">#}
{#    <li class="nav-item" role="presentation">#}
{#    <button class="nav-link active" id="process-comment-tab"#}
{#            data-bs-toggle="pill" data-bs-target="#process-comment"#}
{#            type="button" role="tab" aria-controls="process-comment" aria-selected="true">進行中のみ</button>#}
{#  </li>#}
{#  <li class="nav-item" role="presentation">#}
{#    <button class="nav-link" id="all-comment-tab"#}
{#            data-bs-toggle="pill" data-bs-target="#all-comment" type="button" role="tab"#}
{#            aria-controls="all-comment"#}
{#            aria-selected="false">全コメント</button>#}
{#  </li>#}
{#</ul>#}
{#<div class="tab-content" id="pills-tabContent">#}
{#  <div class="tab-pane fade show active" id="process-comment" role="tabpanel" aria-labelledby="process-comment-tab">...</div>#}
{#  <div class="tab-pane fade" id="all-comment" role="tabpanel" aria-labelledby="all-comment-tab">.dfgdf..</div>#}
{#</div>#}
<input type="checkbox" class="switch-checkbox"  name="switch-checkbox-comment" id="switch-checkbox-comment">
<label for="switch-checkbox-comment" class="tab-comment active" onclick="changeTab(1)">進行中のみ</label>
<label for="switch-checkbox-comment" class="tab-comment" onclick="changeTab(2)">全コメント</label>




</div>
  <div class="pd-comment__content">
    <div class="mmessage-component">
      <div class="mattach" style="color: black;" id="comment-input-99-mcomment-attach" data-maxfile="2" data-maxsize="32">
        <div class="mattach-overlay"></div>
        <form class="mattach-file" action="upload.php" id="comment-input-99-mcomment-attach-form">
          <div class="mattach-drop">
            <div class="mattach-text"></div>
            <div class="border"></div>
          </div>
        </form>
      </div>
      <div class="message-list-new mmessage-list mscrollbar mscrollbar--vertical mscrollbar--bottom{% if view_only or not user or not user.is_authenticated %} view_only{% endif %}{% if is_seen %} not-seen{% endif %}">
        {% include 'top/_item_messages_2.html' with comments=comments user=user type=type %}
        <div class="mlast__content"></div>
        <div class="show-message-btn hidden-pc"></div>
      </div>

      {% if not is_deleted and not view_only %}
        <div class="maction">
          <div class="mcommment" id="comment-input-99">
            <div class="mcomment-message">
              <div class="mcomment-top">
                <div class="mcomment-attached">
                  <div class="mattach-preview-container">
                    <div class="mattach-previews collection">
                      <div class="mattach-template collection-item item-template">
                        <div class="mattach-info" data-dz-thumbnail="">
                          <div class="mcommment-file">
                             <div class="progress">
                              <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                            </div>
                            <div class="mcommment-file__name" data-dz-name=""></div>
                            <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                    class="icon icon--sicon-close"></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="mcomment-input">
                  <div class="mcomment-input-title"></div>
                  <textarea class="mcomment-input-text mcomment-autoExpand" name="mcomment-input" rows="1"
                            placeholder="コメントを入力…"></textarea>
                  <div class="mcomment-input-close"><i class="icon icon--sicon-close"></i>
                  </div>
                </div>
                  <div class="block-remove-msg-editing d-none">
                      <div class="mcomment-input-close btn-remove-msg">
                          <i class="icon icon--sicon-close"></i>
                      </div>
                  </div>
              </div>
              <div class="mcomment-bottom">
                <div class="mcomment-pin">
                  <i class="icon material-symbols-rounded">pin_drop</i>
                </div>
                <div class="mcomment-action">
                  <div class="mattach-label"><i class="icon material-symbols-rounded">add_circle</i>
                  </div>
                </div>
                <div class="mcomment-input-placeholder">コメントを送信</div>
                <div class="mcomment-icon">
                  <div class="mboard-triangle"></div>
                  <div class="mcomment-icon-board mscrollbar">
                    <div class="micon-board-section micon-board-recently">
                      <div class="micon-board-heading">最近使った</div>
                      <div class="micon-board-list">
                        <div class="micon-board-icon"><img src="images/micon-happy.png" alt=""/></div>
                        <div class="micon-board-icon"><img src="images/micon-happy.png" alt=""/></div>
                        <div class="micon-board-icon"><img src="images/micon-happy.png" alt=""/></div>
                        <div class="micon-board-icon"><img src="images/micon-happy.png" alt=""/></div>
                        <div class="micon-board-icon"><img src="images/micon-happy.png" alt=""/></div>
                        <div class="micon-board-icon"><img src="images/micon-happy.png" alt=""/></div>
                        <div class="micon-board-icon"><img src="images/micon-happy.png" alt=""/></div>
                      </div>
                    </div>
                    <div class="micon-board-section micon-board-all">
                      <div class="micon-board-heading">全て</div>
                      <div class="micon-board-list">
                        <div class="micon-board-icon"><img src="images/micon-happy.png" alt=""/></div>
                        <div class="micon-board-icon"><img src="images/micon-happy.png" alt=""/></div>
                        <div class="micon-board-icon"><img src="images/micon-happy.png" alt=""/></div>
                        <div class="micon-board-icon"><img src="images/micon-happy.png" alt=""/></div>
                        <div class="micon-board-icon"><img src="images/micon-happy.png" alt=""/></div>
                        <div class="micon-board-icon"><img src="images/micon-happy.png" alt=""/></div>
                        <div class="micon-board-icon"><img src="images/micon-happy.png" alt=""/></div>
                        <div class="micon-board-icon"><img src="images/micon-happy.png" alt=""/></div>
                        <div class="micon-board-icon"><img src="images/micon-happy.png" alt=""/></div>
                        <div class="micon-board-icon"><img src="images/micon-happy.png" alt=""/></div>
                        <div class="micon-board-icon"><img src="images/micon-happy.png" alt=""/></div>
                      </div>
                    </div>
                  </div>
                </div>
                <a class="mcomment-send disabled" href="#"><span class="material-symbols-rounded u-fontsize-32 u-text-border">send</span></a>
              </div>
            </div>
          </div>
        </div>
{#           <div class="block-input-message-new">#}
{#            <div class="input-message-new">#}
{#                <textarea placeholder="メッセージを送信"></textarea>#}
{#            </div>#}
{#            <div class="action-message d-flex">#}
{#                <a href="javascript:void(0);">#}
{#                    <img src="{% static 'images/icon-play-stop-new.svg' %}" alt="play stop">#}
{#                </a>#}
{#                 <a href="javascript:void(0);" class="ms-2">#}
{#                    <img src="{% static 'images/icon-add-new.svg' %}" alt="add new">#}
{#                </a>#}
{#                 <a href="javascript:void(0);" class="ms-auto">#}
{#                    <img src="{% static 'images/icon-send-new.svg' %}" alt="send message">#}
{#                </a>#}
{#            </div>#}
{#        </div>#}
      {% endif %}

    </div>
  </div>
</div>