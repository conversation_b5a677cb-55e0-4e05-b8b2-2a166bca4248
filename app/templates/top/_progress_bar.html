{% load util %}
{% load static %}

{% with done_count=project.current_heart scene_count=project.current_scene %}
  <div class="progress" data-done-scene="{{ done_count }}" data-current-scene="{{ scene_count }}"
       data-type="{{ type_page }}">
    <span class="p-tooltip p-tooltip--black p-tooltip--total">{{ project.max_scene }}</span>
    <div class="progress-bar" role="progressbar" style="width: {{ project.get_current_heart_rate }}%"
         aria-valuenow="40" aria-valuemin="0"
         aria-valuemax="100">
      <span class="p-tooltip p-tooltip--blue"><i
              class="icon icon--sicon-heart"></i><span>{{ done_count|floatformat:"0" }}</span></span>
    </div>
    <div class="progress-bar progress-bar-black" role="progressbar"
         style="width: {{ project.get_current_scene_rate }}%" aria-valuenow="50"
         aria-valuemin="0" aria-valuemax="100">
      <span class="p-tooltip p-tooltip--black p-tooltip_two"><i
              class="icon icon--sicon-heart-o"></i><span>{{ scene_count|minus:done_count|floatformat:"0" }}</span></span>
    </div>
  </div>
{% endwith %}

