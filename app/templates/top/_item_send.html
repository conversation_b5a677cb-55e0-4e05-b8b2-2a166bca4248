{% load static %}
{% load util %}

{% if type_file == 'audio' %}
    <div class="comment-audio-content comments-audio-block ">
        <div class="mmessenger mmessenger--audio-wave mmessenger--black audio-message" data-file-id="{{ file.pk }}">
            <div class="messenger-content">
                <div class="s-audio s-audio--audio-wave s-audio--black"
                     {% if type not in 'product, messenger_owner, messenger' %}data-scene-id="{{ message.scene_id }}{% endif %}">
                    <div class="s-audio-control
                            {% if type not in 'product, messenger_owner, messenger' %}{% if message.pin_video %}video-pin-time{% endif %}{% endif %}">
                        <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                            play_circle
                        </span>
                        <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                    </div>
                    <div class="video-pin-start hide">
                        {% if type not in 'product, messenger_owner, messenger' %}
                            {{ message.pin_time }}{% endif %}</div>
                    <div class="s-audio-source" data-link="{{ file.file.url }}" title="{{ file_name }}"
                         data-waveColor="#d3d3d3" data-progressColor="#53565a"
                         data-peaks-loaded="{{ file.peaks }}"></div>
                    <div class="s-audio-waveform"></div>
                    <div class="s-audio-time"></div>
                </div>
            </div>
        </div>
        <div class="info-item-audio-comment {% if scene.production_file %}tfile-producttion-file {% endif %}block-download-file"
             data-scene-title-id="{{ scene.pk }}"
             data-file-id="{{ file.pk }}">
            <div class="info-audio-message">
                <div class="block-name-action-audio">
                    <p class="file-name-message file-name-cmt">{{ file_name }} </p>
                    <div class="block-btn-action-audio-msg">
                        {% if file.is_audio_file in 'audio,video' and file.acr_status in '1,2,3' %}
                            {% if file.acr_status == '3' %}
                                <a href="javascript:void(0);" class="acr-result-icon btn-finger-print active"
                                   data-file-id="{{ file.pk }}">
                                    <img src="{% static 'images/scene-detail/icon-finger-print-active.svg' %}"
                                         class=""
                                         alt="finger print">
                                </a>
                            {% else %}
                                <a href="javascript:void(0);" class="acr-result-icon btn-finger-print">
                                    <img src="{% static 'images/scene-detail/icon-finger-print.svg' %}" class=""
                                         alt="finger print">
                                </a>
                            {% endif %}
                        {% endif %}
                        <a href="javascript:void(0);"
                           class="btn-download-file"
                        >
                        <span class="material-symbols-rounded scene-file-download">
                            download
                        </span>
                        </a>
                    </div>
                </div>
                <div class="audio-type-info-msg info-message-file">
                    {% with file.file_info|parse_json_message_info:'audio' as result %}
                        {% if result is not None %}
                            <div class="file-info-message">
                                <span>{{ result.sample_rate }} </span>
                                <span>{{ result.bit_depth }} </span>
                                <span>{{ result.channel_type }} </span>
                                <span>{{ result.loudness }}</span>
                            </div>
                        {% endif %}
                    {% endwith %}
                    <div class="peoples-downloaded-audio-msg">
                        {% with file|get_list_user_download:type as user_dowloadeds %}
                            <div class="has_user_downloaded">
                                {% include 'top/_sview_user.html' with file=file type_comment=type user_dowloadeds=user_dowloadeds %}
                            </div>
                        {% endwith %}
                    </div>
                </div>
            </div>
        </div>
    </div>

{% else %}
    {% if type not in 'product, messenger_owner, messenger' and message.pin_video %}
        <div class="s-audio s-audio--audio s-audio--black" data-scene-id="{{ message.scene_id }}">
            <div style="display: flex">
                <div class="s-audio-control video-pin-time">
                    <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                        play_circle
                    </span>
                    <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                </div>
                <div class="s-audio-time video-pin-start">{{ message.pin_time }}</div>
            </div>
            <div class="s-audio-text s-audio-file">
                <div class="mmessenger mmessenger--file mmessenger--black {% if type_file == 'folder' %}messager-folder{% endif %}"
                     data-toggle="modal"
                     data-target="#modal-{{ type_file }}-popup" data-link="{{ file.file.url }}"
                     data-name="{{ file_name }}" data-type="{{ type_file }}" data-file-id="{{ file.pk }}">
                    <div class="messenger-content">
                        <div class="s-file s-file--file s-file--black">
                            <i class="{% if type_file == 'folder' %}icon icon--sicon-storage{% else %}icon icon--sicon-clip{% endif %}"></i>{{ file_name }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% else %}
        <div class="mmessenger mmessenger--file mmessenger--black block-download-file {% if type_file == 'folder' %}messager-folder{% endif %}"
             data-toggle="modal"
             data-target="#modal-{{ type_file }}-popup" data-link="{{ file.file.url }}"
             data-name="{{ file_name }}" data-type="{{ type_file }}" data-file-id="{{ file.pk }}">
            <div class="messenger-content">
                <div class="s-file s-file--file s-file--black {% if type_file == 'image' %}messenger-image-preview-content {% elif type_file == 'video' %}message-video-content {% elif type_file == 'document' %}message-document-content {% elif type_file == 'folder' %}message-folder-content {% else %}other-file{% endif %}">
                    {% if type_file == 'image' %}
                        <div class="image-preview-comment {% if type_file == 'image' %}active-view{% endif %}">
                            <img src="{{ file.file.url }}" alt="{{ file_name }}" loading="lazy">
                        </div>
                        <div class="comment-file-content">
                            <span style="word-break: break-all;">{{ file_name }}</span>
                            <div class="btn-download-file-cmt "
                                 data-scene-title-id="{{ scene.pk }}"
                                 data-file-id="{{ file.pk }}">
                                <a href="javascript:void(0);"
                                   class="btn-download-file"
                                   data-scene-title-id="{{ scene.pk }}"
                                   data-file-id="{{ file.pk }}"
                                >
                                <span class="material-symbols-rounded scene-file-download">
                                    download
                                </span>
                                </a>
                            </div>
                        </div>
                        <div class="info-message-image info-message-file">
                            {% with file.file_info|parse_json_message_info:'image' as result %}
                                {% if result is not None %}
                                    <div class="size-file-message">
                                        <span>{{ result.width }} x {{ result.height }}px </span>
                                    </div>
                                {% endif %}
                            {% endwith %}
                            <div class="user-download-file">
                                {% with file|get_list_user_download:type_comment as user_dowloadeds %}
                                    <div class="has_user_downloaded">
                                        {% include 'top/_sview_user.html' with file=file type_comment=type_comment user_dowloadeds=user_dowloadeds %}
                                    </div>
                                {% endwith %}
                            </div>
                        </div>
                    {% elif type_file == 'video' %}
                        <div class="block-video-cmt" style="width: 100%">
                            {% render_video_with_hls file height="144px" preload="metadata" loading="lazy" id="send_video_"|add:file.pk as video_html %}
                              {% if video_html %}
                                {{ video_html }}
                              {% else %}
                                <video height="144px"
                                       preload="metadata" loading="lazy">
                                    <source src="{{ file.file.url }}"
                                            type="video/mp4"/>
                                </video>
                              {% endif %}
                            {% endwith %}
                        </div>
                        <div class="comment-file-content ">
                            <p style="word-break: break-all;" class="file-name">{{ file_name }}</p>
                            <div class="action-right-video">
                                {% if file.is_audio_file in 'audio,video' and file.acr_status in '1,2,3' %}
                                    {% if file.acr_status == '3' %}
                                        <a href="javascript:void(0);"
                                           class="acr-result-icon btn-finger-print active"
                                           data-file-id="{{ file.pk }}">
                                           <span class="material-symbols-rounded scene-file-download">
                                                download
                                            </span>
                                        </a>
                                    {% else %}
                                        <a href="javascript:void(0);" class="acr-result-icon btn-finger-print">
                                            <img src="{% static 'images/scene-detail/icon-finger-print.svg' %}"
                                                 class=""
                                                 alt="finger print">
                                        </a>
                                    {% endif %}
                                {% endif %}
                                <a href="javascript:void(0);"
                                   class="btn-download-file"
                                >
                                <span class="material-symbols-rounded scene-file-download">
                                    download
                                </span>
                                </a>
                            </div>
                        </div>
                        <div class="info-message-video info-message-file">
                            <div class="size-file-message">
                                {% with file.file_info|parse_json_message_info:'video' as result %}
                                    {% if result is not None %}
                                        <span>{{ result.width }} x {{ result.height }}px </span>
                                        <span>{{ result.fps }}fps</span>
                                    {% endif %}
                                {% endwith %}
                            </div>
                            <div class="user-download-file">
                                {% with file|get_list_user_download:type_comment as user_dowloadeds %}
                                    <div class="has_user_downloaded">
                                        {% include 'top/_sview_user.html' with file=file type_comment=type_comment user_dowloadeds=user_dowloadeds %}
                                    </div>
                                {% endwith %}
                            </div>
                        </div>
                    {% elif type_file == 'document' %}
                        <div class="block-pdf-image">
                              {% with file.file_info|parse_json_message_info:'document' as result %}
                                {% if result is not None %}
                                    <img src="{{ result.url_image|get_presigned_url_message }}" class="pdf-image"
                                         alt="pdf image">
                                {% endif %}
                            {% endwith %}
                        </div>
                        <div class="comment-file-content ">
                            <p style="word-break: break-all;" class="file-name">{{ file_name }}</p>
                            <div class="btn-download-file-cmt">
                                <a href="javascript:void(0);"
                                   data-scene-title-id="{{ scene.pk }}"
                                   data-file-id="{{ file.pk }}"
                                   class="btn-download-file {% if scene.production_file %} tfile-producttion-file {% endif %}block-download-file"
                                >
                                    <span class="material-symbols-rounded scene-file-download">
                                        download
                                    </span>
                                </a>
                            </div>
                        </div>
                        <div class="info-message-file">
                            <div class="size-file-message">
                                {% with file.file_info|parse_json_message_info:'document' as result %}
                                    {% if result is not None %}
                                        <span>{{ result.width }} x {{ result.height }}px</span>
                                    {% endif %}
                                {% endwith %}
                            </div>
                            <div class="user-download-file">
                                {% with file|get_list_user_download:type_comment as user_dowloadeds %}
                                    <div class="has_user_downloaded">
                                        {% include 'top/_sview_user.html' with file=file type_comment=type_comment user_dowloadeds=user_dowloadeds %}
                                    </div>
                                {% endwith %}
                            </div>
                        </div>
                    {% elif type_file == 'folder' %}
                        <div class="left-folder-message">
                            <img src="{% static 'images/scene-detail/icon-folder.svg' %}" class="icon-folder-left"
                                 alt="folder icon">
                            <p class="file-name-cmt" style="word-break: break-all;">{{ file_name }}</p>
                        </div>
                        <img src="{% static 'images/scene-detail/icon-navigate-next.svg' %}" alt="navigation next">
                    {% else %}
                        <div class="comment-file-content ">
                            <p style="word-break: break-all;" class="file-name">
                                {% if file_name|length > 25 %}{{ file_name|slice:"0:25" }} {% else %} {{ file_name }}{% endif %}
                            </p>
                            <div class="btn-download-file-cmt">
                                <a href="javascript:void(0);"
                                   data-scene-title-id="{{ scene.pk }}"
                                   data-file-id="{{ file.pk }}"
                                   class="btn-download-file {% if scene.production_file %} tfile-producttion-file {% endif %}block-download-file"
                                >
                                <span class="material-symbols-rounded scene-file-download">
                                    download
                                </span>
                                </a>
                            </div>
                        </div>
                        <div class="info-message-file">
                            <div class="size-file-message">
                                {% if file.file_info is not None %}
                                    {% with file.file_info|parse_json_message_info:'other' as result %}
                                        {% if result is not None %}
                                            <span>{{ result.size_converted }}</span>
                                        {% endif %}
                                    {% endwith %}
                                {% endif %}
                            </div>
                            <div class="user-download-file">
                                {% with file|get_list_user_download:type_comment as user_dowloadeds %}
                                    <div class="has_user_downloaded">
                                        {% include 'top/_sview_user.html' with file=file type_comment=type_comment user_dowloadeds=user_dowloadeds %}
                                    </div>
                                {% endwith %}
                            </div>
                        </div>

                    {% endif %}
                </div>
                {% if type_file == 'folder' %}
                    {% with  file|get_total_file_in_folder as total_files %}
                        <p class="txt-total-file">
                            {{ total_files }}{% if total_files > 1 %} files{% else %} file{% endif %}</p>
                    {% endwith %}
                {% endif %}
            </div>
        </div>
    {% endif %}
{% endif %}

{##}
{#{% if type_file == 'audio' %}#}
{#  <div class="mmessenger mmessenger--audio-wave mmessenger--black" data-file-id="{{ file.pk }}">#}
{#    <div class="messenger-content">#}
{#      <div class="s-audio s-audio--audio-wave s-audio--black"#}
{#           {% if type not in 'product, messenger_owner, messenger' %}data-scene-id="{{ message.scene_id }}{% endif %}">#}
{#        <div class="s-audio-control#}
{#                {% if type not in 'product, messenger_owner, messenger' %}{% if message.pin_video %}video-pin-time{% endif %}{% endif %}">#}
{#          <i class="icon icon--sicon-pause"></i>#}
{#          <i class="icon icon--sicon-play"></i>#}
{#        </div>#}
{#        <div class="video-pin-start hide">{% if type not in 'product, messenger_owner, messenger' %}{{ message.pin_time }}{% endif %}</div>#}
{#        <div class="s-audio-source" data-link="{{ file.file.url }}" title="{{ file_name }}"#}
{#             data-waveColor="#d3d3d3" data-progressColor="#53565a"#}
{#             data-peaks-loaded="{{ file.peaks }}"></div>#}
{#        <div class="s-audio-waveform"></div>#}
{#        <div class="s-audio-time"></div>#}
{#      </div>#}
{#    </div>#}
{#  </div>#}
{#{% else %}#}
{#  {% if type not in 'product, messenger_owner, messenger' and message.pin_video %}#}
{#    <div class="s-audio s-audio--audio s-audio--black" data-scene-id="{{ message.scene_id }}">#}
{#      <div style="display: flex">#}
{#        <div class="s-audio-control video-pin-time"><i class="icon icon--sicon-pause"></i><i#}
{#                class="icon icon--sicon-play"></i>#}
{#        </div>#}
{#        <div class="s-audio-time video-pin-start">{{ message.pin_time }}</div>#}
{#      </div>#}
{#      <div class="s-audio-text s-audio-file">#}
{#        <div class="mmessenger mmessenger--file mmessenger--black {% if type_file == 'folder' %}messager-folder{% endif %}"#}
{#             data-toggle="modal"#}
{#             data-target="#modal-{{ type_file }}-popup" data-link="{{ file.file.url }}"#}
{#             data-name="{{ file_name }}" data-type="{{ type_file }}" data-file-id="{{ file.pk }}">#}
{#          <div class="messenger-content">#}
{#            <div class="s-file s-file--file s-file--black">#}
{#              <i class="{% if type_file == 'folder' %}icon icon--sicon-storage{% else %}icon icon--sicon-clip{% endif %}"></i>{{ file_name }}#}
{#            </div>#}
{#          </div>#}
{#        </div>#}
{#      </div>#}
{#    </div>#}
{#  {% else %}#}
{#    <div class="mmessenger mmessenger--file mmessenger--black {% if type_file == 'folder' %}messager-folder{% endif %}"#}
{#         data-toggle="modal"#}
{#         data-target="#modal-{{ type_file }}-popup" data-link="{{ file.file.url }}"#}
{#         data-name="{{ file_name }}" data-type="{{ type_file }}" data-file-id="{{ file.pk }}">#}
{#      <div class="messenger-content">#}
{#        <div class="s-file s-file--file s-file--black {% if type_file == 'image' %}messenger-image-preview-content{% endif %}">#}
{#          {% if type_file == 'image' %}#}
{#          <div class="comment-file-content">#}
{#            <i class="{% if type_file == 'folder' %}icon icon--sicon-storage{% else %}icon icon--sicon-clip{% endif %}"></i><p style="word-break: break-all;">{{ file_name }}</p>#}
{#          </div>#}
{#          <div class="image-preview-comment {% if type_file == 'image' %}active-view{% endif %}">#}
{#            <img src="{{ file.file.url }}" alt="{{ file_name }}">#}
{#          </div>#}
{#          {% else %}#}
{#          <i class="{% if type_file == 'folder' %}icon icon--sicon-storage{% else %}icon icon--sicon-clip{% endif %}"></i><p style="word-break: break-all;">{{ file_name }}</p>#}
{#          {% endif %}#}
{#        </div>#}
{#      </div>#}
{#    </div>#}
{#  {% endif %}#}
{#{% endif %}#}
