{% load static %}
{% load util %}
{% for other_version in scene.other_versions.all %}
  <div class="video-item-component{% if forloop.first %} active{% endif %}" data-scene-id="{{ other_version.pk }}">
    {% with  other_version.is_audio_file as type_scene %}
      {% if scene.title.status == '1' and role != 'admin' %}
        <div class="new-tag">NEW</div>
      {% endif %}
      <div class="version-tag after video-modal-version ">{{ forloop.revcounter|add:1 }}</div>
      <div class="video-item-component-content">
        <div class="video-item-component-content-video" data-peaks-loaded="{{ other_version.peaks }}"
             data-scene-id="{{ other_version.pk }}">
          {% if type_scene == 'document' %}
            <iframe width="100%" height="100%" class="scrollbar pdf-component"
                    src="/static/pdfjs/web/viewer.html?file={{ other_version.movie.url|urlencode }}#zoom=page-width"></iframe>
          {% elif type_scene == 'video' %}
            {% with other_version|render_video_with_hls:width="100%",height="auto",poster=other_version|get_thumbnail,preload="none",id="video_"|add:other_version.pk as video_html %}
              {% if video_html %}
                {{ video_html }}
              {% else %}
                <video width="100%" height="auto" poster="{{ other_version|get_thumbnail }}" preload="none">
                  <source src="{{ other_version.movie.url }}" type="video/mp4">
                </video>
              {% endif %}
            {% endwith %}
            <div class="play-video"></div>
          {% elif type_scene == 'audio' %}
            <div class="video-comment-audio-wave audio-component" data-audio="{{ other_version.movie.url }}"></div>
            <div class="video-pin-time pin-time-audio">
            </div>
          {% endif %}
          {% if scene_count == 1 %}
            <div class="video-item-chapter"
                 title="{{ scene.title.product_scene.name }} | {{ scene.title.title }}"><span
                    class="video-product-scene-name"> {{ scene.title.product_scene.name }} </span>
              <span class="name-gray">|</span> {{ scene.title.title }}
            </div>
          {% else %}
            <div class="video-item-chapter"
                 title="{{ scene.title.product_scene.name }} | {{ scene.title.title }} | {{ other_version.get_file_name }}">
              <span class="video-product-scene-name"> {{ scene.title.product_scene.name }} </span>
              <span class="name-gray">|</span> {{ scene.title.title }} <span class="name-gray">|</span><span
                    class="video-variation-name"> {{ other_version.get_file_name }} </span>
            </div>
          {% endif %}

          {% if not is_deleted %}
            {% if can_share_link %}
              <div class="video-item-slug"
                   share-url="{% url 'app:scene_show' other_version.get_slug %}"></div>
              <a class="video-button video-item-share" href="javascript:void(0)" data-toggle="modal"
                 data-target="#shareModal"></a>
            {% endif %}
            {% if not role == 'admin' and scene.title.status == '1' %}
              <div class="video-button video-item-button-bottom mark-as-ok">とりあえずOK</div>
            {% else %}
              {% if role == 'admin' and not scene.title.is_done %}
                <div class="video-button video-item-button-bottom upload_version_admin">アップデート</div>
              {% else %}
                {% if not scene.title.is_done %}
                  <div class="video-button video-item-button-bottom show-comment-dom">チェックバック</div>
                {% endif %}
              {% endif %}
            {% endif %}
          {% endif %}
          {% if type_scene == 'video' %}
            <div class="video-button video-item-button-left">もういちど</div>
          {% endif %}
        </div>
      </div>
    {% endwith %}
  </div>
{% endfor %}
{% with scene.other_versions.count as other_variation_count %}
  {% with scene.is_audio_file as type_scene %}
    <div class="video-item-component{% if other_variation_count == 0 %} active{% endif %}"
         data-scene-id="{{ scene.pk }}">
      {% if scene.title.status == '1' and role != 'admin' %}
        <div class="new-tag">NEW</div>
      {% endif %}
      {% if other_variation_count > 0 %}
        <div class="version-tag after video-modal-version ">1</div>{% endif %}
      <div class="video-item-component-content">
        <div class="video-item-component-content-video" data-peaks-loaded="{{ scene.peaks }}"
             data-scene-id="{{ scene.pk }}">
          {% if type_scene == 'video' %}
            {% with scene|render_video_with_hls:width="100%",height="auto",poster=scene|get_thumbnail,preload="none",id="video_"|add:scene.pk as video_html %}
              {% if video_html %}
                {{ video_html }}
              {% else %}
                <video width="100%" height="auto" poster="{{ scene|get_thumbnail }}" preload="none">
                  <source src="{{ scene.movie.url }}" type="video/mp4">
                </video>
              {% endif %}
            {% endwith %}
            <div class="play-video"></div>

          {% elif type_scene == 'document' %}
            <iframe width="100%" height="100%" class="scrollbar pdf-component" src="/static/pdfjs/web/viewer.html?file={{ scene.movie.url|urlencode }}#zoom=page-width"></iframe>
          {% elif type_scene == 'audio' %}
            <div class="video-comment-audio-wave audio-component" data-audio="{{ scene.movie.url }}"></div>
            <div class="video-pin-time pin-time-audio">

            </div>
          {% endif %}
          {% if scene_count == 1 %}
            <div class="video-item-chapter"
                 title="{{ scene.title.product_scene.name }} | {{ scene.title.title }}"><span
                    class="video-product-scene-name"> {{ scene.title.product_scene.name }} </span>
              <span class="name-gray">|</span> {{ scene.title.title }}
            </div>
          {% else %}
            <div class="video-item-chapter"
                 title="{{ scene.title.product_scene.name }} | {{ scene.title.title }} | {{ scene.get_file_name }}">
              <span class="video-product-scene-name"> {{ scene.title.product_scene.name }} </span>
              <span class="name-gray">|</span> {{ scene.title.title }} <span class="name-gray">|</span><span
                    class="video-variation-name"> {{ scene.get_file_name }} </span>
            </div>
          {% endif %}
          {% if not is_deleted %}
            {% if can_share_link %}
              <div class="video-item-slug" share-url="{% url 'app:scene_show' scene.get_slug %}"></div>
              <a class="video-button video-item-share" href="javascript:void(0)" data-toggle="modal"
                 data-target="#shareModal"></a>
            {% endif %}
            {% if not role == 'admin' and scene.title.status == '1' %}
              <div class="video-button video-item-button-bottom mark-as-ok">とりあえずOK</div>
            {% else %}
              {% if role == 'admin' and not scene.title.is_done %}
                <div class="video-button video-item-button-bottom upload_version_admin">アップデート</div>
              {% else %}
                {% if not scene.title.is_done %}
                  <div class="video-button video-item-button-bottom show-comment-dom">チェックバック</div>
                {% endif %}
              {% endif %}
            {% endif %}
          {% endif %}
          {% if type_scene == 'video' %}
            <div class="video-button video-item-button-left">もういちど</div>
          {% endif %}
        </div>
      </div>
    </div>
  {% endwith %}
{% endwith %}
