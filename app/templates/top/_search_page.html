{% load static %}
{% load util %}
<div class="project-chapter-item-search  tab--video-all">
  <h2>検索結果</h2>
  <div class="project-chapter ui-sortable">
    <hr>
    <div class="project-chapter-item sortable-element hide" style="">
      <div class="project-chapter-title ui-sortable-handle"></div>
      <div class="project-chapter-videos ui-sortable" id="id-sort-videos">
      </div>
    </div>
    {% if not has_result %}
      「{{ keyword }}」に一致するシーンは見つかりませんでした。
    {% else %}
      <div class="pd-section pd-section--all-video pd-section--search-video">

        {% include 'top/_menu_sort.html' %}

        <div class="pd-section__main">
          <div class="pd-section__content">
            <div class="pd-chapter-list">
              {% for ps in productscenes %}
                {% if ps.title_product_scene|check_null %}
                  <div class="pd-chapter active" data-index="{{ ps.order }}" data-product-scene-id="{{ ps.pk }}">
                    <div class="pd-chapter__title active">{{ ps.name }}
                      <div class="pd-chapter__line"></div>
                    </div>
                    <div class="pd-chapter__content" style="display: block;">
                      <div class="pd-chapter__list mscrollbar">

                        {% for st in ps.title_product_scene.all %}
                          {% if st.scene_title.exists %}
                            {% if st.scene_title.all.0.other_versions.exists %}
                              {% with st.scene_title.all.0.other_versions.all.0 as scene %}
                                {% include 'top/_cscene.html' with st=st scene=scene role=role is_done=is_done %}
                              {% endwith %}
                            {% else %}
                              {% with st.scene_title.all.0 as scene %}
                                {% include 'top/_cscene.html' with st=st scene=scene role=role is_done=is_done %}
                              {% endwith %}
                            {% endif %}
                          {% endif %}
                        {% endfor %}
                      </div>
                    </div>
                  </div>
                {% endif %}
              {% endfor %}
            </div>
          </div>
        </div>
      </div>

    {% endif %}
  </div>
</div>
