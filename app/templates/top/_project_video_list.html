{% load static %}
{% load util %}
<style>
    .video-item-comment-resolved {
        height: 45px;
    }

    .asc, .desc {
        display: none;
    }

    .asc.active, .desc.active {
        display: block;
    }

    .asc.active:before {
        content: '▲'
    }

    .desc.active:before {
        content: '▼'
    }

    .asc, .desc {
        height: 20px;
        width: 10px;
    }

    .asc.active:hover:before,
    .desc.active:hover:before {
        color: #0076a5;
        cursor: pointer;
    }

    .product-scene-list-header {
        display: flex;
        justify-content: space-between;
    }

    .sort-selector {
        margin: auto 15px auto auto;
    }

    .SumoSelect>.CaptionCont>span {
        padding-right: 0;
        cursor: pointer;
    }

    .messenger__order-right .sumo-select {
        min-width: 40px;
    }

    .SumoSelect.open>.optWrapper {
        width: auto;
    }
</style>
<div class="product-scene-list-header">
    <div class="project-video-item">
        <div class="video-item-comment-resolved">
            <div class="button-switch">
                <input class="switch" type="checkbox" id="id-sort-switch" checked/>
            </div>
            <div class="video-item-comment-resolved-label">チャプターごとに分ける</div>
        </div>
    </div>
    <div class="scene-filter filter-all"></div>
    <div class="sort-selector">
        <div class="messenger__header">
            <div class="messenger__order-right">
                <div class="form-group select-container select_order sumo-select">
                    <select class="select__value-list" name="sort-type" id="id-sort-type"
                            data-placeholder="">
                        <option value="title" selected>シーン名</option>
                        <option value="modified">更新日</option>
                        <option value="priority">優先順</option>
                    </select>
                </div>
                <div data-value="ascending" class="sort-direction asc"></div>
                <div data-value="descending" class="sort-direction desc active"></div>
            </div>
        </div>
    </div>
</div>
<hr>

<div class="product-scene-list">
  <div class="project-chapter-item sortable-element hide">
    <div class="project-chapter-title ui-sortable-handle"></div>
    <div class="project-chapter-videos ui-sortable" id="id-sort-videos">
    </div>
  </div>
  {% for ps in product_scenes %}
    {% if ps.title_product_scene|check_null %}
      <div class="project-chapter-item" data-index="{{ ps.order }}" data-product-scene-id="{{ ps.pk }}">
        <div class="project-chapter-title">
          <div class="productscene-name">{{ ps.name }}</div>
          <div class="project-chapter-title-edit comment-edit-button-group">
            <button class="edit-product-scene">
              <img src="{% static 'images/edit.svg' %}" alt="">
            </button>
            <button class="delete-product-scene">
              <img src="{% static 'images/delete.svg' %}" alt="">
            </button>
          </div>
        </div>
        <div class="project-chapter-videos">
          {% for st in ps.title_product_scene.all %}
            {% if st.scene_title.exists %}
              {% if st.scene_title.all.0.other_versions.exists %}
                {% with st.scene_title.all.0.other_versions.all.0 as scene %}
                  {% include 'top/_process_bottom_video.html' with st=st scene=scene role=role is_done=is_done %}
                {% endwith %}
              {% else %}
                {% with st.scene_title.all.0 as scene %}
                  {% include 'top/_process_bottom_video.html' with st=st scene=scene role=role is_done=is_done %}
                {% endwith %}
              {% endif %}
            {% endif %}
          {% endfor %}
        </div>
      </div>
    {% endif %}
  {% endfor %}
</div>

{% if deleted_product_scenes %}
  <hr>
  <h3>削除したチャプター</h3>
  {% for ps in deleted_product_scenes %}
    {% if ps.title_product_scene|check_null %}
        <div class="project-chapter-item" data-index="{{ ps.order }}" data-product-scene-id="{{ ps.pk }}">
            <div class="project-chapter-title-deleted">
                <div class="productscene-name">{{ ps.name }}</div>
            </div>
            <div class="project-chapter-videos project-chapter-videos-deleted">
                {% for st in ps.title_product_scene.all %}
                    {%  if st.scene_title.exists %}
                        {% if st.scene_title.all.0.other_versions.exists %}
                            {% with st.scene_title.all.0.other_versions.all.0 as scene %}
                                {% include 'top/_process_bottom_video.html' with st=st scene=scene role=role is_done=is_done deleted=True %}
                            {% endwith %}
                        {% else %}
                            {% with st.scene_title.all.0 as scene %}
                                {% include 'top/_process_bottom_video.html' with st=st scene=scene role=role is_done=is_done deleted=True %}
                            {% endwith %}
                        {% endif %}
                    {% endif %}
                {% endfor %}
            </div>
        </div>
    {% endif %}
  {% endfor %}
{% endif %}
