{% load static %}
{% load util %}

{% comment %} <div class="cvideo cvideo__thumb-list-update"> {% endcomment %}
  <div class="project-delivery-item-content item-delivery-video" data-scene-title-id="{{ st.pk }}"
       data-status="{{ st.status }}" data-rating="{{ st|get_rating:role }}"
       data-title="{{ st.title }}" data-scene-id="{{ scene.pk }}">
       <div style="display: flex; justify-content: space-between;">
       <div class="cvideo__title bodytext-1316">{{ st.title }}が届いています</div>
       <div class="cvideo__heading">
         <div class="cvideo__meta">
           <div class="cvideo__date-time">
             <div class="cvideo__date">{{ st.updated_at|get_updated_time }}</div>
             <div class="cvideo__time">{{ st.updated_at|date:"H:i" }}</div>
           </div>
         </div>
       </div>
      </div>
    <div class="cvideo__thumb cvideo__thumb-list-update">
      <video width="100%" height="100%" poster="{{ scene|get_thumbnail }}"
             preload="none" loading="lazy" style="border-radius: 6px;">
        <source src="{% if scene.movie %}{{ scene.movie.url }}{% endif %}" type="video/mp4">
      </video>
    </div>
    {% with st|get_last_message_title:role as last_comment %}
      {% if last_comment %}
        <div class="cvideo__last-msg">

          <!-- Comment file -->
          <div class="msg__wrap">
            <div class="msg__avatar">
              <img src="{{ last_comment.user|get_avatar:'small' }}" alt="" width="32" height="32">
            </div>
            {% if last_comment.comment and last_comment.comment != '' %}
              <div class="msg__info">
                <div class="bodytext-13 u-line-height-150">{{ last_comment.comment }}</div>
              </div>

            {% else %}

              <div class="msg__file-text-none">
                {% for folder in last_comment.folders.all %}
                  {% if not folder.parent %}
                    <div class="msg__file"><i class="icon icon--sicon-storage"></i>
                      <div class="msg__file-name">{{ folder.name }}</div>
                    </div>
                  {% endif %}
                {% endfor %}

                {% for file in last_comment.files.all %}
                  {% if not file.folder %}
                    <div class="msg__file"><i class="icon icon--sicon-clip"></i>
                      <div class="msg__file-name">{{ file.real_name }}</div>
                    </div>
                  {% endif %}
                {% endfor %}
              </div>

            {% endif %}
          </div>
          {% if last_comment.comment and last_comment.comment != '' %}
            {% for folder in last_comment.folders.all %}
              {% if not folder.parent %}
                <div class="msg__file"><i class="icon icon--sicon-storage"></i>
                  <div class="msg__file-name">{{ folder.name }}</div>
                </div>
              {% endif %}
            {% endfor %}

            {% for file in last_comment.files.all %}
              {% if not file.folder %}
                <div class="msg__file"><i class="icon icon--sicon-clip"></i>
                  <div class="msg__file-name">{{ file.real_name }}</div>
                </div>
              {% endif %}
            {% endfor %}
          {% endif %}
        </div>
      {% endif %}
    {% endwith %}

    <div style="display: flex; justify-content: flex-end;">
    {% if st.status not in '5,6' %}
    <div title=""
         class="{{ show_button }} project-chapter-video-done {% if role == 'admin' or view_only %}cannot-check{% endif %}">
         <span class="material-symbols-rounded">
          check_circle
        </span>
      </div>
  {% else %}
    <div title=""
         class="{{ show_button }} project-chapter-video-undone {% if view_only %}cannot-check{% endif %}">
         <span class="material-symbols-rounded u-text-blue">
          check_circle
        </span>
      </div>
  {% endif %}
    </div>
  </div>
{% comment %} </div> {% endcomment %}
