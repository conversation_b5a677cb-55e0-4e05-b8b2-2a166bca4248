{% load util %}
{% load static %}
{% load i18n %}

{% with notification=user|check_notification_product:project project_user=user|get_product_user:project owners=project|get_owners_project font=project.font_setting color=project.color_setting %}
    <div id="sheaderplaceholder" class="sheader-pc-placeholder"></div>
    <div class="pbanner new-banner-project-refactor banner-project-backgroud {{ color.code }} {% if not notification and type_page == 'top_page' %}no-notification {% endif %}"
         data-project-role="{{ project_user.position }}" {% if color %} style='{{ color.css_code|safe }}' {% endif %}>
		 {% comment %} 左のコードネーム {% endcomment %}
        <div class="block-project-left bodytext-16">
            <div class="code-name-block">
                <div class="code-name" {% if font and color %} style='{{ font.css_code|safe }} {{ color.text_color_css }}' {% endif %}>{{ title_page }}</div>
            </div>
        </div>
		{% comment %} 右のユーザーリストとスケジュール {% endcomment %}
        <div class="block-project-right">
            <div class="block-user-avatar">
				{% comment %} クライアント側のアバター描画 {% endcomment %}
                {% if owners %}
                    {% for owner in owners|slice:":5" %}
                        <img class="avatar-user-project"
                             data-user="{{ owner.pk }}"
                             title="{{ owner.get_display_name }}"
                             src="
                                  {% if owner.avatar %}{{ owner|get_avatar:'small' }}{% else %}/static/images/default-avatar-owner.png{% endif %}"
                             alt="">
                    {% endfor %}
                    {% if owners|length > 5 %}
                        <div class="dot-more-user">...</div>
                    {% endif %}
                {% endif %}

                <span class="c-vertical-line"></span>

				{% comment %} 管理者とプロデューサーとディレクターのアバター描画 {% endcomment %}
                {% with project.get_director_producer_master_admin_in_project|check_owner_product:user as admins %}
                    {% if admins %}
                        {% for admin in admins|slice:":5" %}
                            <img class="avatar-user-project"
                                 title="{{ admin.get_display_name }}" data-user="{{ admin.pk }}"
                                 src="{{ admin|get_avatar:'small' }}" alt="">
                        {% endfor %}
                        {% if admins|length > 5 %}
                            <div class="dot-more-user">...</div>
                        {% endif %}
                    {% endif %}
                {% endwith %}
            </div>
			{% include 'top/_modal_users_join_project.html' with project_list=False %}

			{% comment %} スケジュールの描画 {% endcomment %}
			<div class="back-button-block schedule-project-detail schedule-icon-modal">
					<span class="material-symbols-rounded calendar-schedule">event_note</span>
					{% include 'top/_modal_schedule.html' %}
				</div>
			</div>
		</div>

{% comment %} アバターの重なり順を逆にする {% endcomment %}
<script>
    window.onload = function() {
        var avatars = document.querySelectorAll('.avatar-user-project');
        for (var i = 0; i < avatars.length; i++) {
            avatars[i].style.position = 'relative';
            avatars[i].style.zIndex = avatars.length - i;
        }
    }
</script>

{% comment %} フォントの読み込み {% endcomment %}
<script>
    (function (d) {
        var config = {
            kitId: 'zsw6vgy',
            scriptTimeout: 3000,
            async: true
        },
            h = d.documentElement, t = setTimeout(function () { h.className = h.className.replace(/\bwf-loading\b/g, "") + " wf-inactive"; }, config.scriptTimeout), tk = d.createElement("script"), f = false, s = d.getElementsByTagName("script")[0], a; h.className += " wf-loading"; tk.src = 'https://use.typekit.net/' + config.kitId + '.js'; tk.async = true; tk.onload = tk.onreadystatechange = function () { a = this.readyState; if (f || a && a != "complete" && a != "loaded") return; f = true; clearTimeout(t); try { Typekit.load(config) } catch (e) { } }; s.parentNode.insertBefore(tk, s)
    })(document);
</script>


{#        <div class="pbanner__top">#}
{#          <div class="pbanner__members">#}
{#            <div class="pbanner__user-list">#}
{#              <div class="" data-toggle="modal" data-target="#modal-member-manage">#}
{#                {% with project|get_member as members %}#}
{#                  {% if owners %}#}
{#                    {% for owner in owners %}#}
{#                      <div class="pbanner__user" data-user="{{ owner.pk }}">#}
{#                        <div class="avatar avatar--image avatar--24 avatar--square background-avt"#}
{#                             title="{{ owner.get_display_name }}">#}
{#                          <div class="avatar-image"#}
{#                               style="background-image: url(#}
{#                                       {% if owner.avatar %}{{ owner|get_avatar:'small' }}{% else %}/static/images/default-avatar-owner.png{% endif %})"></div>#}
{#                        </div>#}
{#                      </div>#}
{#                    {% endfor %}#}
{#                  {% endif %}#}
{#                  {% if members %}#}
{#                    {% for user in members %}#}
{#                      <div class="pbanner__user" data-user="{{ user.pk }}">#}
{#                        <div class="avatar avatar--image avatar--24 avatar--square background-avt"#}
{#                             title="{{ user.get_display_name }}" data-user="{{ user.pk }}">#}
{#                          <div class="avatar-image" style="background-image: url({{ user|get_avatar:'small' }})"></div>#}
{#                        </div>#}
{#                      </div>#}
{#                    {% endfor %}#}
{#                  {% endif %}#}
{##}
{#                  {% if not owners and not members and project|get_member_inviting %}#}
{#                    <div class="pbanner__user">#}
{#                      <div class="avatar avatar--image avatar--24 avatar--square background-avt">#}
{#                        <div class="avatar-image button--show-member button--show-member"#}
{#                             style="background-image: url(/static/images/icon_user.svg)"></div>#}
{#                      </div>#}
{#                    </div>#}
{#                  {% endif %}#}
{#                {% endwith %}#}
{#              </div>#}
{##}
{#              {% if user|is_editable_description:project_user %}#}
{#                <span class="pbanner__user-btn" data-toggle="modal" data-target="#modal-invite-member"#}
{#                      style="margin-bottom: 2px">#}
{#                    <i class="icon icon--sicon-plus"></i>#}
{#                  </span>#}
{#              {% endif %}#}
{#            </div>#}
{#          </div>#}
{#        #}
{#        </div>#}

{#        <div class="pbanner__bottom">#}
{#          {% with project.get_director_producer_master_admin_in_project|check_owner_product:user as admins %}#}
{#            <div class="pbanner__user-list pbanner__admin-list" data-toggle="modal"#}
{#                 data-target="#modal-member-manage">#}
{#              {% if admins %}#}
{#                {% for admin in admins %}#}
{#                  <div class="pbanner__user" data-user="{{ admin.pk }}">#}
{#                    <div class="avatar avatar--image avatar--24 avatar--square background-avt"#}
{#                         title="{{ admin.get_display_name }}">#}
{#                      <div class="avatar-image" style="background-image: url({{ admin|get_avatar:'small' }})"></div>#}
{#                    </div>#}
{#                  </div>#}
{#                {% endfor %}#}
{#                {% else %}#}
{#                <div class="pbanner__user">#}
{#                      <div class="avatar avatar--image avatar--24 avatar--square background-avt">#}
{#                        <div class="avatar-image button--show-member button--show-member"#}
{#                             style="background-image: url(/static/images/icon_user.svg)"></div>#}
{#                      </div>#}
{#                    </div>#}
{#              {% endif %}#}
{#            </div>#}
{#          {% endwith %}#}
{#        </div>#}
{#    </div>#}
{% endwith %}
