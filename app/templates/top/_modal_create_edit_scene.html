{% load static %}
{% load util %}
{% load i18n %}

<!-- first modal -->
<div class="smodal smodal--large modal fade" id="modal-upload-scene" role="dialog">
  <div class="modal-dialog mscrollbar" role="document" style="background-color: #FFFFFF">

    <div class="u-wrapper u-row-center u-fixed">
      <div class="modal-dialog__header__container">
        <hr/>
        <div class="modal-dialog__header__text">
            <h1 style="margin: 0 !important;">シーンの作成</h1>
        </div>
      </div>
    </div>
    <div class="modal-content">
      <div class="create-scene mscrollbar">
          <div class="create-scene__action" style="text-align: right !important;">
          <div class="create-scene__action__container">
            {% include 'buttons/conponent_button.html' with className='btn--tertiary small btn-popup-close' value='キャンセル' attribute='data-dismiss="modal"' %}
            {% include 'buttons/conponent_button.html' with className='btn--primary medium btn-popup-ok btn-upload-scene' value='OK'  disabled='disabled' %}
          </div>
        </div>
        <div class="u-wrapper-reading">
          {% include 'top/_content_create_edit_scene.html' with scenes=scenes original_scene=original_scene %}
        </div>
      </div>
    </div>
  </div>
</div>
<!-- end first modal -->

<!-- modal take -->
<div class="smodal smodal--large modal fade" id="modal-take-scene" role="dialog">
  <div class="modal-dialog mscrollbar" role="document" style="background-color: #FFFFFF">
    <div class="modal-dialog__header">
      <div class="modal-dialog__header__container">
      </div>
    </div>
    <div class="modal-content">
      <div class="take-scene">
        <div class="take-scene-back">
          <svg data-dismiss="modal" class="close-modal-edit-video" xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
            <path d="M19.8802 8.94664C19.3602 8.42664 18.5202 8.42664 18.0002 8.94664L11.8802 15.0666C11.3602 15.5866 11.3602 16.4266 11.8802 16.9466L18.0002 23.0666C18.5202 23.5866 19.3602 23.5866 19.8802 23.0666C20.4002 22.5466 20.4002 21.7066 19.8802 21.1866L14.7069 16L19.8802 10.8266C20.3869 10.3066 20.3869 9.45331 19.8802 8.94664Z" fill="#A7A8A9"/>
          </svg>
        </div>
        <div class="take-scene-name-container">
          <div class="take-scene-name">テイク３</div>
          <div class="take-scene-time">fri</div>
        </div>
        <div class="modal-body" style="padding-top: 0px; padding-bottom: 0px">
          <div class="version-container mscrollbar">
            
            
          </div>
          <div class="upload-container">
            <div class="col-sm-12 form-group" style="margin-bottom: -1px; padding: 0;">
              <label for="id-upload-version">
                <div class="contract__form-label">バリエーションを追加 <span class="grey-label--8">{% trans "any" %}</span></div>
              </label>
              <div class="account_upload-file mattach mattach-form mattach-form-upload-version" style="margin-bottom: 0px;">
                <div class="mcomment-attached" style="display: none;">
                  <div class="mattach-preview-container mattach-preview-container-form">
                    <div class="mattach-previews mattach-previews-form collection">
                      <div class="mattach-template mattach-template-form collection-item item-template">
                        <div class="mattach-info" data-dz-thumbnail="">
                          <div class="mcommment-file">
                            <div class="determinate" style="width:0" data-dz-uploadprogress=""></div>
                             <div class="mcommment-file__name mcommment-file__name-form" data-dz-name=""></div>
                            <div class="mcommment-file__delete" href="#!" data-dz-remove=""><i
                                    class="icon icon--sicon-close"></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="fallback dropzone" id="version-upload-dropzone">
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- <div class="take-scene__action" style="text-align: right !important;">
          <div class="take-scene__action__container">
            {% include 'buttons/conponent_button.html' with className='btn--tertiary small btn-popup-close' value='キャンセル' attribute='data-dismiss="modal"' %}
            {% include 'buttons/conponent_button.html' with className='btn--primary small btn-popup-ok btn-confirm-take-scene' value='OK' %}
          </div>
        </div> -->
      </div>
    </div>
  </div>
</div>
<!-- end modal take -->

<!-- modal thumbnail -->
<div class="smodal smodal--large modal fade" id="modal-scene-thumbnail" role="dialog" style="margin: 0;">
  <div class="modal-dialog" role="document" style="background-color: #FFFFFF">
    <div class="modal-dialog__header">
      <div class="modal-dialog__header__container">
      </div>
    </div>
    <div class="modal-content">
      <div class="scene-thumnail">
        <div class="scene-thumnail-back scene-thumbnail-back-new">
          <svg data-dismiss="modal" xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
            <path d="M19.8802 8.94664C19.3602 8.42664 18.5202 8.42664 18.0002 8.94664L11.8802 15.0666C11.3602 15.5866 11.3602 16.4266 11.8802 16.9466L18.0002 23.0666C18.5202 23.5866 19.3602 23.5866 19.8802 23.0666C20.4002 22.5466 20.4002 21.7066 19.8802 21.1866L14.7069 16L19.8802 10.8266C20.3869 10.3066 20.3869 9.45331 19.8802 8.94664Z" fill="#A7A8A9"/>
          </svg>
        </div>
        <div class="scene-thumnail-name-container">
          <div class="scene-thumnail-name">サムネイル</div>
        </div>
        <div class="modal-body mscrollbar" style="padding-top: 0px; padding-bottom: 0px">
          <div class="version-container">
            {% with '動画から, 画像をアップロード'|create_option_component_select:'video_select, upload_image' as options %}
                {% include 'tabs/component_tab.html' with options=options type='segment' filename='top/_tab_video_thumb_select.html, top/_tab_image_upload_thumbnail.html'|create_option_component_select:'' %}
            {% endwith %}
            <div class="thumbnail-img-container-1" crossorigin="anonymous">
              <svg xmlns="http://www.w3.org/2000/svg" width="49" height="49" viewBox="0 0 49 49" fill="none">
                <path d="M48.5 43.6042V6.27083C48.5 3.3375 46.1 0.9375 43.1667 0.9375H5.83333C2.9 0.9375 0.5 3.3375 0.5 6.27083V43.6042C0.5 46.5375 2.9 48.9375 5.83333 48.9375H43.1667C46.1 48.9375 48.5 46.5375 48.5 43.6042ZM16.2333 30.2175L21.8333 36.9642L30.1 26.3242C30.6333 25.6308 31.7 25.6308 32.2333 26.3508L41.5933 38.8308C42.26 39.7108 41.62 40.9642 40.5267 40.9642H8.55333C7.43333 40.9642 6.82 39.6842 7.51333 38.8042L14.1533 30.2708C14.66 29.5775 15.6733 29.5508 16.2333 30.2175Z" fill="#A7A8A9"/>
              </svg>
              <img src="" alt="">
            </div>
            <div class="scene-thumnail-name" style="margin-bottom: 0;">バリエーション名</div>
            {% include 'input_box/component_input.html' with attribute='id="id_version_name" name="vesionName"' value='A' placeholder='' %}
            <div class="reupload-version-container">
              <div class="version-name">メディアファイル.mp4</div>
              {% include 'buttons/conponent_button.html' with icon_file_start='top/_button_reupload_icon.html' className='btn--secondary small btn-reupload' value='リプレイス' %}
            </div>
            <input type="file" accept=".png, .PNG, .jpg, .JPG, .jpeg, .JPEG" class="hide" id="upload-image-thumbnail">
            <input type="file" accept=".mp4, .MP4, .mov, .MOV, application/pdf, .mp3, .wav, .WAV" class="hide" id="reupload-file-version">
          </div>
        </div>
        <!-- <div class="take-scene__action" style="text-align: right !important;">
          <div class="take-scene__action__container">
            {% include 'buttons/conponent_button.html' with className='btn--tertiary small btn-popup-close' value='キャンセル' attribute='data-dismiss="modal"' %}
            {% include 'buttons/conponent_button.html' with className='btn--primary small btn-popup-ok btn-confirm-version-scene' value='OK' %}
          </div>
        </div> -->
      </div>
    </div>
  </div>
</div>
<!-- end modal thumbnail -->
