{% load static %}
{% load util %}
{% with file|get_list_user_download_refactor as user_dowloadeds %}
  <div class="tfile-infor tfile-type tfile-type--file {% if user_dowloadeds %}has-user-seen{% endif %}"
       data-file-id="{{ file.pk }}"
       data-type="{{ file.is_audio_file }}"
       data-link="{{ file.file.url }}"
       data-name="{{ file.real_name }}"
       data-toggle="modal"
       data-target="#modal-{{ file.is_audio_file }}-popup"
       data-message-id="{{ file.message.pk }}">
    <div class="tfile-file">
      <div class="s-file s-file--file
       {% if type_comment in 'messenger,messenger_owner' %}{% if file.message.user.role != user.role %}s-file--gray{% endif %}{% endif %}">
        <div class="scene-file--name">{{ file.real_name }}</div>
      </div>
      {% include 'top/_sview_user_refactor.html' with file=file type_comment=type_comment user_dowloadeds=user_dowloadeds %}
    </div>
    {% if file.is_audio_file in 'audio,video' and file.acr_status in '1,2,3' %}
      {% if file.acr_status == '3' %}
        <img class="acr-result-icon active" src="/static/images/icon_acr_active.svg" />
      {% else %}
        <img class="acr-result-icon deactive" src="/static/images/icon_acr_deactive.svg" />
      {% endif %}
    {% endif %}
    <a class="tfile-info btn-download-file" href="javascript:void(0)"><span class="material-symbols-rounded">download</span></a>
  </div>
{% endwith %}

