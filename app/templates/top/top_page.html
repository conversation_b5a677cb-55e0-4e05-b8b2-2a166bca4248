{% extends "base_nofooter.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static compress %}
{% load user_agents %}

{% block extrahead %}
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.css"/>
  {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/main.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/modal.css' %}"/>
  {% endcompress %}
  <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>

{% endblock %}

{% block content %}
  {% compress css %}
  <link rel="stylesheet" type="text/css" href="{% static 'css/project_list.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/top.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/top_admin.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/modal_manager.css' %}"/>

  <style>
    .btn-create-new-project {
      margin: 0 auto 20px;
      text-align: center;
      width: 40%;
      min-width: 300px;
      position: relative;
      padding-top: 40px;
      color: #53565a;
    }

    .btn-create-new-project:before {
      content: '+';
      height: 20px;
      width: 20px;
      border-radius: 50%;
      background: #52565a;
      color: #fff;
      top: 10px;
      position: absolute;
      left: 48%;
      font-weight: bold;
      font-size: 1.2em;
      line-height: 20px;
    }


    .btn-create-new-project:hover {
      color: #009ace;
    }

    .btn-create-new-project:hover:before {
      background: #009ace;
    }
  </style>
  {% endcompress %}
  <main>
    <div class="mcontainer">
      <div class="sprojects">
        <div class="sprojects-search">
          <div class="sprojects-search-form">
            <div class="sform-row sprojects-search-input">
              <div class="sform-group sform-group--required">
                <div class="sform-group__input-group sform-group__append-before">
                  <input class="sform-control sform-control--input sform-control--full" id="pl-search" type="search"
                         placeholder="Enter type text" required="required"/>
                  <i class="icon icon--sicon-close search-delete"></i>
                  <label class="sform-group__append" for="pl-search">
                    <i class="icon icon--sicon-search"></i>
                  </label>
                </div>
              </div>
            </div>
            <div class="sform-row">
              <div class="sprojects-search-tag">
                {% for view_product in view_products %}
                  <a href="{% if user.role == 'master_admin' %}{% url 'app:product_update'  view_product.pk %} {% else %}{% url 'app:top_project_detail' view_product.pk %}{% endif %}"
                     target="_blank">
                    <div class="tag tag--black" id="pl-tag-1">
                      <span class="tag-text">{{ view_product.name }}</span>
                    </div>
                  </a>
                {% endfor %}
              </div>
            </div>
          </div>
        </div>
        <div class="sprojects-list project-list" data-total-page="{{ total_page }}" data-done="{{ is_done }}">
          {% for project in projects %}
            {% include 'top/_product_item.html' with project=project user=user is_pc=request|is_pc show_staff=True %}
          {% endfor %}
        </div>
      </div>
    </div>

    {% include 'top/_modal_setting_setting.html' %}
  </main>

  <div class="modal fade share-modal" role="dialog" id="shareModal" style="z-index: 9999;">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h6 class="modal-title"></h6>
          <button class="close" data-dismiss="modal"
                  type="button">閉じる
          </button>
        </div>
        <div class="modal-body">
          <div class="video-time-slider-item">
            <div class="video-time-slider-content">
              <div class="video-time-slider-start">00:00
              </div>
              <div class="video-time-slider-bar"></div>
              <div class="video-time-slider-end hide">00:00
              </div>
            </div>
            <div class="video-time-slider-label">
              <div class="video-time-slider-label-start">
                開始位置を指定
              </div>
              <div class="video-time-slider-label-end hide">
                終了位置も指定
              </div>
            </div>
          </div>
          <div class="modal-share-link">
            <div class="video-item-share-input">
              <input class="video-share-link"
                     id="video-share-link" type="text"
                     name="video-share-link"
                     placeholder=""
                     value="">
            </div>
            <a class="button button--text button--text-primary video-item-share-btn"
               href="javascript:;" role="button">URLをコピー</a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" role="dialog" id="processingSceneModal" style="overflow: hidden;">
    <div class="modal-dialog modal-lg" role="document" style="width: 100%">
      <div class="modal-content" style="height: 100vh">
        <div class="modal-body container" style="max-height: 100vh;">
          <div class="project-video-item show-comment">
            <div class="video-item-wrap">
              <div class="video-item-list">
                <div class="video-item-component active"
                     data-scene-id="">
                  <div class="video-item-component-content">
                    <div class="video-item-component-content-video">
                      <div class="video-item-chapter"></div>
                      <div class="video-item-variation"></div>
                      <div class="video-item-slug"
                           share-url=""></div>
                      <a class="video-button video-item-bookmark" href="javascript:;"></a>
                      <a class="video-button video-item-share" href="javascript:void(0)"
                         data-toggle="modal"
                         data-target="#shareModal"></a>
                      <div class="video-button video-item-button-top">とりあえずOK</div>
                      <div class="video-button video-item-button-bottom">チェックバック</div>
                      <div class="video-button video-item-button-left">もういちど</div>
                      <video width="100%" height="auto" poster="" controls preload="none">
                        <source src="" type="video/mp4">
                      </video>
                    </div>
                  </div>
                </div>
              </div>
              <div class="video-item-control">
                <div class="video-item-bullets-wrap">
                  <div class="video-item-bullets">
                    <div class="video-item-bullet-prev disable"
                         data-current_index="0"></div>
                    <div class="video-item-bullet-list">
                      <div class="video-item-bullet active"
                           data-index=""></div>
                    </div>
                    <div class="video-item-bullet-next"
                         data-current_index="0"></div>
                  </div>
                </div>
                <div class="video-item-thumbnails">
                  <div class="video-item-thumbnail-list">
                    <div class="video-item-thumbnail active"
                         data-index="" data-id="">
                      <video class="active" width="100%" height="auto" preload="none"
                             poster="">
                      </video>
                    </div>
                  </div>
                </div>
                <div class="video-item-collapse">
                  <div class="video-item-collapse-button"></div>
                </div>
              </div>
              <br>
            </div>
            <div class="video-item-comment" data-scene-id="">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="project-member-setting-modal modal fade" id="project-member-setting"
       role="dialog" style="display: none;">
    <!-- ajax will append data here -->
  </div>

  {% if not is_done and have_done or is_done and have_processing %}
    <div class="project-progress-action" id="id-top-done-banner" style="z-index: 12">
      <div class="project-progress-action-content">
        <div class="project-progress-action-notice">
          {% if is_done %}
            <div class="project-progress-action-message">←</div>
          {% else %}
            <div class="project-progress-action-message">検収完了したものはこちら。</div>
            <div class="project-progress-action-message">納品ファイルをダウンロードできます。</div>
          {% endif %}
        </div>
        <div class="project-progress-action-btn">
          <a class="button button--gradient button--gradient-primary button--round" href="
                        {% if is_done %}{% url 'app:top_page' %}?force=true{% else %}{% url 'app:top_page' %}?is_done=1&force=true{% endif %}"
             role="button">{% if is_done %}進行中プロジェクトに戻る{% else %}アーカイブを観る{% endif %}</a>
        </div>
      </div>
    </div>
  {% endif %}
{% compress js inline %}
  <script>
      let default_thumb = '{% static 'images/messenger-thumb.png' %}';
      let user = {{ user.id }};
  </script>
  <script src="{% static 'js/isInViewport.min.js' %}"></script>
  <script src="{% static 'js/jquery.scopeLinkTags.js' %}"></script>
  <script src="{% static 'js/top_page_admin.js' %}"></script>
  <script src="{% static 'js/top_page_member.js' %}"></script>
{% endcompress %}
{% endblock %}
{% block extra_script %}
  <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.2.5/moment-timezone-with-data.min.js" integrity="sha512-OvYMp/zgYHU6ojgDxmtCLsgHBtSrX34Cx/01Tv2bFfbJnlVMbIv5TVVTQ+0wU4GHVDIlHAMMsjFBpTzDnSqIiA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.8.0/Chart.bundle.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.js"></script>
  <script src="{% static 'js/unpkg_wavesurfer.js' %}"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"
          integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q"
          crossorigin="anonymous"></script>
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    {% compress js inline %}
  <script src="{% static 'js/main.js' %}"></script>
    {% endcompress %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui-touch-punch/0.2.3/jquery.ui.touch-punch.min.js"></script>
    {% compress js inline %}
  <script src="{% static 'js/sort_project.js' %}"></script>
  <script src="{% static 'js/action_banner.js' %}"></script>
    {% endcompress %}
{% endblock %}
