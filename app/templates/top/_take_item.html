{% load static %}
{% load util %}
{% load i18n %}

{% if scene.movie %}
<div class="scene-take-container" data-scene-id="{{scene.pk}}">
    <div class="scene-take-video">
        <img src="{{ scene.get_first_variation_take|get_thumbnail }}" alt="">
    </div>
    <div class="scene-take-namne-container">
        {% if scene.movie %}
            <div class="scene-take-name-left">
                <div class="scene-take-name">
                    <div class="scene-take-name-content">{% if index == 1 %}ファーストテイク {% else %}テイク{{index}}{% endif %}</div>
                    <div class="scene-take-time-content">{{ scene.take_uploaded|get_weekday_new }}</div>
                </div>
                <div class="scene-take-list-variant">
                        <div class="variant-name-container" data-scene-id="{{scene.pk}}">
                            <div class="scene-variant-name">{{ scene.real_name }}</div>
                            <div class="scene-variant-file">{{ scene.get_file_movie_name }}</div>
                        </div>
                        {% for other in scene.other_versions.all %}
                        <div class="variant-name-container" data-scene-id="{{other.pk}}">
                            <div class="scene-variant-name">{{ other.real_name }}</div>
                            <div class="scene-variant-file">{{ other.get_file_movie_name }}</div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                <div class="scene-take-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M9.2925 6.71002C8.9025 7.10002 8.9025 7.73002 9.2925 8.12002L13.1725 12L9.2925 15.88C8.9025 16.27 8.9025 16.9 9.2925 17.29C9.6825 17.68 10.3125 17.68 10.7025 17.29L15.2925 12.7C15.6825 12.31 15.6825 11.68 15.2925 11.29L10.7025 6.70002C10.3225 6.32002 9.6825 6.32002 9.2925 6.71002Z" fill="#A7A8A9"/>
                    </svg>
                </div>
            </div>
        {% endif %}
</div>
{% endif %}

