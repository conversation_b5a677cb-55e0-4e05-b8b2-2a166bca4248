{% load util %}
{% load static %}
{% load i18n %}
{% with notification=user|check_notification_product:project project_user=user|get_product_user:project owners=project|get_owners_project %}
    {% if type_page == 'top_page' and page_detail and page_detail == 'detail' or type_page == 'messenger' and page_detail and page_detail == 'messenger_detail' %}
        <div class="block-navigation-bar {% if user.role == 'master_client' and not project.check_has_scene_in_project %} hide{% endif %}">
            <ul class="navigation-bar {% if not project_user.position in 'admin, producer' and not user.role == 'master_client' %}navigation-bar-custom{% endif %}">
                {% if project_user.position in 'admin, producer' or user.role == 'master_client' %}
                    {% with project|count_new_update_scene_title:user as count_new_update_scene_title %}
                        <a href="{% url 'app:top_project_detail' pk=project.pk %}"
                           class="link-nav-item pbanner-tab pbanner-tab-all" data-show="progress"
                           data-url="{% url 'app:top_project_detail' pk=project.pk %}">
                            <li class="nav-bar-item">
                                <div class="icon-with-badge"><span class="material-symbols-rounded">home</span>
                                    <span class="number-notification"
                                          value="{{ count_new_update_scene_title }}">{% if count_new_update_scene_title < 100 %}
                                        {{ count_new_update_scene_title }}{% else %}99+{% endif %}</span></div>
                                <span class="label-nav-bar-item">ホーム</span>
                            </li>
                        </a>
                    {% endwith %}
                    {% with project|count_undownload_product_comment:user as undownloaded_comment %}
                        <a href="{% url 'app:top_project_detail' pk=project.pk %}?tab=product-comment"
                           class="link-nav-item pbanner-tab pbanner-tab--exchange file-upload-owner"
                           data-show="product-comment"
                           data-url="{% url 'app:top_project_detail' pk=project.pk %}?tab=product-comment">
                            <li class="nav-bar-item">
                                <div class="icon-with-badge">
                                    <span class="material-symbols-rounded">forum</span>
                                    <span class="number-notification"
                                          value="{{ undownloaded_comment }}">{% if undownloaded_comment < 100 %}
                                        {{ undownloaded_comment }}{% else %}99+{% endif %}</span>
                                </div>
                                <span class="label-nav-bar-item">トークルーム</span>
                            </li>
                        </a>
                    {% endwith %}
                    {% if project_user.position != 'client' %}
                        <a href="{% url 'app:top_project_detail' pk=project.pk %}?tab=messenger"
                           class="link-nav-item pbanner-tab pbanner-tab-message" data-show="messenger">
                            <div></div>
                            <li class="nav-bar-item">
                                <div class="icon-with-badge">
                                    <span class="material-symbols-rounded">mail</span>
                                    {% with project|count_unread_offer_message_count:user as unread_message_count %}
                                        {% if unread_message_count %}
                                            <span class="number-notification unread-offer-comment"
                                                    value="{{ unread_message_count }}">{% if unread_message_count < 100 %}
                                                {{ unread_message_count }}{% else %}99+{% endif %}</span>
                                        {% endif %}
                                    {% endwith %}
                                </div>
                                <span class="label-nav-bar-item">個別DM</span>
                            </li>
                        </a>
                    {% endif %}
                {% elif user.role == 'master_admin' %}
                    <a href="javascript:void(0);"
                       data-url="
            {% if user.role == 'admin' %}{% url 'app:messenger_waiting' %}?project_id=

                           {{ project.pk }}{% else %}{% url 'app:direct_inbox' %}?offer={{ project.offer_product.first.pk }}{% endif %}"
                       class="link-nav-item pbanner-tab pbanner-tab-message active" data-show="messenger">
                        <li class="nav-bar-item">
                            <div class="icon-with-badge">
                                <span class="material-symbols-rounded">mail</span>
                                {% with project|count_unread_offer_message_count:user as unread_message_count %}
                                        {% if unread_message_count %}
                                            <span class="number-notification unread-offer-comment"
                                                    value="{{ unread_message_count }}">{% if unread_message_count < 100 %}
                                                {{ unread_message_count }}{% else %}99+{% endif %}</span>
                                        {% endif %}
                                    {% endwith %}
                            </div>
                            <span class="label-nav-bar-item">個別DM</span>
                        </li>
                    </a>
                {% else %}
                    <a href="javascript:void(0);"
                       data-url="
            {% if user.role == 'admin' %}{% url 'app:messenger_waiting' %}?project_id=

                           {{ project.pk }}{% else %}{% url 'app:direct_inbox' %}?offer={{ project.offer_product.first.pk }}{% endif %}"
                       class="link-nav-item pbanner-tab pbanner-tab-message" data-show="messenger">
                        <li class="nav-bar-item">
                            <div class="icon-with-badge">
                                <span class="material-symbols-rounded">mail</span>
                                {% with project|count_unread_offer_message_count:user as unread_message_count %}
                                        {% if unread_message_count %}
                                            <span class="number-notification unread-offer-comment"
                                                    value="{{ unread_message_count }}">{% if unread_message_count < 100 %}
                                                {{ unread_message_count }}{% else %}99+{% endif %}</span>
                                        {% endif %}
                                    {% endwith %}
                            </div>
                            <span class="label-nav-bar-item">個別DM</span>
                        </li>
                    </a>
                {% endif %}
            </ul>
        </div>
    {% endif %}

{% endwith %}
