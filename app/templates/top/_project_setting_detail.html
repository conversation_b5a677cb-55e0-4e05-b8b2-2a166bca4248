{% load util %}
{% load static %}
{% load i18n %}

{% with is_editable_director=user|is_editable_director:project_user %}
  <div class="pbanner__detail-expand-top">
    <div class="detail-expand-left u-row u-gap16">
        {# code name #}
      <div class="detail-code-name {% if not is_editable_director %}cannot-check{% endif %}">
        <div class="detail-code-name__content">
          {% if project.code_name %}
            {{ project.code_name }}{% else %}{% trans "CODE NAME" %}{% endif %}
        </div>
      </div>
      <div class="detail-time-range-container cannot-check">
        <span class="detail-time-range {% if not is_editable_director %}cannot-check{% endif %}">{{ project.start_time|get_range_date:project.end_time }}</span>
      </div>
    </div>
    <div class="detail-expand-right u-row">
      <div>
        {% comment %} <div class="sproject-wishlist">
          <i class="icon icon--sicon-heart-o cannot-check" style="font-size: 18px;"></i>
        </div>
        <div class="sproject-time sproject-scene" style="display: flex;">
          <span class="sproject-time__current-heart-rate">{{ project.get_current_heart_rate }}%</span>
          {% with user|is_editable_description:project_user as can_edit_description %}
            <span class="sproject-time__done-count {% if not can_edit_description %}cannot-check{% endif %}"
                  data-max-scene="{{ project.max_scene }}">({{ project.current_heart|floatformat:"0" }}/{{ project.max_scene }})</span>
          {% endwith %}
        </div> {% endcomment %}

        <div class="sproject-banner-rating stars-detail-expand">
          {% with project.rating as rating %}
            <div class="stars" data-rating="{{ rating }}">{{ rating|generate_star }}</div>
            <span class="rating_value">{{ rating }}</span>
            <span class="number_vote"> ({{ project|project_number_vote }})</span>
          {% endwith %}
        </div>

        {% comment %} <div class="sproject-date-time sproject-project-time">
          {% include 'top/_infor_project.html' with project=project %}
        </div> {% endcomment %}

        {% comment %} {% if type_page != 'messenger_projects' %}
          {% if user.role == 'admin' or user.role == 'master_client' %}
            <div class="batch-number-project">
              {% with project|count_batch_number_project:user as batch_number %}
                {% if batch_number > 0 %}
                  <div class="slabel slabel--blue">
                    {% if batch_number < 100 %}{{ batch_number }}{% else %}99+{% endif %}</div>
                {% endif %}
              {% endwith %}
            </div>
          {% endif %}
        {% endif %} {% endcomment %}
      </div>
    </div>
  </div>


  <!-- Check description to display test here -->
  {% with user|is_editable_description:project_user as can_edit_description %}
    <div class="pbanner__detail-description">
      <div class="description-content {% if not project.description %}none-description {% endif %}{% if not can_edit_description %}cannot-check{% endif %}">
        <div class="description-content__content">
          {% if project.description %}{{ project.description }} {% else %}
            {% trans "Please enter the detailed information of the project." %}{% endif %}
        </div>
      </div>
    </div>
  {% endwith %}

  <div class="pbanner__detail-expand-bottom">
    <!-- Owner name here -->
    <div class="pbanner__detail-owner-name {% if not is_editable_director %}cannot-check{% endif %}">
      {% if project.client_name %}{{ project.client_name }}{% else %}エンドクライアント{% endif %}</div>
  </div>
  <div class="pbanner__detail-show-credit">
    <div class="credit-button-action">
            <span type="button"
                  class="btn {% if is_editable_director or project.sections.exists or project.items_staff_credit.exists %}btn--secondary{% else %}btn--primary disabled{% endif %} button-show-staff">クレジットを観る</span>
    </div>

  </div>
  <div class="pbanner__detail-expand-action">
          <span class="pbanner__icon-collapse-container">
            <i class="icon icon--sicon-down icon-toggle"></i>
          </span>
  </div>
{% endwith %}
