{% load util %}
{% load static %}
{% load i18n %}
{% with notification=user|check_notification_product:project project_user=user|get_product_user:project owners=project|get_owners_project %}
{% if type_page == 'top_page' and page_detail and page_detail == 'detail' or type_page == 'messenger' and page_detail and page_detail == 'messenger_detail' %}
<div
    class="block-navigation-bar {% if user.role == 'master_client' and not project.check_has_scene_in_project or project_user.position not in 'admin, producer' and user.role != 'master_client'%}hide{% endif %}">
    <ul
        class="navigation-bar {% if not project_user.position in 'admin, producer' and not user.role == 'master_client' %}navigation-bar-custom{% endif %}">
        {% if project_user.position in 'admin, producer' or user.role == 'master_client' %}
        {% with project|count_new_update_scene_title:user as count_new_update_scene_title %}
        <a href="{% url 'app:top_project_detail' pk=project.pk %}" class="link-nav-item pbanner-tab"
            data-show="progress" data-url="{% url 'app:top_project_detail' pk=project.pk %}">
            <li class="nav-bar-item">
                <div class="material-symbols-rounded icon-with-badge u-relative">home
                    {% if count_new_update_scene_title %}
                    <div class="c-budge u-row-center u-absolute" style="top: -8px; right: -8px;" value="{{ count_new_update_scene_title }}">
                        {% if count_new_update_scene_title < 100 %}
                        {{ count_new_update_scene_title }}{% else %}99+{% endif %}</div>
                        {% endif %}
                    </div>
                <div class="label8">ホーム</div>
            </li>
        </a>
        {% endwith %}
        {% with project|count_undownload_product_comment:user as undownloaded_comment %}
        <a href="{% url 'app:top_project_detail' pk=project.pk %}?tab=product-comment"
            class="link-nav-item pbanner-tab pbanner-tab--exchange file-upload-owner" data-show="product-comment"
            data-url="{% url 'app:top_project_detail' pk=project.pk %}?tab=product-comment">
            <li class="nav-bar-item">
                <div class="material-symbols-rounded icon-with-badge u-relative">forum
                    {% if undownloaded_comment %}
                    <div class="c-budge u-row-center u-absolute" style="top: -8px; right: -8px;" value="{{ undownloaded_comment }}">
                        {% if undownloaded_comment < 100 %}
                        {{ undownloaded_comment }}{% else %}99+{% endif %}</div>
                    {% endif %}
                    </div>
                <span class="label8">トークルーム</span>
            </li>
        </a>
        {% endwith %}
        {% if project_user.position != 'client' %}
        <a href="{% url 'app:top_project_detail' pk=project.pk %}?tab=messenger"
            class="link-nav-item pbanner-tab pbanner-tab-message" data-show="messenger">
            <div></div>
            <li class="nav-bar-item">
                <div class="icon-with-badge material-symbols-rounded u-relative">mail
                    {% with project|count_unread_message:user as unread_message_count %}
                    {% if unread_message_count %}
                    <div class="c-budge u-row-center u-absolute unread-offer-comment" style="top: -8px; right: -8px;"
                        value="{{ unread_message_count }}">{% if unread_message_count < 100 %}
                        {{ unread_message_count }}{% else %}99+{% endif %}</div>
                    {% endif %}
                    {% endwith %}
                </div>
                <span class="label8">DM</span>
            </li>
        </a>
        {% endif %}
        {% elif user.role == 'master_admin' %}
        <a href="javascript:void(0);"
            data-url="
            {% if user.role == 'admin' %}{% url 'app:messenger_waiting' %}?project_id=
                           {{ project.pk }}{% else %}{% url 'app:direct_inbox' %}?offer={{ project.offer_product.first.pk }}{% endif %}"
            class="link-nav-item pbanner-tab pbanner-tab-message active" data-show="messenger">
            <li class="nav-bar-item">
                <div class="icon-with-badge material-symbols-rounded u-relative">mail
                    {% with project|count_unread_message:user as unread_message_count %}
                    {% if unread_message_count %}
                    <div class="c-budge u-row-center u-absolute unread-offer-comment" style="top: -8px; right: -8px;"
                        value="{{ unread_message_count }}">{% if unread_message_count < 100 %}
                        {{ unread_message_count }}{% else %}99+{% endif %}</div>
                    {% endif %}
                    {% endwith %}
                </div>
                <span class="label8">DM</span>
            </li>
        </a>
        {% else %}
        <a href="javascript:void(0);"
            data-url="
            {% if user.role == 'admin' %}{% url 'app:messenger_waiting' %}?project_id=

                           {{ project.pk }}{% else %}{% url 'app:direct_inbox' %}?offer={{ project.offer_product.first.pk }}{% endif %}"
            class="link-nav-item pbanner-tab pbanner-tab-message" data-show="messenger">
            <li class="nav-bar-item">
                <div class="icon-with-badge material-symbols-rounded u-relative">mail
                    {% with project|count_unread_message:user as unread_message_count %}
                    {% if unread_message_count %}
                    <div class="c-budge u-row-center u-absolute unread-offer-comment" style="top: -8px; right: -8px;"
                        value="{{ unread_message_count }}">{% if unread_message_count < 100 %}
                        {{ unread_message_count }}{% else %}99+{% endif %}</div>
                    {% endif %}
                    {% endwith %}
                </div>
                <span class="label8">DM</span>
            </li>
        </a>
        {% endif %}
    </ul>
</div>
{% endif %}

{% endwith %}