{% extends "base_nofooter.html" %}
{% load bootstrap3 %}
{% load util %}
{% load widget_tweaks %}
{% load static %}
{% block extrahead %}
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
{% endblock extrahead %}
{% block content %}
    <main>
        <div class="container">
            <h2>ACRcloud check</h2>
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                {{ form }}
                <input class="btn btn-primary" type="submit" value="Submit">
            </form>
            {% for result_dict in result_rec %}
                <label for="">Recognize Result: {{ result_dict.name }}</label>
                <textarea class="form-control">{{ result_dict.result }}</textarea>
            {% endfor %}
        </div>
    </main>
    <script>
        $(document).ready(function () {
                window.URL = window.URL || window.webkitURL;
                var file_element = $('form #id_file');
                var myVideos = [];

                file_element.on('change', function (e) {
                    var files = this.files;
                    if (files.length > 0) {
                        return
                    }
                    myVideos.push(files[0]);
                    var video = document.createElement('video');
                    video.preload = 'metadata';

                    video.onloadedmetadata = function () {
                        window.URL.revokeObjectURL(video.src);
                        let duration = video.duration;
                        $('form #id_duration').val(Math.floor(duration))
                    }
                    video.src = URL.createObjectURL(files[0]);
                });
            }
        );
    </script>
{% endblock content %}
