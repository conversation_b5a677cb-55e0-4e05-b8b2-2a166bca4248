{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 compress %}

{% block title %}{% endblock title %}

{% block extrahead %}
    {% compress css %}
    <style>
        #video-demo-container {
            width: 400px;
            margin: 40px;
        }

        #main-video {
            display: none;
            max-width: 400px;
        }

        #video-canvas, #video-canvas-resize {
            max-width: 400px;
        }

        .canvas, #video-canvas-resize {
            display: none;
        }

        .upload-content {
            margin-top: 40px;
        }

        #submit {
            margin-top: 36px;
            width: 100px;
        }

        .scene_upload {
            padding-top: 50px;
        }

        .button {
            padding-left: 0px;
        }
    </style>
    {% endcompress %}
{% endblock %}

{% block content %}

    <main class="container">
        <div class="m10 row scene_upload">
            <div class="col-xs-12 upload-content">
                <form method="post" id="myAwesomeForm" action="{% url 'app:product_order_upload' product_id %}"
                      enctype="multipart/form-data">
                    <div class="col-xs-12">
                        {% csrf_token %}
                    </div>
                    <div id="video-demo-container col-xs-12">
                        {% if format_upload == 'pdf' %}
                            <div class="form-group"><label class="control-label" for="file">ファイル</label><input
                                    type="file" name="file"
                                    accept=".pdf"
                                    oninvalid="this.setCustomValidity('このフィールドは必須項目です。')"
                                    onchange="this.setCustomValidity('')"
                                    class="" title="" required id="id_file"></div>
                            <div class="col-xs-5 col-sm-2 button">
                                {% if not upload_carousel %}
                                    <div class="form-group">
                                    <label class="control-label" for="id_type_file_pdf">ファイルタイプ</label>
                                    <select name="type_file_pdf" class="form-control col-xs-2" title="" id="id_type_file_pdf">
                                    <option value="0">普通</option>

                                    <option value="1">見積書</option>
                                    <option value="2">請求書</option>
                                {% else %}
                                    <input type="hidden" name="type_file_pdf" id="carousel_file" value="{{ upload_carousel }}">
                                {% endif %}


                                </select></div>
                                <div class="form-group button price hidden"><label class="control-label" for="id_first_name">価格</label><input
                                     type="number" name="price_bill_pdf" class="form-control money" min=1
                                     onfocus="this.setCustomValidity('')"
                                     oninput="this.setCustomValidity('')" pattern="\d*"
                                     placeholder="円" title="" id="id_first_name"></div>
                            </div>

                            <input type="hidden" name="type_file_upload" value="0" id="id_type_file_upload">
                        {% elif  format_upload == 'video' %}
                            <div class="form-group"><label class="control-label" for="id_movie">動画ファイル</label><input
                                    type="file" name="file" accept=".mp4, .x-m4v, .avi, .webm, video/*"
                                    oninvalid="this.setCustomValidity('このフィールドは必須項目です。')"
                                    oninput="setCustomValidity('')" class="" title="" required id="id_movie"></div>
                            <div class="col-xs-6 button">
                                <video id="main-video" controls>
                                    <source type="video/mp4">
                                </video>
                            </div>
                            <input type="hidden" name="type_file_upload" value="1" id="id_type_file_upload">
                        {% endif %}
                        <input type="hidden" name="product" value="{{ product_id }}" id="id_product_id">
                        <input type="hidden" name="version" value="{{ order_upload_id }}" id="id_version">
                        <input type="hidden" name="owner_id" value="{{ owner_id }}" id="id_owner_id">
                        <input type="hidden" name="tag" value="1" id="id_tag">
                    </div>
                    <div class="col-xs-12 button">
                        <div class="form-group">
                            {% buttons %}
                                <input type="submit" value="登録" id="submit" class="btn btn-primary btn_upload"/>
                            {% endbuttons %}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </main>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.js"></script>
    {% compress js inline %}
    <script type="text/javascript">
        $(document).ready(function () {
            $('#id_type_file_pdf').change(function () {
                if ($(this).val() === '2') {
                    $('.price').removeClass("hidden");
                    $(".money").prop('required',true);

                } else {
                    $('.price').addClass("hidden");
                    $(".money").prop('required',false);
                }
            });

            let movie_dom = $('#id_movie');
            if(movie_dom.length) {
                document.querySelector("#id_movie").addEventListener('change', function () {
                    var _VIDEO = document.querySelector("#main-video"),
                        id_movie = '#id_movie';
                    // Object Url as the video source
                    document.querySelector("#main-video source").setAttribute('src', URL.createObjectURL(document.querySelector(id_movie).files[0]));

                    // Load the video and show it
                    _VIDEO.load();
                    _VIDEO.style.display = 'inline';

                    // Load metadata of the video to get video duration and dimensions
                    _VIDEO.addEventListener('loadedmetadata', function () {
                        var video_duration = _VIDEO.duration,
                            duration_options_html = '';

                        // Set options in dropdown at 4 second interval
                        for (var i = 0; i < Math.floor(video_duration); i = i + 4) {
                            duration_options_html += '<option value="' + i + '">' + i + '</option>';
                        }

                        $('#id_video_width').val(_VIDEO.videoWidth);
                        $('#id_video_height').val(_VIDEO.videoHeight);
                    });
                });

                movie_dom.on('change', function () {
                    if (this.files.length && !this.files[0].name.match(/\.(mp4|x-m4v|avi|webm|pdf|mp3|wav)$/)) {
                        alert('ファイルのフォーマットが正しくありません。MP4, webmファイルをアップロードしてください。');
                        $('#id_movie').val('').clone(true);
                    } else if (!this.files.length) {
                        return false;
                    }
                });
            } else {
                $('#id_file').on('change', function () {
                    if (this.files.length && !this.files[0].name.match(/\.(PDF|pdf)$/)) {
                        alert('PDFファイル形式でアップロードしてください。');
                        $('#id_file').val('').clone(true);
                    }
                });
            }
        });

        $('#id_first_name').keyup(function() {
            if(!this.value) {
                this.setCustomValidity('このフィールドは必須項目です。')
            } else if(this.value < 1) {
                this.setCustomValidity('正しい価格を入力してください。')
            }
        });

        $('#submit').unbind('click').bind('click', function () {
            let movie_dom = $('#id_movie');
            let file_dom = $('#id_file');
            if(movie_dom.length) {
                if(movie_dom[0].file.length) {
                    return true;
                } else {
                    movie_dom[0].setCustomValidity('このフィールドは必須項目です。');
                }
            } else if(file_dom.length) {
                if (file_dom[0].files.length && !file_dom[0].files[0].name.match(/\.(PDF|pdf)$/)) {
                    alert('PDFファイル形式でアップロードしてください。');
                    $('#id_file').val('').clone(true);
                } else if(!file_dom[0].files.length) {
                    file_dom[0].setCustomValidity('このフィールドは必須項目です。')
                } else {
                    return true;
                }
            }

            let input_price = $('#id_first_name');
            if(input_price.length && input_price.prop('required')) {
                if(!input_price[0].value) {
                    input_price[0].setCustomValidity('このフィールドは必須項目です。')
                } else if(input_price[0].value < 1) {
                    input_price[0].setCustomValidity('正しい価格を入力してください。')
                } else {
                    return true;
                }
            }
        });
    </script>
    {% endcompress %}
{% endblock content %}
