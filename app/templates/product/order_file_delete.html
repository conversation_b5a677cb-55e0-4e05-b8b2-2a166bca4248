{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}

{% block extrahead %}
    <style>
        textarea.form-control {
            height: 60px;
        }

        .delete {
            margin-top: 40px;
        }

        .file_pdf {
            width: 400px;
            height: 200px;
        }
    </style>
{% endblock %}

{% block content %}
    <main class="container delete">
        <div class="m10 row">
            <div class="col-xs-6">
                <form action="" method="post">{% csrf_token %}
                    <p>削除してよろしいですか?</p>
                    {% buttons %}
                        <input type="submit" value="削除する" class="btn btn-primary">
                    {% endbuttons %}
                </form>
                {% if object.type_file_upload == 1 %}
                    {% render_video_with_hls object id="movie"|add:object.pk class="video-js" controls="controls" preload="metadata" width="400" height="200" poster="" data-setup="{}" as video_html %}
                      {% if video_html %}
                        {{ video_html }}
                      {% else %}
                        <video id="movie{{ object.pk }}" class="video-js" controls preload="metadata" width="400" height="200"
                               poster="" data-setup="{}">
                            <source src="{{ object.file.url }}" type='video/mp4'>
                            <p class="vjs-no-js">
                                To view this video please enable JavaScript, and consider upgrading to a web browser that
                                <a href="http://videojs.com/html5-video-support/" target="_blank">supports HTML5 video</a>
                            </p>
                        </video>
                      {% endif %}
                    {% endwith %}
                {% elif object.type_file_upload == 0 %}
                    <embed class="file_pdf" src="{{ object.file.url }}"
                           type="application/pdf"/>
                {%endif%}
            </div>
        </div>
    </main>
{% endblock content %}
