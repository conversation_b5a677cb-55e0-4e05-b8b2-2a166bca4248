{% load static %}
{% load util %}
{% load i18n %}


{% if item_artist and not project_artist %}
  <div class="modal-staff-credit-artist-header-item item-edit-artist hide"

       data-title="{{ item_artist.position|default_if_none:"" }}"
       data-title-en="{{ item_artist.title|default_if_none:"" }}"
       data-artist-name="{{ item_artist.artist_name|default_if_none:"" }}"
       data-artist-name-en="{{ item_artist.artist_name_en|default_if_none:"" }}"
       data-link-creator="{{ item_artist.link_profile }}"
       data-item-artist-id="{{ item_artist.pk }}">

  </div>
{% else %}
  {% with artist=project_user.user %}

    <div class="modal-staff-credit-artist-header-item {% if item_artist %}item-edit-artist{% endif %}"
         {% if not item_artist %}data-title="{{ artist.position|default_if_none:"" }}"
         data-title-en="{{ artist.type|default_if_none:"" }}" data-artist-name="{{ artist|get_stage_name_in_modal }}"
         data-artist-name-en="{{ artist.stage_name_en|default_if_none:"" }}"{% else %}
         data-title="{{ item_artist.position|default_if_none:"" }}"
         data-title-en="{{ item_artist.title|default_if_none:"" }}"
         data-artist-name="{{ item_artist.artist_name|default_if_none:"" }}"
         data-artist-name-en="{{ item_artist.artist_name_en|default_if_none:"" }}"
         data-link-creator="{{ item_artist.link_profile }}"
         data-item-artist-id="{{ item_artist.pk }}"
         {% endif %} data-artist="{{ artist.pk }}">

      <div class="modal-staff-credit-artist-header-item--avatar"
           style="background-image: url({{ artist|get_avatar:'medium' }})"></div>

      <div class="modal-staff-credit-artist-header-item--name bodytext--13">{{ artist.get_display_name }}</div>
      <div class="modal-staff-credit-artist-header-item--title caption--11">{{ artist.position|default_if_none:"" }}</div>
    </div>

  {% endwith %}
{% endif %}
