import logging

from django.http import JsonResponse
from django.shortcuts import redirect
from django.utils.translation import gettext as _
from django.template.loader import render_to_string

from accounts.models import AuthUser
from app.app_services.offer_project_services import get_information_offer_creator_service, delete_offer_creator_service, \
    delete_project_service, update_info_offer_creator_service
from app.models import OfferCreator, OfferProduct, Product


def get_information_offer_creator_view(request):
    offer_id = request.GET.get('offer_id', None)
    context = {}
    if offer_id:
        try:
            master_admins = AuthUser.get_master_admins()
            offer = OfferCreator.objects.get(pk=offer_id)
            if request.user not in master_admins:
                return JsonResponse(context, status=500)
            if not offer.check_can_delete_offer():
                return JsonResponse(context, status=500)
            context = get_information_offer_creator_service(offer, request.user)
            return JsonResponse(context, status=200)
        except:
            pass
    return JsonResponse(context, status=500)


def update_info_offer_creator_view(request):
    offer_id = request.POST.get('offer_id', None)
    context = {}
    if offer_id:
        try:
            master_admins = AuthUser.get_master_admins()
            offer = OfferCreator.objects.get(pk=offer_id)
            if request.user not in master_admins:
                return JsonResponse(context, status=500)
            if not offer.check_can_delete_offer():
                return JsonResponse(context, status=500)
            html = update_info_offer_creator_service(request, offer)
            context.update({'project_id': str(offer.project.pk), 'html': html})
            return JsonResponse(context, status=200)
        except:
            pass
    return JsonResponse(context, status=500)


def delete_offer_creator_view(request):
    context = {}
    user = request.user
    offer_id = request.POST.get('offer_id')
    if not offer_id:
        return JsonResponse({}, status=500)
    offer = OfferCreator.objects.filter(pk=offer_id).first()
    if not offer or offer and not offer.check_can_delete_offer():
        return JsonResponse({}, status=500)
    master_admins = AuthUser.get_master_admins()
    if user not in master_admins:
        return JsonResponse({}, status=500)
    html = delete_offer_creator_service(request, offer)
    context.update({'project_id': str(offer.project.pk), 'html': html})
    return JsonResponse(context, status=200)


def delete_project_view(request):
    user = request.user
    project_id = request.POST.get('project_id')
    if not project_id:
        return JsonResponse({}, status=500)
    project = Product.objects.filter(pk=project_id).first()
    if not project:
        return JsonResponse({}, status=500)
    if not project.check_can_delete_project():
        return JsonResponse({}, status=500)
    master_admins = AuthUser.get_master_admins()
    if user not in master_admins:
        return JsonResponse({}, status=500)
    deleted = delete_project_service(project)
    if deleted:
        return JsonResponse({}, status=200)
    return JsonResponse({}, status=500)

