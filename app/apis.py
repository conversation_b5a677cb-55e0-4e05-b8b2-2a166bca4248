# 01EG89H6SS2141VNGDDEHBMV4Q
# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import ast
from django.forms.models import model_to_dict
# built-in python
import base64
import datetime
import logging
from multiprocessing import context
from unicodedata import name
from django.utils.translation import gettext as _
from django.utils.crypto import get_random_string

import environ
import io
import json
import jwt
import os
import re
import sys
import time
import urllib
import uuid
from base64 import urlsafe_b64decode, urlsafe_b64encode
from urllib.parse import quote
from random import shuffle, random, sample

import stripe as stripe
import unicodecsv as csv
from dateutil.relativedelta import relativedelta
from django.views.decorators.csrf import csrf_exempt
from django.db import connection

from landingPage.forms import ContactInfoForm
from landingPage.models import ContactFolder, ContactFile, ContactInfo
from app.app_forms.contract_plan_upload_form import ContractPlanUploadForm
from .app_services.delete_message_services import (
	remove_old_file,
	remove_file as remove_file_service,
	get_comments,
	revert_offer_condition,
	create_system_message_plan,
	delete_system_message_project_done,
	getAllComments,
	removeFilePerformance
)
from .app_services.upload_contract_services import (
	regen_contract_and_plan,
	generate_contract,
	generate_plan,
	send_mail_service,
	get_current_form_contract_and_plan_service,
	push_offer_message,
	get_budget_in_form,
	render_artist_contract,
	regen_plan,
	render_confirming_artist_contract,
)
from .services import (
	GetSceneWattingsFeedbackService,
	add_admin_to_project,
	get_offers_in_project_refactor,
	check_budget_admin,
	get_list_creators,
	send_message_update_scene,
	update_peak_in_comment,
	handle_writer_csv_file,
	save_form_create_offer_edit_service,
	update_menu_project_detail,
	save_form_project_setting_modal_service,
	get_link_to_preview,
	add_change_offer_creator_from_admin_to_producer,
	create_system_message,
	get_budget_creator_can_assign,
	save_form_create_offer_service,
	update_topic_service,
	create_offer_creator_for_master_producer,
	done_offer_product,
	save_form_create_offer_project_by_sale_service,
	update_master_producer,
	save_form_create_offer_project_by_contact_artist_service,
	check_sale_content_type,
	sort_offers,
	save_mgk_form_contact_service,
)
from .tasks import (
	create_message_file,
	create_message_folder,
	add_member_into_offer_creator,
	add_remove_member_into_offer_product,
	sendFileToACRFileScanning,
	checkACRForProduct,
)
from .util import parser_date, get_download_link, get_info_file, get_type_file
from accounts.tasks import getACRResultForFile
from app.util import (
	create_folder_media,
	create_info_file,
	remove_file_upload,
	capture_thumbnail_ffmpeg,
)
from django.core.files.storage import FileSystemStorage
from payments.services import (
	query_project_by_offer_creators,
	total_user_reward_in_project,
	get_total_budget_and_used_budget,
)

try:
	from StringIO import StringIO
except ImportError:
	from io import StringIO

# built-in django
from django.conf import settings
from django.core import serializers
from django.core.exceptions import PermissionDenied, EmptyResultSet
from django.core.paginator import Paginator
from django.urls import reverse_lazy, reverse
from django.contrib.auth.mixins import LoginRequiredMixin
from django.utils import timezone
from django.contrib.auth.mixins import UserPassesTestMixin
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db.models import (
	Prefetch,
	Q,
	Case,
	When,
	Value,
	CharField,
	Count,
	Subquery,
	Sum,
	BooleanField,
	IntegerField,
)
from django.db.models.expressions import (
	Case,
	When,
	F,
	Value,
	ExpressionWrapper,
	Subquery,
	OuterRef,
	Exists,
)
from django.http import (
	JsonResponse,
	HttpResponseRedirect,
	HttpResponse,
	HttpResponseBadRequest,
)
from django.shortcuts import redirect, render
from django.template.loader import render_to_string
from django.views import View
from django.views.generic import DetailView
from django.views.generic import (
	TemplateView,
	CreateView,
	UpdateView,
	ListView,
	DeleteView,
	FormView,
)
from django.db import transaction
from django.contrib.auth.tokens import default_token_generator
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode
from django.db.models import Count as dbCount
from django.db.models import F
from django.db.models import Case, When
from django.utils.safestring import mark_safe
from django.core.files import File

# in project
from app.models import (
	ContentSale,
	AudioSale,
	ImageSale,
	Audio,
	Product,
	OfferCreator,
	OfferProduct,
	PlanOffer,
	PreviewProductComment,
	ProductCommentDownloaded,
	ProductMessage,
	MessageReceiver,
	ReviewOffer,
	SceneTitle,
	VariationOffer,
	SaleContent,
	SaleContentVersion,
	RatingSceneTitle,
	SceneCommentReceiver,
	Post,
	PostItem,
	ProductCommentFolder,
	FormContractAndPlan,
	OfferProject,
	OfferUser,
	OfferMessageReceiver,
	ListWork,
	SaleContentListWork,
	TopicGallery,
	HashTag,
	TopicTag,
	Category,
	TopicCategory,
	SelectionGallery,
	SaleContentSelection,
	ProductMessageFile,
	ProductMessageFolder,
	OrderData,
	SceneTitleBookmark,
	ProductCommentFile,
	SceneCommentFile,
	DraftMessage,
	MessageFile,
	UserOnlineStatus,
	SceneTaken,
	SceneTakenScenes,
	Scene as ModelScene,
	UserProductCharge,
	CreatorOfferFile,
	ColorProjectSetting,
	FontProjectSetting,
	ProductMilestone,
	BlockListArtist,
)
from .forms import ProductSetting, SearchCreatorForm, OfferCreatorForm
from accounts.models import (
	ProductUser,
	AuthUser,
	Creator,
	ScheduleCreator,
	CreatorProfile,
	CreatorListCreator,
	CreatorList,
	BlockList,
	Skill,
)
from . import forms
from . import models
from . import tasks
from accounts.services import update_product_user_new_video
from app.templatetags.util import (
	check_child_folder,
	count_list_user_download,
	count_new_update_scene_title,
	count_undownload_product_comment,
	count_unread_message,
	function_count,
	get_weekday_new,
	get_width_for_thumbnail,
	get_avatar,
	count_batch_number_project,
	get_data_offer_by_role,
	get_offer_user,
	get_offer_user_refactor,
	get_data_project_order,
	get_user_seen,
	get_presigned_url_message,
	minus,
	parse_json_message_info,
	get_list_user_download,
	get_postion,
	get_display_fullname,
	get_owners_project,
	get_rating,
	get_thumbnail,
	get_last_message_title_refactor,
	get_schedule_out_of_date,
 	get_offer_user_by_message,
  get_system_message,
	show_user_seen_max,
)
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from accounts.tasks import (
	check_scene_title_status,
	auto_use_last_scene,
	get_creator_profile,
	update_product_user_order,
	get_list_works,
	get_list_search_sale_content,
	bookmark_sale_service,
)
from .serializers import (
	AuthUserGroupOfferSerializer,
	GroupOfferCreatorSerializer,
	MasterAdminProductOfferSerializer,
	GroupOfferProductSerializer,
	OfferProjectSerializer,
	ProductSceneSerializer,
	SceneTitleScheduleSerializer,
	OfferProjectScheduleSerializer,
	ProductSerializer,
	OfferProjectScheduleSerializer,
	SceneTitleSerializer,
	SceneCommentSerializer,
	SectionCreditSerializer,
	ProjectsSerializer,
)

from accounts.serializers import AuthUserSerializer, ProductUserSerializer

from django.db.models.functions import TruncDate

from .views import get_all_skill, get_online_user, get_value_exists
#added rest framework views
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import TokenAuthentication, SessionAuthentication
from rest_framework import status
from rest_framework.renderers import JSONRenderer
from django.contrib.auth.decorators import login_required

TYPE_ORDER_UP = "1"
MESSAGE_PER_PAGE = 40
SECRET = settings.SECRET_KEY
SCENE_PER_PAGE = int(settings.SCENE_PER_PAGE)
SCENE_PER_UPDATE_PAGE = int(settings.SCENE_PER_UPDATE_PAGE)
PROJECT_PER_PAGE = int(settings.PROJECT_PER_PAGE)
PRODUCT_SCENE_LOADED = int(settings.PRODUCT_SCENE_LOADED)
LOAD_MORE_LIST = 3

def get_list_comment(request):
	comment_type = request.GET.get("type", None)
	scene_id = request.GET.get("scene_id", None)
	project_id = request.GET.get("project_id", None)

	try:
		if comment_type == "scene":
			is_deleted = False
			try:
				scene = models.Scene.objects.get(pk=scene_id)
			except:
				scene = models.Scene.original_objects.get(pk=scene_id)
				is_deleted = True
			scene_title = scene.title
			if scene_title:
				scenes = models.Scene.objects.filter(
					title_id=scene.title_id,
					product_scene__isnull=False,
					product__isnull=False,
				).order_by("-modified")
				parent_comments = (
					models.SceneComment.objects.filter(
						Q(scene_id__in=scenes.values_list("scene_id", flat=True))
						| Q(scene_title_id=scene_title.pk),
						parent__isnull=True,
					)
					.order_by("created")
					.select_related("user")
					.prefetch_related(
						Prefetch(
							"child_comment",
							queryset=models.SceneComment.objects.order_by(
								"created"
							).prefetch_related(
								Prefetch(
									"preview_comment",
									queryset=models.PreviewScene.objects.exclude(
										owner_id=request.user.id
									).order_by("-created"),
								)
							),
						)
					)
					.prefetch_related(
						Prefetch(
							"preview_comment",
							queryset=models.PreviewScene.objects.exclude(
								owner_id=request.user.id
							).order_by("-created"),
						)
					)
				)

				if scene_title.status in ["5", "6"] or is_deleted:
					view_only = True
				else:
					view_only = (
						request.user.role == "master_client"
						and ProductUser.objects.filter(
							user=request.user, product=scene.product, view_only=True
						).exists()
					)

				html = render_to_string(
					"top/_comment_container_new_refactor.html",
					{
						"obj": scene,
						"user": request.user,
						"parent_comments": parent_comments,
						"view_only": view_only,
						"type": "scene",
					},
				)
				return JsonResponse({"html": html})
		elif comment_type == "project":
			product = models.Product.objects.get(pk=project_id)
			product_user = ProductUser.objects.filter(
				user=request.user, product=product
			).first()
			if product_user.position not in [
				ProductUser.DIRECTOR,
				ProductUser.OWNER,
				ProductUser.REVIEWER,
				ProductUser.PRODUCER,
			]:
				return JsonResponse({"status": "404"})
			view_only = (
				request.user.role == AuthUser.MASTERCLIENT
				and product_user.view_only == True
			)

			list_count = models.ProductComment.objects.filter(project=product).count()
			total_page = (
				list_count / MESSAGE_PER_PAGE
				if list_count % MESSAGE_PER_PAGE == 0
				else int(list_count / MESSAGE_PER_PAGE) + 1
			)
			comments = getAllComments(product)
			commentFiles = models.ProductCommentFile.objects.filter(
				message__in=comments
			).order_by("-created")
			comment_files = list(commentFiles.values())
			# Add MediaConvert data to files
			comment_files = add_mediaconvert_data_to_files(comment_files)
			for file in comment_files:
				if get_type_file(file["real_name"]):
					file["type_file_name"] = get_type_file(file["real_name"])
			comment_folders = list(models.ProductCommentFolder.objects.filter(
				message__in=comments
			).order_by("-created").values())

			listExtraFileInfo = getUrlFile(comment_type, commentFiles)
			filterComment = list(comments.values())[-MESSAGE_PER_PAGE:]
			listSameRole = list_check_is_same_role(
				request.user.role, get_comments(product)
			)
			rawArray = list(map(lambda x: x["parent_id"], filterComment))
			listParentComment = [i for i in rawArray if i is not None]

			newParentComemnts = models.ProductComment.objects.filter(pk__in=listParentComment)
			parentComment = list(newParentComemnts.values())
			dictFolder = {str(item["message_id"]): {"folder_id": str(item["folder_id"]),"name": str(item["name"])} for item in comment_folders}
			dictFile = {str(item["message_id"]): {"file_id": str(item["file_id"]),"name": str(item["real_name"])} for item in comment_files}
			for item in parentComment:
				messageId = str(item["comment_id"])
				if str(item["comment_id"]) in dictFolder:
					fileExist = dictFolder.get(messageId)
					item["folder_id"] = fileExist["folder_id"]
					item["name"] = fileExist["name"]
				elif str(item["comment_id"]) in dictFile:
					fileExist = dictFile.get(messageId)
					item["file_id"] = fileExist["file_id"]
					item["name"] = fileExist["name"]
			is_seen = models.SceneCommentReceiver.objects.filter(
				product_comment__project=product,
				seen_date__isnull=True,
				user=request.user,
			).exists()
			is_pc_device = True if request.user_agent.is_pc else False
			return JsonResponse(
				{
					"total_page": total_page,
					"list_extra_file_info": listExtraFileInfo,
					"list_same_role": listSameRole,
					"comments": filterComment,
					"is_seen": is_seen,
					"view_only": view_only,
					"type": "product",
					"is_pc_device": is_pc_device,
					"folder": comment_folders,
					"comment_files": comment_files,
					"current_user_id": request.user.id,
					"current_user_role": request.user.role,
					"parent_comments": parentComment,
					"dictFile": dictFile
				}
			)
		return JsonResponse({"html": html})
	except:
		pass
	return JsonResponse({"status": "404"})

def get_load_more_offer_message(request):
	offer_id = request.GET.get("offer_id", None)
	if not offer_id:
		return JsonResponse({"html": "", "status": "failed"})

	real_offer = OfferProject.objects.filter(pk=offer_id).first()

	if not real_offer or real_offer.offer_creator and real_offer.offer_product:
		return JsonResponse({"html": "", "status": "failed"})

	offer_user = OfferUser.objects.filter(user=request.user, offer=real_offer).first()

	if offer_user:
		last_message_id = request.GET.get("last_message_id", None)
		last_message = None
		type_comment = "messenger_owner"
		if last_message_id:
			if real_offer.offer_product:
				last_message = models.ProductMessage.objects.filter(
					pk=last_message_id
				).first()
			else:
				last_message = models.OfferMessage.objects.filter(
					pk=last_message_id
				).first()
				type_comment = "messenger"

		offset = request.GET.get("offset")
		offset = int(offset) if offset.isdigit() else 0
		messages = get_messages(real_offer, offset, last_message)
		html = render_to_string(
			"top/_item_messages_refactor.html",
			{
				"user": request.user,
				"messages": messages,
				"type_comment": type_comment,
				"offer": real_offer.offer_product,
				"offer_user": offer_user,
				"type": type_comment,
			},
		)
		return JsonResponse({"html": html}, status=200)

def get_messenger_artist(request):
	product_id = request.GET.get("project_id")
	offer_id = request.GET.get("offer_active")
	filter_offer = request.GET.get("filter_offer")
	user_id = request.GET.get("user_id")
	context = {}
	user = request.user
	project = Product.objects.get(pk=product_id)
	project_user = ProductUser.objects.get(user=user, product=project)
	type_model = ""
	offer_class = ""
	if project_user.position in [
		ProductUser.DIRECTOR,
		ProductUser.STAFF,
		ProductUser.MASTERADMIN,
	]:
		type_model = "messenger"
		offer_class = OfferCreator
	elif project_user.position in [ProductUser.OWNER]:
		type_model = "messenger_owner"
		offer_class = OfferProduct
	if project_user:
		if not filter_offer or filter_offer == "search":
			try:
				waiting_offer = get_offers_in_project_refactor(
					project, user, project_user, "waiting", type_model
				).exists()
				processing_offer = get_offers_in_project_refactor(
					project, user, project_user, "processing", type_model
				).exists()
				if filter_offer == "search":
					waiting_offer = False
					processing_offer = False
				if waiting_offer:
					filter_offer = "waiting"
				elif processing_offer:
					filter_offer = "processing"
				elif user.role != AuthUser.MASTERCLIENT:
					context.update({"page": "search"})
					context = {}
					form = SearchCreatorForm()
					offer_form = OfferCreatorForm()
					role_creators = Creator.ROLE_CREATOR
					is_done_project = False
					list_value = get_value_exists(project, request.user)

					list_scenes = list_value[0]
					list_quantity = list_value[1]
					list_data_format = list_value[2]

					context.update(
						{
							"project": project,
							"form": form,
							"offer_form": offer_form,
							"role_creators": role_creators,
							"is_done_project": is_done_project,
							"skills": get_all_skill(),
							"list_scenes": list_scenes,
							"list_quantity": list_quantity,
							"list_data_format": list_data_format,
							"page": "search",
						}
					)

			except:
				pass

		# if filter_offer != "search":
		# 	if offer_id:
		# 		try:
		# 			offer = OfferProject.objects.get(pk=offer_id)
		# 			status = offer.offer_status
		# 			role = user.role
		# 			if status in OfferProject.STATUS_IN_PROGRESS:
		# 				filter_offer = "waiting"
		# 			elif status in OfferProject.STATUS_IN_DONE:
		# 				filter_offer = "processing"
		# 		except offer_class.DoesNotExist:
		# 			filter_offer = "not-exists"

		offers = get_offers_in_project_refactor(
			project, user, project_user, filter_offer, type_model
		)
		offers = sort_offers(offers, user)
		offers = offers.distinct().select_related(
			"project", "offer_creator", "offer_product"
		)
		offer_ids = offers.values_list("offer_id", flat=True)
		offer_users = OfferUser.objects.filter(user=user, offer__in=offer_ids).order_by(
			"pk"
		)
		prefetch_offer_users = Prefetch(
			"offeruser_set", queryset=offer_users, to_attr="prefetched_offer_users"
		)
		same_offer_users = (
			OfferUser.objects.filter(offer__in=offer_ids, user__is_active=True)
			.select_related("user")
			.order_by("pk")
		)
		prefetch_same_offer_users = Prefetch(
			"offeruser_set",
			queryset=same_offer_users,
			to_attr="prefetched_same_offer_users",
		)
		offers = offers.prefetch_related(
			prefetch_offer_users, prefetch_same_offer_users
		)

		unreadMap = dict(OfferMessageReceiver.count_unread_message(user_id, product_id))
		when_list = [When(offer_id=offer_id, then=Value(unread_count)) for offer_id, unread_count in unreadMap.items()]
		offers = offers.annotate(
			unread_message_count=Case(
				*when_list,
				default=Value(0), 
				output_field=IntegerField(),
			)
		)

		form_contract_and_plan, current_product_message_file = (
			get_current_form_contract_and_plan_service(project)
		)
		context.update(
			{
				"project": project,
				"offers": offers,
				"user": request.user,
				"offer_status": filter_offer,
				"project_user": project_user,
				"owner": project.get_owner(),
				"form_contract_and_plan": form_contract_and_plan,
				"product_message_file": current_product_message_file,
			}
		)
		isMasterAdmin = False
		if project_user.position in [
			ProductUser.MASTERADMIN,
		]:
			isMasterAdmin = True
		html = render_to_string("messenger/_offers_project_refactor_v2.html", context)
		response = {
			"html": html,
			"offer_status": filter_offer,
			"offer_active": offer_id,
			'isMasterAdmin': isMasterAdmin,
			"count_offer": len(offers)
		}

		list_scenes = get_list_name_to_search(project, project_user, user)
		response.update({"list_scenes": list_scenes})

		return JsonResponse(response, status=200)
	return JsonResponse({}, status=200)

def get_load_offer_message(request):
	offer_id = request.GET.get("offer_id")
	if not offer_id:
		return JsonResponse({"html": "", "status": "failed"})

	real_offer = OfferProject.objects.filter(pk=offer_id).first()
	if not real_offer or real_offer.offer_creator and real_offer.offer_product:
		return JsonResponse({"html": "", "status": "failed"})

	offer_user = OfferUser.objects.filter(user=request.user, offer=real_offer).first()
	if not offer_user:
		return JsonResponse({"html": "", "status": "failed"})

	if real_offer.offer_creator:
		offer = real_offer.offer_creator
		target = (
			models.OfferCreator.objects.filter(pk=offer.pk)
			.prefetch_related(
				Prefetch(
					"message_offer",
					queryset=models.OfferMessage.objects.order_by("created"),
				)
			)
			.first()
		)

		if not target:
			return JsonResponse({"html": "", "status": "failed"})

		receiver = target.admin if request.user != target.admin else target.creator
		thread_name = receiver.get_display_name()
		is_seen = not target.check_offer_is_unread(request.user)

		message_files = target.message_offer.filter(has_file=True)
		file_messages = (
			models.MessageFile.objects.filter(
				message__in=message_files, folder__isnull=True
			)
			.select_related("message", "message__offer", "message__user")
			.prefetch_related(
				Prefetch(
					"user_downloaded",
					queryset=models.DownloadedMessageFile.objects.select_related(
						"user"
					),
				)
			)
			.order_by("-created")
		)
		folder_messages = (
			models.MessageFolder.objects.filter(
				message__in=message_files, parent__isnull=True
			)
			.select_related("message", "message__offer", "message__user")
			.prefetch_related(
				Prefetch(
					"child_folders",
					queryset=models.MessageFolder.objects.prefetch_related(
						Prefetch(
							"child_folders", queryset=models.MessageFolder.objects.all()
						)
					),
				),
				Prefetch(
					"children",
					queryset=models.MessageFile.objects.prefetch_related(
						Prefetch(
							"user_downloaded",
							queryset=models.DownloadedMessageFile.objects.select_related(
								"user"
							),
						)
					),
				),
			)
			.order_by("-created")
		)

		list_review = ReviewOffer.objects.filter(offer_id=offer_id.replace("-", ""))
		user_ids = [int(review.user_id) for review in list_review]

		dict_files = {}
		for file in file_messages:
			dict_files[file.pk] = {"object": file, "modified": file.modified}

		for folder in folder_messages:
			dict_files[folder.pk] = {"object": folder, "modified": folder.modified}

		dict_files = {
			k: v
			for k, v in sorted(
				dict_files.items(), key=lambda item: item[1]["modified"], reverse=True
			)
		}

		list_count = target.message_offer.count()
		total_page = (list_count // MESSAGE_PER_PAGE) + (
			1 if list_count % MESSAGE_PER_PAGE != 0 else 0
		)
		id_review_offer = [offer.admin.pk, offer.creator.pk]
		messages = get_messages(target.offer)
		prev_messages = messages
		next_messages = None
		list_messages_afterreview = []
		firstSystem = None
		if offer.accept_time:
			firstSystem = messages.filter(
				Q(type_message='2') 
			).first()
			prev_messages = messages.filter(
				Q(created__lt=offer.accept_time) 
			).union(messages.filter(pk=firstSystem.pk) if firstSystem is not None else messages.none())
			next_messages = messages.filter(
				Q(created__gt=offer.accept_time)
			).exclude(pk=firstSystem.pk if firstSystem else None)
			
			if len(list_review) > 0:
				for review in list_review:
					filter_message = next_messages.filter(created__lt=review.created)
					next_messages = next_messages.exclude(created__lt=review.created)
					list_messages_afterreview.append(filter_message)
				list_messages_afterreview.append(next_messages.filter(created__gt=list_review[len(list_review) - 1].created))
			else:
				list_messages_afterreview.append(next_messages)

		context = {
			"offer": target,
			"real_offer": real_offer,
			"user": request.user,
			"is_seen": is_seen,
			"request": request,
			"thread_name": thread_name,
			"receiver": receiver,
			"message_file": message_files,
			"dict_files": dict_files,
			"type_comment": "messenger",
			"prev_messages": prev_messages,
			"next_messages": next_messages,
			"offer_user": offer_user,
			"messages": messages,
			"offer_creator": offer,
			"user_ids": user_ids,
			"id_review_offer": id_review_offer,
				"list_messages_afterreview": list_messages_afterreview,
			"list_review": list_review,
		}

		html = render_to_string("messenger/_offer_detail_refactor.html", context)
		infor_html = render_to_string("messenger/_offer_infor_refactor.html", context)

		return JsonResponse(
			{
				"html": html,
				"infor_html": infor_html,
				"status": "success",
				"total_page": total_page,
			}
		)

	else:
		offer = real_offer.offer_product
		target = models.OfferProduct.objects.filter(pk=offer.pk).first()

		if not target:
			return JsonResponse({"html": "", "status": "failed"})

		thread_name = target.project.get_name_by_contract()
		is_seen = not target.check_offer_is_unread(request.user)
		message_files = target.message_product.filter()
		file_messages = (
			models.ProductMessageFile.objects.filter(
				(
					Q(message__in=message_files)
					| Q(offer=target, type_file=models.ProductMessageFile.OFFER)
				)
				& Q(folder__isnull=True)
			)
			.select_related("message", "message__offer", "message__user")
			.prefetch_related(
				Prefetch(
					"user_downloaded",
					queryset=models.DownloadedProductMessageFile.objects.select_related(
						"user"
					),
				)
			)
			.order_by("-created")
		)
		folder_messages = (
			models.ProductMessageFolder.objects.filter(
				(
					Q(message__in=message_files)
					| Q(offer=target, type_file=models.ProductMessageFolder.OFFER)
				)
				& Q(parent__isnull=True)
			)
			.select_related("message", "message__offer", "message__user")
			.prefetch_related(
				Prefetch(
					"child_folders",
					queryset=models.ProductMessageFolder.objects.prefetch_related(
						Prefetch(
							"child_folders",
							queryset=models.ProductMessageFolder.objects.all(),
						)
					),
				),
				Prefetch(
					"children",
					queryset=models.ProductMessageFile.objects.prefetch_related(
						Prefetch(
							"user_downloaded",
							queryset=models.DownloadedProductMessageFile.objects.select_related(
								"user"
							),
						)
					),
				),
			)
			.order_by("-created")
		)

		dict_files = {}
		for file in file_messages:
			dict_files[file.pk] = {"object": file, "modified": file.modified}

		for folder in folder_messages:
			dict_files[folder.pk] = {"object": folder, "modified": folder.modified}

		dict_files = {
			k: v
			for k, v in sorted(
				dict_files.items(), key=lambda item: item[1]["modified"], reverse=True
			)
		}

		list_count = target.message_product.count()
		total_page = (list_count // MESSAGE_PER_PAGE) + (
			1 if list_count % MESSAGE_PER_PAGE != 0 else 0
		)
		messages = get_messages(target.offer)
		is_pc_device = request.user_agent.is_pc

		context = {
			"offer": target,
			"real_offer": real_offer,
			"user": request.user,
			"is_seen": is_seen,
			"request": request,
			"thread_name": thread_name,
			"dict_files": dict_files,
			"type_comment": "messenger_owner",
			"offer_user": offer_user,
			"messages": messages,
			"is_pc_device": is_pc_device,
		}

		html = render_to_string("messenger/_offer_detail_refactor_v2.html", context)
		infor_html = render_to_string("direct/_offer_infor_refactor.html", context)

		context_data = {}
		if target.condition in OfferProduct.STATUS_SHOW_MENU:
			file_contract = target.get_contract_in_offer()
			if file_contract:
				context_data["file_contract"] = str(file_contract.pk)

		if target.condition == OfferProduct.STATUS_PAYMENTED or target.receipt_id:
			file_bill = target.get_bill_in_offer()
			if file_bill:
				context_data["file_bill"] = str(file_bill.pk)

		context_data.update(
			{
				"html": html,
				"infor_html": infor_html,
				"status": "success",
				"total_page": total_page,
			}
		)

		return JsonResponse(context_data)

def get_list_name_to_search(project, project_user, user):
	offer_id_creators = OfferUser.objects.filter(
		position__in=[OfferUser.ADMIN, OfferUser.CREATOR],
		user=user,
		offer__project=project,
	).values_list("offer_id", flat=True)

	offer_creators = (
		project.product_offers.filter(offer__pk__in=offer_id_creators)
		.exclude(status=OfferCreator.STATUS_REJECT)
		.select_related("scenes")
	)

	if project_user.position in [ProductUser.MASTERADMIN, ProductUser.PRODUCER]:
		list_all_scenes = set(offer_creators.values_list("scenes", flat=True))
		list_all_scenes.add(project.get_project_name())
	elif project_user.position == ProductUser.OWNER:
		list_all_scenes = {project.get_project_name()}
	else:
		list_all_scenes = set(offer_creators.values_list("scenes", flat=True))

	list_scenes = sorted(list(list_all_scenes))

	return list_scenes

def get_messages(real_offer, offset=0, last_message=None):
	messages = None
	if real_offer.offer_product:
		offer = real_offer.offer_product
		if last_message:
			messages = offer.message_product.filter(
				created__lte=last_message.created
			).order_by("-created")[MESSAGE_PER_PAGE * offset :][:MESSAGE_PER_PAGE]
		else:
			messages = offer.message_product.order_by("-created")[
				MESSAGE_PER_PAGE * offset :
			][:MESSAGE_PER_PAGE]
		if messages:
			ids = list(messages.values_list("pk", flat=True))
			messages = (
				offer.message_product.filter(pk__in=ids)
				.select_related("user")
				.prefetch_related(
					Prefetch(
						"files", queryset=ProductMessageFile.objects.order_by("created")
					)
				)
				.order_by("created")
			)
	else:
		offer = real_offer.offer_creator
		if last_message:
			messages = offer.message_offer.filter(
				created__lte=last_message.created
			).order_by("-created")[MESSAGE_PER_PAGE * offset :][:MESSAGE_PER_PAGE]
		else:
			messages = offer.message_offer.order_by("-created")[
				MESSAGE_PER_PAGE * offset :
			][:MESSAGE_PER_PAGE]
		if messages:
			ids = list(messages.values_list("pk", flat=True))
			messages = (
				offer.message_offer.filter(pk__in=ids)
				.select_related("user")
				.prefetch_related(
					Prefetch("files", queryset=MessageFile.objects.order_by("created"))
				)
				.order_by("created")
			)
	return messages

def load_more_comments(request):
	project_id = request.GET.get("project_id", None)
	name = ""
	real_name = ""
	if not project_id:
		return JsonResponse({"status": "500"})
	product = Product.objects.filter(pk=project_id).first()
	if not product:
		return JsonResponse({"status": "500"})
	last_message_id = request.GET.get("last_message_id", None)
	if last_message_id:
		last_message = models.ProductComment.objects.filter(pk=last_message_id).first()
	offset = request.GET.get("offset")
	offset = int(offset) if offset.isdigit() else 0
	comments = get_comments(product, offset, last_message)

	comment_type = request.GET.get("type", None)
	rawArray = list(map(lambda x: x["parent_id"], list(comments.values())))
	listParentComment = [i for i in rawArray if i is not None]
	newParentComemnts = models.ProductComment.objects.filter(pk__in=listParentComment)
	listFile = list(models.ProductCommentFile.objects.filter(message__in=newParentComemnts).values())
	listFolder = list(models.ProductCommentFolder.objects.filter(message__in=newParentComemnts).values())
	listParentUpdate = list(newParentComemnts.values())
	dictFolder = {str(item["message_id"]): {"folder_id": str(item["folder_id"]),"name": str(item["name"])} for item in listFolder}
	dictFile = {str(item["message_id"]): {"file_id": str(item["file_id"]),"name": str(item["real_name"])} for item in listFile}
	for item in listParentUpdate:
		messageId = str(item["comment_id"])
		if str(item["comment_id"]) in dictFolder:
			fileExist = dictFolder.get(messageId)
			item["folder_id"] = fileExist["folder_id"]
			item["name"] = fileExist["name"]
		elif str(item["comment_id"]) in dictFile:
			fileExist = dictFile.get(messageId)
			item["file_id"] = fileExist["file_id"]
			item["name"] = fileExist["name"]
	return JsonResponse(
		{
			"comments": list(comments.values()),
			"list_extra_file_info": get_list_extra_file_info(comment_type, comments),
			"list_same_role": list_check_is_same_role(request.user.role, comments),
				"parent_comments": listParentUpdate,
		},
		status=200,
	)

def getUrlFile(comment_type, comment_files):
	listExtraFileInfo = []
	for comment_file in comment_files:
		list_downloaded_user = get_list_user_download(comment_file, comment_type)
		list_downloaded_user_info = []
		if list_downloaded_user is not None:
			for downloaded_user in list_downloaded_user:
				list_downloaded_user_info.append(
					{
						"display_name": downloaded_user.get_display_name(),
						"avatar": get_avatar(downloaded_user, "small"),
					}
				)
		if comment_file.type_file_name == "document":
			result = parse_json_message_info(comment_file.file_info, "document")
			listExtraFileInfo.append(
				{
					"presigned_url": get_presigned_url_message(result.get("url_image")),
					"file_id": str(comment_file.pk),
					"list_user_downloaded": list_downloaded_user_info,
				}
			)
		elif comment_file.type_file_name == "audio":
			listExtraFileInfo.append(
				{
					"presigned_url": comment_file.file.url,
					"file_id": str(comment_file.pk),
					"list_user_downloaded": list_downloaded_user_info,
				}
			)
		else:
			listExtraFileInfo.append(
				{
					"file_id": str(comment_file.pk),
					"list_user_downloaded": list_downloaded_user_info,
				}
			)
	return listExtraFileInfo

def get_list_extra_file_info(comment_type, comments):
	comment_files = models.ProductCommentFile.objects.filter(
		message__in=comments
	).order_by("-created")
	listExtraFileInfo = []
	for comment_file in comment_files:
		list_downloaded_user = get_list_user_download(comment_file, comment_type)
		list_downloaded_user_info = []
		if list_downloaded_user is not None:
			for downloaded_user in list_downloaded_user:
				list_downloaded_user_info.append(
					{
						"display_name": downloaded_user.get_display_name(),
						"avatar": get_avatar(downloaded_user, "small"),
					}
				)
		if comment_file.type_file_name == "document":
			result = parse_json_message_info(comment_file.file_info, "document")
			listExtraFileInfo.append(
				{
					"presigned_url": get_presigned_url_message(result.get("url_image")),
					"file_id": str(comment_file.pk),
					"list_user_downloaded": list_downloaded_user_info,
				}
			)
		elif comment_file.type_file_name == "audio":
			listExtraFileInfo.append(
				{
					"presigned_url": comment_file.file.url,
					"file_id": str(comment_file.pk),
					"list_user_downloaded": list_downloaded_user_info,
				}
			)
		else:
			listExtraFileInfo.append(
				{
					"file_id": str(comment_file.pk),
					"list_user_downloaded": list_downloaded_user_info,
				}
			)
	return listExtraFileInfo

def datetime_converter(o):
	if isinstance(o, datetime.datetime):
		return o.isoformat()

def create_and_update_comment(request):
	try:
		scene_id = request.POST.get("scene_id", None)
		comment = request.POST.get("message", None)
		parent_id = request.POST.get("parent_id", None)
		comment_type = request.POST.get("type", None)
		project_id = request.POST.get("project_id", None)
		list_file_id = request.POST.get("list_file_id", None)
		list_folder_id = request.POST.get("list_folder_id", None)
		parent_id = request.POST.get("parent_id", None)
		parent_comments = []
		fileListParent = []
		folderListParent = []
		name = ""
		real_name = ""

		if parent_id != "undefined":
			parent_comments_queryset = models.ProductComment.objects.filter(pk__in=[parent_id])
			fileListParent = list(models.ProductCommentFile.objects.filter(message__in=parent_comments_queryset).values())
			folderListParent = list(models.ProductCommentFolder.objects.filter(message__in=parent_comments_queryset).values())
			parent_comments = list(parent_comments_queryset.values())
			if len(folderListParent) > 0:
				name = folderListParent[0]["name"]
			elif len(fileListParent) > 0:
				real_name = fileListParent[0]["real_name"]
			for i in range(0, len(parent_comments)):
				parent_comments[i]["comment_id"] = parent_id
		success_message = ""
		is_pc_device = is_pc_device = True if request.user_agent.is_pc else False
		splitListFolder = []
		if list_file_id:
			list_file_id = list_file_id.split(",")
			splitListFolder = list_folder_id.split(",")

		if comment_type == "project":
			project = models.Product.objects.get(pk=project_id)
			if request.user not in project.get_member_project_detail():
				return JsonResponse({"status": "500"}, status=500)
			try:
				parent = models.ProductComment.objects.get(pk=parent_id)
			except:
				parent = None

			comment = models.ProductComment.objects.create(
				project=project, comment=comment, user=request.user, parent=parent
			)
			listFile = []
			listFolder = []
			urlFile = None
			if list_file_id:
				success_message = "アップロードしました。"
				comment.has_file = True
				comment.save()
				list_files = models.ProductCommentFile.objects.filter(
					pk__in=list_file_id
				)
				list_files.update(message=comment)
				# list_file_idの順序を保持してファイルを取得
				listFile = []
				list_files_dict = {str(f.pk): f for f in models.ProductCommentFile.objects.filter(pk__in=list_file_id).select_related('message', 'folder')}
				for file_id in list_file_id:
					if file_id in list_files_dict:
						f = list_files_dict[file_id]
						listFile.append({
							'file_id': str(f.pk),
							'message_id': str(f.message_id) if f.message_id else None,
							'file': str(f.file) if f.file else None,
							'real_name': f.real_name,
							'peaks': f.peaks,
							'folder_id': str(f.folder_id) if f.folder_id else None,
							'type_file_name': f.type_file_name,
							'file_info': f.file_info,
							'created': f.created,
							'modified': f.modified,
						})
				# 順序を保持したファイルリストを作成
				ordered_files = []
				for file_id in list_file_id:
					if file_id in list_files_dict:
						ordered_files.append(list_files_dict[file_id])
				urlFile = getUrlFile(comment_type, ordered_files)
			if list_folder_id:
				tasks.update_folder(list_folder_id, "product", comment.pk)
				list_folders = models.ProductCommentFolder.objects.filter(
					pk__in=splitListFolder
						)
				listFolder = list(list_folders.values())

			list_user = project.get_member_project_detail()

			receivers = list_user.exclude(pk=request.user.pk)
			if receivers.exists():
				for receiver in receivers:
					SceneCommentReceiver.objects.create(
						product_comment=comment, user=receiver, seen_date=None
					)

			data = list()
			data.append(comment)
			channel_layer = get_channel_layer()
			for i in range(0, len(listFile)):
				listFile[i]["message_id"] = str(comment.pk)
				if get_type_file(listFile[i]["real_name"]):
					listFile[i]["type_file_name"] = get_type_file(listFile[i]["real_name"])
				if(len(splitListFolder) > 0 and not listFile[i].get("folder_id")):
					listFile[i]["folder_id"] = splitListFolder[0]
			for i in range(0, len(listFolder)):
				listFolder[i]["folder_id"] = splitListFolder[i]
				listFolder[i]["message_id"] = str(comment.pk)
			list_all_user = AuthUser.objects.filter(pk__in=list_user)
			for identity in list_user:
				new_offer_message = {
					"type": "offer_message",
					"type_message": "project",
					"comment": serializers.serialize("json", data),
					"message_id": str(comment.pk),
					"action": "new_message",
					"sender_id": request.user.pk,
					"sender_role": request.user.role,
					"product_id": project_id,
					"is_pc_device": is_pc_device,
					"listFile": json.dumps(listFile, default=datetime_converter),
					"listFolder": json.dumps(listFolder, default=datetime_converter),
					"parent_comments": json.dumps(parent_comments, default=datetime_converter),
					"real_name": real_name,
					"list_extra_file_info": json.dumps(urlFile, default=datetime_converter),
					"name": name,
					"user_avatar": get_avatar(comment.user, "medium"),
				}

				new_offer_message["count_file_un_download"] = (
					project.count_undownloaded_file_storage(identity)
				)

				async_to_sync(channel_layer.group_send)(
					"{}".format(identity.pk), new_offer_message
				)
			update_menu_project_detail(project)
			user = request.user
			host = settings.HOST
			scheme = request.scheme
			url = (
				reverse_lazy("app:top_project_detail", kwargs={"pk": project.pk})
				+ "?tab=product-comment"
			)
			path = "{host}{url}".format(host=host, url=url)
			recipients_id = []
			if user.role == "master_client":
				recipients_id = list(
					project.productuser_set.filter(
						is_invited=False,
						position__in=[ProductUser.DIRECTOR, ProductUser.PRODUCER],
						user__is_active=True,
						notification="on",
					).values_list("user_id", flat=True)
				)
			elif user.role == "admin":
				recipients_id = list(
					project.productuser_set.filter(
						is_invited=False,
						user__role="master_client",
						user__is_active=True,
						notification="on",
					).values_list("user_id", flat=True)
				)
			if recipients_id:
				tasks.send_email_when_create_comment_product.delay(
					comment.pk, recipients_id, user.pk, scheme, host, path
				)

			return JsonResponse(
				{"status": "200", "success_message": success_message}, status=200
			)

		elif comment_type == "scene":
			has_pin = request.POST.get("has_pin", None)
			scene_title_id = request.POST.get("scene_title_id", None)
			pin_time = request.POST.get("pin_time", None)

			if scene_id is None and scene_title_id is None and parent_id is None:
				return JsonResponse({"status": "500"}, status=500)
			scene = None
			scene_title = models.SceneTitle.objects.get(pk=scene_title_id)
			product = scene_title.product_scene.product_scene.first()
			if request.user not in product.get_member_project_detail():
				return JsonResponse({"status": "500"}, status=500)

			parent = None
			if scene_id != "":
				scene = models.Scene.objects.get(pk=scene_id)
			if parent_id != "":
				parent = models.SceneComment.objects.get(pk=parent_id)
			scene_comment = models.SceneComment.objects.create(
				comment=comment, user=request.user, parent=parent
			)
			if list_file_id:
				success_message = "アップロードしました。"
				scene_comment.has_file = True
				scene_comment.save()
				list_files = models.SceneCommentFile.objects.filter(pk__in=list_file_id)
				for f in list_files:
					f.message = scene_comment
					f.save()

			if list_folder_id:
				tasks.update_folder(list_folder_id, "scene", scene_comment.pk)

			if has_pin == "false" and scene_title:
				scene_comment.scene_title = scene_title
				scene_comment.save()
			elif has_pin == "true" and scene:
				scene_comment.scene = scene
				scene_comment.pin_time = pin_time
				scene_comment.pin_video = scene_id
				scene_comment.save()

			if scene_comment.has_file:
				try:
					duration = int(request.POST.get("duration", -1))
				except:
					duration = -1
				for message_file in scene_comment.files.all():
					if message_file.is_audio_file() in "audio, video":
						# tasks.check_acr_for_scene_comment.delay(file=str(message_file.pk), re_config=None,
						#											 duration=duration)
						sendFileToACRFileScanning.delay(
							str(message_file.pk), message_file.__class__.__name__
						)
			list_user = product.get_member_project_detail()

			receivers = list_user.exclude(pk=request.user.pk)
			if receivers.exists():
				for receiver in receivers:
					SceneCommentReceiver.objects.create(
						message=scene_comment, user=receiver, seen_date=None
					)

			file_infor_html = render_to_string(
				"top/_comment_file_infor.html",
				{
					"message": scene_comment,
					"user": request.user,
					"type_comment": "scene_comment",
				},
			)

			if scene_id is None or scene_id == "":
				message_send_html = render_to_string(
					"top/_message_send.html",
					{
						"message": scene_comment,
						"user": request.user,
						"type": "scene_comment",
					},
				)
				message_send_by_role_html = render_to_string(
					"top/_message_send.html", {"message": scene_comment, "user": ""}
				)
				message_received_html = render_to_string(
					"top/_message_received.html",
					{"message": scene_comment, "user": "", "type": "scene_comment"},
				)
			else:
				message_send_html = render_to_string(
					"top/_message_send_2.html",
					{
						"message": scene_comment,
						"user": request.user,
						"type": "scene_comment",
					},
				)
				message_send_by_role_html = render_to_string(
					"top/_message_send_2.html", {"message": scene_comment, "user": ""}
				)
				message_received_html = render_to_string(
					"top/_message_received_2.html",
					{"message": scene_comment, "user": "", "type": "scene_comment"},
				)

			data = list()
			data.append(scene_comment)
			channel_layer = get_channel_layer()
			for identity in list_user:
				async_to_sync(channel_layer.group_send)(
					"{}".format(identity.pk),
					{
						"type": "offer_message",
						"comment": serializers.serialize("json", data),
						"message_id": str(scene_comment.pk),
						"action": "new_message",
						"sender_id": request.user.pk,
						"sender_role": request.user.role,
						"scene_title_id": scene_title_id,
						"project_id": str(product.pk),
						"is_pc_device": is_pc_device,
					},
				)

			check_scene_title_status(scene_title)
			if request.user.role == "admin":
				scene_title.tag = True
				scene_title.admin_updated_at = datetime.datetime.now()
			else:
				scene_title.tag = False

			scene_title.updated_at = datetime.datetime.now()
			scene_title.save()
			product.save()
			send_message_update_scene(product)

			return JsonResponse(
				{"status": "200", "success_message": success_message}, status=200
			)
	except:
		return JsonResponse({"status": "500"}, status=500)

def seen_comment(request):
	try:
		comment_type = request.POST.get('comment_type', None)
		if comment_type == 'scene':
			scene_title_id = request.POST.get('scene_title_id', None)
			scene_title = models.SceneTitle.objects.get(pk=scene_title_id)
			scenes = scene_title.scene_title.all()
			comments = models.SceneCommentReceiver.objects.filter(seen_date__isnull=True, user=request.user).filter(
				Q(message__scene__in=scenes) | Q(message__scene_title=scene_title)).order_by('-message__created')
			if comments.exists():
				comment_id = [str(comment.message_id) for comment in comments]
				comments.update(seen_date=datetime.datetime.now())
				product = scene_title.product_scene.product_scene.first()
				list_user = product.get_member_project_detail()
				channel_layer = get_channel_layer()
				for identity in list_user:
					async_to_sync(channel_layer.group_send)(
						'{}'.format(identity.pk),
						{
							'type': 'chat_message',
							'message': 'all',
							'action': 'seen_comment',
							'comment_id': comment_id,
							'scene_title_id': scene_title_id,
						}
					)
				return JsonResponse({'status': '200'}, status=200)
		elif comment_type == 'project':
			product_id = request.POST.get('product_id', None)
			product = Product.objects.get(pk=product_id)
			comments = models.SceneCommentReceiver.objects.filter(product_comment__project=product,
				seen_date__isnull=True, user=request.user)\
				.order_by('-product_comment__created')
			if comments.exists():
				comment_id = [str(comment.product_comment_id) for comment in comments]
				comments.update(seen_date=datetime.datetime.now())
				url = get_avatar(request.user, "small")
				list_user = product.get_member_project_detail()
				channel_layer = get_channel_layer()
				for identity in list_user:
					async_to_sync(channel_layer.group_send)(
						'{}'.format(identity.pk),
						{
							'type': 'chat_message',
							'message': 'all',
							'type_message': 'project',
							'action': 'seen_comment',
							'comment_id': comment_id,
							'product_id': product_id,
							"url": url,
						}
					)
				return JsonResponse({'status': '200'}, status=200)
	except:
		pass
	return JsonResponse({'status': '200'}, status=200)


def list_check_is_same_role(userRole, comments):
	listSameRole = []
	for comment in comments:
		listUserSeen = get_user_seen(comment, "XXX")
		listUserAvatar = []
		for user_seen in listUserSeen:
			listUserAvatar.append({"user_avatar": get_avatar(user_seen, "small")})
		json = {
			"comment_id": comment.comment_id,
			"is_same_role": comment.user.role == userRole,
			"user_avatar": get_avatar(comment.user, "medium"),
			"display_name": comment.user.get_display_name(),
			"list_user_seen": listUserAvatar,
		}
		listSameRole.append(json)
	return listSameRole

def change_project_scene_order(request):
	base_scene_id = request.GET.get("base_scene_id")
	finish_scene_id = request.GET.get("finish_scene_id")
	base_scene = models.ProductScene.objects.filter(pk=base_scene_id).first()
	finish_scene = models.ProductScene.objects.filter(pk=finish_scene_id).first()
	product = base_scene.product_scene.first()
	from_id = base_scene.order
	to_id = finish_scene.order

	if base_scene.order > finish_scene.order:
		ps_list = models.ProductScene.original_objects.filter(
			product_scene=product, order__gte=to_id, order__lt=from_id
		)
		base_scene.order = to_id
		for ps in ps_list:
			ps.order += 1
			ps.save()
		base_scene.save()
		return JsonResponse({"success": "false"}, status=200)
	else:
		ps_list = models.ProductScene.original_objects.filter(
			product_scene=product, order__lte=to_id, order__gt=from_id
		)
		base_scene.order = to_id
		for ps in ps_list:
			ps.order -= 1
			ps.save()
		base_scene.save()
		return JsonResponse({"success": "true"}, status=200)


def get_project_process_video(request):
	project_id = request.GET.get("project_id", None)
	variation_id = request.GET.get("variation_id", None)
	html = ""
	context = {}
	scene_titles = None
	if project_id:
		project = models.Product.objects.filter(pk=project_id)
		if request.user not in project[0].get_member_project_detail():
			return JsonResponse({}, status=500)
		if project.exists():
			project = project[0]
			product_scenes = project.scene_list
			count_product_scene = product_scenes.all().count()
			list_product_scene_ids = list(product_scenes.all().values_list("pk", flat=True))
			scene_titles = models.SceneTitle.objects.filter(
				product_scene__in=product_scenes.all()
			)
			list_scene_title_ids = list(scene_titles.all().values_list("pk", flat=True))
			count_processing = 0
			if request.user.role == "master_client":
				scene_titles = scene_titles.filter(status="3")
				count_processing = (
					models.Scene.objects.filter(title__in=scene_titles)
					.values("title_id")
					.annotate(num_of_scene=Count("scene_id"))
					.filter(num_of_scene__gt=0)
					.count()
				)
				if count_processing > 0:
					scene_titles = (
						scene_titles.select_related("last_version", "product_scene")
						.order_by("-updated_at")
						.prefetch_related(
							Prefetch(
								"scene_title",
								queryset=models.Scene.objects.filter(version=None)
								.order_by("-variation__order")
								.prefetch_related(
									Prefetch(
										"other_versions",
										queryset=models.Scene.objects.order_by(
											"-created"
										),
									)
								),
							),
							Prefetch(
								"owner_rating_title",
								queryset=models.RatingSceneTitle.objects.filter(
									user_id=request.user.id
								),
							),
							Prefetch("title_taken_scene__taken_scenes"),
						)
					)

			product_scenes = product_scenes.filter(pk__in=list_product_scene_ids).prefetch_related(
				Prefetch(
					"title_product_scene",
					queryset=SceneTitle.objects.filter(
						title_id__in=list_scene_title_ids
					)
					.select_related("last_version")
					.prefetch_related(
						Prefetch(
							"owner_rating_title",
							queryset=models.RatingSceneTitle.objects.filter(
								user_id=request.user.id
							),
						),
						Prefetch(
							"scene_title",
							queryset=models.Scene.objects.filter(version=None)
							.order_by("-variation__order")
							.prefetch_related(
								Prefetch(
									"other_versions",
									queryset=models.Scene.objects.order_by("-created"),
								)
							),
						),
						Prefetch("title_taken_scene__taken_scenes"),
					),
				)
			)

			can_share_link = project.share_link
			product_user = models.ProductUser.objects.get(
				user=request.user, product=project
			)
			view_only = product_user.view_only
			role = request.user.role
			if role != AuthUser.MASTERCLIENT:
				role = product_user.position
			if product_user.position == ProductUser.PRODUCER:
				role = AuthUser.CREATOR

			html = render_to_string(
				"top/_project_video_process_refactor.html",
				{
					"scene_titles": scene_titles,
					"count_processing": count_processing,
					"product_scenes": product_scenes,
					"is_done": False,
					"project": project,
					"role": role,
					"can_share_link": can_share_link,
					"view_only": view_only,
				},
			)
			context.update(
				{
					"html": html,
					"role": request.user.role,
					"current_load": 1,
					"list_product_scene_ids": list_product_scene_ids,
					"total_load": 0,
					"count_product_scene": count_product_scene,
				}
			)
			if variation_id != "-1":
				try:
					scene = models.Scene.original_objects.filter(
						pk=variation_id
					).first()
					scene_title = scene.title
					scene_title_id = scene_title.pk
					context.update({"scene_title_id": scene_title_id})
				except:
					pass
				return JsonResponse(context)

		return JsonResponse(context)


def get_project_update_video(request):
	project_id = request.GET.get("project_id")
	updated_scene_titles = None
	scene_titles = None
	comments = None
	product = Product.objects.get(pk=project_id)
	if request.user not in product.get_member_project_detail():
		return JsonResponse({}, status=500)
	product_scenes = product.scene_list.all()
	if request.user.role == "master_client":
		scene_titles = (
			models.SceneTitle.objects.exclude(status__in=["5", "6"])
			.filter(
				(Q(status__in=["1", "2"]) | Q(tag=True))
				& Q(product_scene__in=product_scenes)
			)
			.order_by("-tag", "status", "-updated_at")
		)
	else:
		scene_titles = models.SceneTitle.objects.filter(
			product_scene__in=product_scenes, status="3"
		).order_by("-updated_at")

	if scene_titles:
		scene_titles_ids = list(scene_titles.values_list("title_id", flat=True))
		preserved = Case(
			*[When(pk=pk, then=pos) for pos, pk in enumerate(scene_titles_ids)]
		)

		valid_scene_title_ids = (
			models.Scene.objects.filter(
				product_scene__isnull=False,
				version__isnull=True,
				title__in=scene_titles,
			)
			.order_by()
			.values_list("title_id", flat=True)
			.annotate(num_of_scene=Count("scene_id"))
			.filter(num_of_scene__gt=0)
		)
		if request.user.role == "master_client":
			valid_scene_title_ids = valid_scene_title_ids.filter(
				Q(schedule_date__lt=datetime.datetime.now()) & ~Q(movie="")
			)
		updated_scene_titles_ids = list(
			valid_scene_title_ids.values_list("title_id", flat=True)
		)
		updated_scene_titles = (
			scene_titles.filter(title_id__in=updated_scene_titles_ids)
			.order_by(preserved)
			.select_related("last_version", "product_scene")
			.prefetch_related(
				Prefetch(
					"scene_title",
					queryset=models.Scene.objects.filter(
						product_scene__isnull=False, version=None
					)
					.exclude(Q(movie="") | Q(movie__isnull=True))
					.order_by("-variation__order")
					.select_related("product_scene")
					.prefetch_related(
						Prefetch(
							"other_versions",
							queryset=models.Scene.objects.order_by("-created"),
						)
					),
				),
				Prefetch(
					"owner_rating_title",
					queryset=models.RatingSceneTitle.objects.filter(
						user_id=request.user.id
					),
				),
				Prefetch("title_taken_scene__taken_scenes"),
			)
		)

		comments = (
			models.SceneComment.objects.filter(
				~Q(user__role=request.user.role)
				& (
					Q(scene__title_id__in=updated_scene_titles_ids)
					| Q(scene_title_id__in=updated_scene_titles_ids)
				)
			)
			.select_related("user")
			.order_by("-created")
		)

	view_only = models.ProductUser.objects.get(
		user=request.user, product=product
	).view_only

	html = render_to_string(
		"top/_project_video_update_refactor.html",
		{
			"project": product,
			"is_done": False,
			"view_only": view_only,
			"role": request.user.role,
			"updated_scene_titles": updated_scene_titles,
			"comments": comments,
		},
	)

	variation_id = request.GET.get("variation_id", "-1")
	if variation_id != "-1":
		scene = models.Scene.original_objects.filter(pk=variation_id).first()
		scene_title = scene.title
		scene_title_id = scene_title.pk
		return JsonResponse(
			{"html": html, "scene_title_id": scene_title_id}, status=200
		)

	return JsonResponse({"html": html, "view_only": view_only}, status=200)


def get_project_finished_video(request):
	project_id = request.GET.get("project_id")
	product = Product.objects.get(pk=project_id)
	user = request.user
	product_user = models.ProductUser.objects.filter(
		product=product,
		user=request.user,
		position__in=[
			ProductUser.DIRECTOR,
			ProductUser.OWNER,
			ProductUser.REVIEWER,
			ProductUser.PRODUCER,
		],
	).first()

	if not product_user:
		return JsonResponse({}, status=500)

	scene_titles = GetSceneWattingsFeedbackService(user, product).process_refactor()

	view_only = models.ProductUser.objects.get(
		user=request.user, product=product
	).view_only

	html = render_to_string(
		"top/_project_video_finished_refactor.html",
		{
			"project": product,
			"is_done": False,
			"view_only": view_only,
			"role": request.user.role,
			"checking_scene_titles": scene_titles,
		},
	)

	return JsonResponse({"html": html}, status=200)


def get_scene_by_id(request):
	scene_id = request.GET.get("scene_id", None)
	template_name = "top/_carousel_all_scene.html"
	html = ""
	context = {}
	if scene_id:
		is_deleted = False
		scenes = models.Scene.objects.filter(pk=scene_id)
		if not scenes.exists():
			scenes = models.Scene.original_objects.filter(pk=scene_id)
			is_deleted = True
		if scenes.exists():
			scene_title = scenes[0].title
			product = scene_title.product_scene.product_scene.first()
			view_only = models.ProductUser.objects.get(
				user=request.user, product=product
			).view_only

			count_all_scenes = models.Scene.objects.filter(
				title_id=scene_title, product_scene__isnull=False, product__isnull=False
			).count()
			if view_only:
				can_share_link = False
			else:
				can_share_link = (
					scene_title.product_scene.product_scene.first().share_link
				)
			related_scenes = (
				models.Scene.original_objects.select_related("product_scene", "title")
				.filter(title_id=scenes.first().title_id, version__isnull=True)
				.order_by("order")
				.prefetch_related(
					Prefetch(
						"other_versions",
						queryset=models.Scene.original_objects.order_by(
							"version_order"
						),
					)
				)
			)
			selected_take = SceneTakenScenes.objects.filter(
				taken__title=scenes.first().title
			).values_list("scene_id", flat=True)
			is_bookmark = False
			if scene_title in request.user.titles.all():
				is_bookmark = True
			scene_share = scene_title.can_share
			selected_take_list = (
				[str(x) for x in selected_take] if selected_take else []
			)
			html = render_to_string(
				template_name,
				{
					"scenes": related_scenes,
					"scene_id": scene_id,
					"scene_share": scene_share,
					"scene": scenes.first(),
					"role": request.user.role,
					"can_share_link": can_share_link,
					"is_deleted": is_deleted,
					"user": request.user,
					"view_only": view_only,
					"count_all_scenes": count_all_scenes,
					"is_bookmark": is_bookmark,
					"selected_take": selected_take_list,
				},
			)

			variation_id = (
				models.Scene.original_objects.filter(
					title_id=scenes.first().title_id, version__isnull=True
				)
				.order_by("order")
				.first()
				.pk
			)
			context.update({"html": html, "variation_id": variation_id})

	return JsonResponse(context)

def update_content_comment(request):
	message_id = request.POST.get('message_id', None)
	message_content = request.POST.get('message_content', None)
	list_file_remove = request.POST.getlist('list_file_remove', None)
	list_file_id = request.POST.get('list_file_id', None)
	list_folder_id = request.POST.get('list_folder_id', None)
	type = request.POST.get('type', None)

	is_pc_device = True if request.user_agent.is_pc else False

	file_url = ''
	is_audio_file = ''
	name = ""
	real_name = ""
	parent_comments = []
	parentFile = []
	parentFolder = []
	listUserAvatar = []

	if message_id and message_content or message_id or len(list_file_remove) > 0 or len(list_file_id) > 0:
		try:
			if type == 'project':
				project_id = request.POST.get('project_id', None)
				project = Product.objects.get(pk=project_id)
				message = models.ProductComment.objects.get(pk=message_id)
				findMessage = models.ProductComment.objects.filter(pk__in=[message_id])
				listUserSeen = get_user_seen(message, "XXX")
				listExtraFileInfo = get_list_extra_file_info(type, findMessage)
				for userSeen in listUserSeen:
					listUserAvatar.append({"user_avatar": get_avatar(userSeen, "small")})
				parent_id = str((list(findMessage.values()))[0]["parent_id"])
				if(parent_id is not None and parent_id != "None"):
					parent_comments = models.ProductComment.objects.filter(pk__in=[parent_id])
					parentFile = list(models.ProductCommentFile.objects.filter(message__in=parent_comments).values())
					parentFolder = list(models.ProductCommentFolder.objects.filter(message__in=parent_comments).values())
					parent_comments = list(parent_comments.values())
					if len(parentFolder) > 0:
						name = parentFolder[0]["name"]
					elif len(parentFile) > 0:
						real_name = parentFile[0]["real_name"]

				message.comment = message_content

				if len(list_file_remove) > 0 and list_file_remove[0] != '':
					remove_id = list_file_remove[0].split(',')
					for file_id in remove_id:
						removeFilePerformance(file_id, 'scene_comment')
					models.ProductCommentFile.objects.filter(file_id__in=remove_id).update(message=None)
					if not message.files.exists():
						message.has_file = False
				message.save()
				if list_folder_id:
					tasks.update_folder(list_folder_id, "project", message.pk)
				if list_file_id:
					list_file_id = list_file_id.split(",")
					message.has_file = True
					message.save()
					list_files = models.ProductCommentFile.objects.filter(pk__in=list_file_id)
					for f in list_files:
						f.message = message
						f.save()

				list_user = project.get_member_project_detail()

				data = list()
				data.append(message)
				channel_layer = get_channel_layer()
				fileList = list(models.ProductCommentFile.objects.filter(message__in=findMessage).values())
				folders = list(models.ProductCommentFolder.objects.filter(message__in=findMessage).values())

				for file in fileList:
					if get_type_file(file["real_name"]):
						file["type_file_name"] = get_type_file(file["real_name"])

				for file in fileList:
					file["message_id"]= str(file["message_id"])
					file["file_id"] = str(file["file_id"])
					file['folder_id'] = str(file['folder_id'])
				for file in folders:
					file["message_id"] = str(file["message_id"])
					file["folder_id"] = str(file["folder_id"])
				new_message_offer = {
					'type': 'offer_message',
					'comment': serializers.serialize('json', data),
					"listUserAvatar": json.dumps(listUserAvatar, default=datetime_converter),
						"user_avatar": get_avatar(message.user, "medium"),
					'type_message': 'project',
					'message_id': str(message.pk),
					'sender_id': request.user.pk,
					'action': 'update_message',
					'message_content': message.comment,
					'file_url': file_url,
					'is_audio_file': is_audio_file,
					'has_file': message.has_file,
					"fileList": json.dumps(fileList, default=datetime_converter),
					"folders": json.dumps(folders, default=datetime_converter), 
					"is_pc_device": is_pc_device,
					"sender_role": request.user.role,
					"parent_comments": json.dumps(parent_comments, default=datetime_converter),
					"name": name,
					"real_name": real_name,
					"list_extra_file_info": json.dumps(listExtraFileInfo, default=datetime_converter),
				}
				for identity in list_user:
					new_message_offer['count_file_un_download'] = project.count_undownloaded_file_storage(identity)
					async_to_sync(channel_layer.group_send)(
						'{}'.format(identity.pk), new_message_offer)

				return JsonResponse({'success_message': "UPDATED"}, status=200)

			else:
				pin_time = request.POST.get('pin_time', None)
				scene_id = request.POST.get('scene_id', None)
				has_pin = request.POST.get('has_pin', None)
				scene_title_id = request.POST.get('scene_title_id', None)
				message = models.SceneComment.objects.get(pk=message_id)
				scene_title = models.SceneTitle.objects.get(pk=scene_title_id)

				if message.user == request.user:
					message.comment = message_content
					if has_pin == 'true':
						scene = models.Scene.objects.get(pk=scene_id)
						message.scene_title = None
						message.scene = scene
						message.pin_time = pin_time
						message.pin_video = scene.pk
					else:
						message.scene = None
						message.pin_time = None
						message.pin_video = None
						message.scene_title = scene_title

					if len(list_file_remove) > 0 and list_file_remove[0] != '':
						remove_id = list_file_remove[0].split(',')
						for file_id in remove_id:
							removeFilePerformance(file_id, 'scene_comment')
						models.SceneCommentFile.objects.filter(file_id__in=remove_id).update(message=None)
						if not message.files.exists():
							message.has_file = False
					message.save()

					if list_folder_id:
						tasks.update_folder(list_folder_id, "scene", message.pk)

					if list_file_id:
						list_file_id = list_file_id.split(",")
						message.has_file = True
						message.save()
						list_files = models.SceneCommentFile.objects.filter(pk__in=list_file_id)
						for f in list_files:
							f.message = message
							f.save()

					if scene_title_id:
						product = scene_title.product_scene.product_scene.first()

						list_user = product.get_member_project_detail()

						is_pc_device = True if request.user_agent.is_pc else False

						message_send_html = render_to_string('top/_item_message_send_2.html',
							{'message': message, "user": request.user, "type": 'scene_comment', "is_pc_device": is_pc_device})
						message_send_by_role_html = render_to_string('top/_item_message_send_2.html',
							{'message': message, "user": '', "type": 'scene_comment', "is_pc_device": is_pc_device})
						message_received_html = render_to_string('top/_item_message_received_2.html',
							{'message': message, "user": '',
								'sender': request.user,
								"type": 'scene_comment', "is_pc_device": is_pc_device})

						data = list()
						data.append(message)
						channel_layer = get_channel_layer()
						for identity in list_user:
							scene_update_comment = {
								'type': 'offer_message',
								'comment': serializers.serialize('json', data),
								'message_id': str(message.pk),
								'action': 'update_message',
								'sender_id': request.user.pk,
								'message_content': message.comment,
								'received_message_html': message_received_html,
								'send_message_html': message_send_html,
								'message_send_by_role_html': message_send_by_role_html,
								'file_url': file_url,
								'is_audio_file': is_audio_file,
								'has_file': message.has_file,
								'project_id': str(product.pk),
								'is_pc_device': is_pc_device
							}
							if message.has_file:
								scene_update_comment['file_infor_html'] = render_to_string(
									'top/_comment_file_infor.html',
									{'message': message, 'user': identity,
										'type_comment': 'scene_comment'})
							async_to_sync(channel_layer.group_send)(
								'{}'.format(identity.pk), scene_update_comment
							)
						send_message_update_scene(product)
					return JsonResponse({'success_message': "UPDATED"}, status=200)
		except:
			pass
		return JsonResponse({}, status=500)

def download_file_link(request):
	file_id = request.POST.get('file_id')
	offer_id = request.POST.get('offer_id')
	type_file = request.POST.get('type_file')
	type_infor = request.POST.get('type_infor')
	real_offer = None
	file = None
	if file_id:
		try:
			if type_file in ['message', 'message_owner'] and offer_id:
				real_offer = OfferProject.objects.filter(pk=offer_id).first()
			if real_offer and real_offer.offer_creator:
				if request.user.is_authenticated:
					file = models.MessageFile.objects.get(pk=file_id)
					download_product = models.DownloadedMessageFile.objects.filter(file=file, user=request.user).first()
					if not download_product:
						download_product = models.DownloadedMessageFile.objects.create(file=file, user=request.user)
						product = file.message.offer.project
						socketDownloadFile(product, request, file, 'message')
			elif real_offer and real_offer.offer_product:
				if request.user.is_authenticated:
					file = models.ProductMessageFile.objects.get(pk=file_id)
					download_product = models.DownloadedProductMessageFile.objects.filter(file=file, user=request.user).first()
					if not download_product:
						download_product = models.DownloadedProductMessageFile.objects.create(file=file, user=request.user)
						if file.message:
							product = file.message.offer.project
						else:
							product = file.offer.project
						socketDownloadFile(product, request, file, 'message_owner')
			elif type_file == 'comment':
				if request.POST.get('production_file', None) == 'production_file':
					scene_title = models.SceneTitle.original_objects.get(pk=file_id)
					file = scene_title.production_file
					if file:
						file_name = file.name
						url = get_download_link(file_name, scene_title.get_file_name())
						return JsonResponse({'url': url}, status=200)
				else:
					if type_infor == 'scene':
						file = models.Scene.original_objects.get(pk=file_id)
						if file.movie:
							file_name = file.movie.name
							url = get_download_link(file_name, file.get_real_movie_name())
							if request.user.is_authenticated:
								download_product = models.DownloadedScene.objects.filter(file=file, user=request.user).exists()
								if not download_product:
									models.DownloadedScene.objects.create(file=file, user=request.user)
									product = file.product
									socketDownloadFile(product, request, file, 'scene')
							return JsonResponse({'url': url}, status=200)
					else:
						file = models.SceneCommentFile.objects.get(pk=file_id)
						if request.user.is_authenticated:
							download_product = models.DownloadedSceneComment.objects.filter(file=file, user=request.user).exists()
							if not download_product:
								models.DownloadedSceneComment.objects.create(file=file, user=request.user)
								if file.message.scene:
									product = file.message.scene.product
								else:
									product = file.message.scene_title.product_scene.product_scene.first()
								socketDownloadFile(product, request, file, 'scene_comment')
			elif type_file == 'project':
				if request.user.is_authenticated:
					file = models.ProductCommentFile.objects.get(pk=file_id)
					download_product = models.DownloadedProductComment.objects.filter(file=file, user=request.user).exists()
					if not download_product:
						models.DownloadedProductComment.objects.create(file=file, user=request.user)
						product = file.message.project
						socketDownloadFile(product, request, file, 'project')
		except:
			pass

	if file and file.file:
		file_name = file.file.name
		url = get_download_link(file_name, file.real_name)
		return JsonResponse({'url': url}, status=200)

	return JsonResponse({}, status=500)

def socketDownloadFile(product, request, file, type):
	list_user = ProductUser.objects.filter(product=product,
		user__role__in=[AuthUser.CREATOR, AuthUser.MASTERADMIN, AuthUser.MASTERCLIENT],
		is_invited=False)
	if type == 'message_owner':
		list_user = ProductUser.objects.filter(product=product, 
			position__in=[ProductUser.MASTERADMIN, ProductUser.OWNER,ProductUser.PRODUCER],
			is_invited=False)
	avatar = get_avatar(request.user, "small")
	user_downloaded_html = render_to_string('top/_sview-user-seen.html',
		{'user_downloaded': request.user})
	context = {
		'type': 'offer_message',
		'action': 'download_file',
		'sender_id': request.user.pk,
		'project_id': str(product.pk),
		'file_id': str(file.pk),
		'avatar': avatar,
		'display_name': get_display_name(request.user),
	}

	context.update({'type_file_download': type,
		'type_message': type})
	if type in ['message', 'message_owner']:
		if file.message:
			offer = file.message.offer
		else:
			offer = file.offer
		context.update({'offer_id': str(offer.offer.pk)})
	if type == 'scene':
		scene_title = file.title
		context.update({'scene_title_id': str(scene_title.pk)})
	if type == 'scene_comment':
		if file.message.scene:
			scene_title = file.message.scene.title
		else:
			scene_title = file.message.scene_title
		context.update({'scene_title_id': str(scene_title.pk)})

	channel_layer = get_channel_layer()
	for identity in AuthUser.objects.filter(pk__in=list_user.values_list('user_id')):
		if type == 'project':
			context['count_file_un_download'] = product.count_undownloaded_file_storage(identity)
		async_to_sync(channel_layer.group_send)('{}'.format(identity.pk), context)
	return

def get_display_name(self):
	if self.role == AuthUser.MASTERCLIENT and self.display_name:
		return self.display_name
	if self.role == AuthUser.CREATOR:
		if self.stage_name:
			return self.stage_name
		if self.stage_name_en:
			return self.stage_name_en
	return self.fullname

def update_offer_review(request):
	get_offer_id = request.POST.get('offer_id', None)
	offer_id = get_offer_id.replace("-", "")

	review = request.POST.get('review', None)
	note = request.POST.get('note', None)
	status = request.POST.get("status", None)
	user_id = request.POST.get("user_id", None)
	real_offer = models.OfferProject.objects.filter(pk=offer_id).first()
	offer = real_offer.offer_creator
	channel_layer = get_channel_layer()
	if offer:
		list_user = offer.get_member_offer()
		list_ids = [user.id for user in list_user]
		different_ids = [id for id in list_ids if str(id) != str(user_id)]
		if offer_id and review != None and len(note) > 0 and len(note) <= 1500 and status and user_id:
			real_offer = OfferProject.objects.filter(pk=offer_id).first()
			if real_offer and real_offer.offer_creator:
				review_offer = ReviewOffer(
					user_id=user_id,
					review=review,
					note=note,
					offer_id=offer_id,
					status=status,
				)
				review_offer.save()
				new_offer_message = {
					'type': 'offer_message',
					'action': 'offer_message',
					'user_id': user_id,
					'review': review,
					'note': note,
					'offer_id': get_offer_id,
					'status': status,
				}
				for user in list_user:
					new_offer_message['position'] = get_postion(user_id)
					new_offer_message['full_name'] = get_display_fullname(user_id)
					new_offer_message['other_name'] = get_display_fullname(different_ids[0])
					async_to_sync(channel_layer.group_send)(
						'{}'.format(user.pk),
						new_offer_message,
					)
				return JsonResponse({'status': 'success'})
	return JsonResponse({'status': 'failed'})

def get_profile(request):
	user = request.user
	small_avatar = get_avatar(user, "small")
	medium_avatar = get_avatar(user, "medium")
	user_data = model_to_dict(user, fields=['id','is_superuser', 'username', 'last_name','first_name','email','is_active', 'is_staff', 'role', 'is_verify', 'position'])
	profile_link = user.get_link_profile()
	creator_id = user.get_creator_id()
	user_data['small_avatar'] = small_avatar
	user_data['medium_avatar'] = medium_avatar
	user_data['profile_link'] = profile_link
	user_data['creator_id'] = creator_id
	return JsonResponse({'status': 200, 'result': user_data})

def remove_excluded_project(projects, current_user, ip):
	# Get IDs of projects to be excluded based on IP check and user role.
	exclude_project_ids = (
		ProductUser.objects.filter(product__in=projects, user=current_user)
		.filter(Q(list_ip__isnull=False) & ~Q(list_ip__contains=ip), position=ProductUser.REVIEWER)
		.values_list('product_id', flat=True)
	)

	# Exclude those projects and prefetch related data.
	filtered_projects = projects.exclude(pk__in=exclude_project_ids).prefetch_related(
		Prefetch(
			'productuser_set',
			queryset=ProductUser.objects.filter(
				is_invited=False,
				user__role=AuthUser.MASTERCLIENT,
				user__is_active=True
			).select_related('user')
		)
	)

	return filtered_projects

def check_project_batch(projects, user):
	# Filter projects that have a batch number greater than 0 for the user
	project_with_batch = [
		project.pk for project in projects
		if count_batch_number_project(project, user) > 0
	]

	# If there's exactly one project with a batch, return True with its primary key
	if len(project_with_batch) == 1:
		return True, str(project_with_batch[0])

	# Return False if there are no projects or more than one with a batch
	return False, ''

class ProjectListView(APIView):
	renderer_classes = [JSONRenderer]
	authentication_classes = [TokenAuthentication, SessionAuthentication]	# Allow both authentication types
	def get(self, request):
		# Extract parameters from the request
		PROJECTS_PER_PAGE = 10
		current_user = request.user
		ip = request.META.get('REMOTE_ADDR')
		page = int(request.GET.get('page', 1))
		projects_per_page = int(request.GET.get('projects_per_page', PROJECTS_PER_PAGE))

		# Get order parameters if they exist
		order_by = request.GET.get("order_by", None)
		project_status = request.GET.get("project_status", None)
		order_type = request.GET.get('order_type', '')

		# Get all active projects the user is related to
		product_users = current_user.productuser_set.filter(is_invited=False)
		products = current_user.products.filter(is_active=True, pk__in=product_users.values_list('product_id'))

		# Apply project status filter
		if project_status == 'undone':
			products = products.exclude(max_scene__lte=F('current_heart'))

		# Order the projects if order_by is provided

		if order_by in ['modified', 'created', 'rating']:
			order = f"{order_type}{order_by}" # Build the order string
			products = products.order_by(order)

		if order_by == 'order':
			order = f"{'-' if order_type == 'desc' else ''}order" # Assuming 'order' is a valid field
			product_ids = list(product_users.order_by(order).values_list('product_id', flat=True))
			preserved = Case(*[When(pk=pk, then=pos) for pos, pk in enumerate(product_ids)])
			products = products.order_by(preserved)

		# Remove excluded projects based on user's IP
		removed_projects = remove_excluded_project(products, current_user, ip)

		# Create a paginator object for the projects
		paginator = Paginator(removed_projects, projects_per_page)
		total_page = paginator.num_pages

		# Get the projects for the current page
		projects = paginator.get_page(page)

		# Serialize the projects
		serializer = ProductSerializer(projects, many=True, context={'user': current_user})

		# Determine whether to go to profile or project detail based on project count
		go_to_profile = False
		go_to_project_detail = False
		go_to_project_detail_pk = ''
		project_count = removed_projects.count()

		force = request.GET.get('force', 'false').lower() == 'true'
		if project_count == 0 and current_user.role == 'admin' and not force:
			go_to_profile = True
		elif project_count == 1 and current_user.role in ['admin', 'master_client'] and not force:
			go_to_project_detail = True
			go_to_project_detail_pk = str(removed_projects.first().pk)
		elif current_user.role in ['admin', 'master_client'] and project_count > 1 and not force:
			go_to_project_detail, go_to_project_detail_pk = check_project_batch(removed_projects, current_user)

		# Prepare the response data
		response_data = {
			'projects': serializer.data,
			'current_page': page,
			'projects_per_page': projects_per_page,
			'total_page': total_page,
			'go_to_profile': go_to_profile,
			'go_to_project_detail': go_to_project_detail,
			'go_to_project_detail_pk': go_to_project_detail_pk,
		}

		return Response(response_data, status=status.HTTP_200_OK)

class ProfileApiView(APIView):
	authentication_classes = [TokenAuthentication, SessionAuthentication]	# Allow both authentication types
	permission_classes = [IsAuthenticated] # Require authentication

	def get(self, request):
		user = request.user
		small_avatar = get_avatar(user, "small")
		medium_avatar = get_avatar(user, "medium")
		user_data = model_to_dict(user, fields=['id','is_superuser', 'username', 'last_name','first_name','email','is_active', 'is_staff', 'role', 'is_verify', 'position'])
		profile_link = user.get_link_profile()
		creator_id = user.get_creator_id()
		user_data['small_avatar'] = small_avatar
		user_data['medium_avatar'] = medium_avatar
		user_data['profile_link'] = profile_link
		user_data['creator_id'] = creator_id
		return Response({'status': 200, 'result': user_data})
	
class ProjectDetailView(APIView):
	renderer_classes = [JSONRenderer]

	def get(self, request):
		# Extract project_id from the request body
		project_id = request.GET.get('project_id')

		# Validate that project_id is provided
		if not project_id:
			return Response({"detail": "Project ID is required."}, status=status.HTTP_400_BAD_REQUEST)

		# Try to fetch the project by ID
		try:
			project = Product.objects.get(pk=project_id, is_active=True)	# Ensure the project is active
			serializer = ProductSerializer(project, context={'user': request.user})
			serializer_data = serializer.data
			owner_list = get_owners_project(project)
			owners_info = [
				{
					'id': owner.pk,
					'display_name': owner.get_display_name(),
					'avatar': get_avatar(owner,'small'),
					'position': owner.position,
					'enterprise': owner.enterprise
				}
				for owner in owner_list
			]
			admin_list = project.get_director_producer_master_admin_in_project()
			admins_info = [
				{
					'id': admin.pk,
					'display_name': admin.get_display_name(),
					'avatar': get_avatar(admin,'small'),
					'position': admin.position,
					'enterprise': admin.enterprise
				}
				for admin in admin_list
			]
			member_list = project.get_creator_in_project()
			member_invited = [
				{
					'id': member.pk,
					'display_name': member.get_display_name(),
					'avatar': get_avatar(member,'small'),
					'position': member.position,
					'enterprise': member.enterprise
				}
				for member in member_list
			]

			product_scenes = project.scene_list.all()
			product_scene_list = [
				{
					'id': product_scene.pk,
					'name': product_scene.name
				}
				for product_scene in product_scenes
			]

			# Get updated scene titles based on user role and project details
			scene_titles = None
			comments = None
			if request.user.role == "master_client":
				scene_titles = models.SceneTitle.objects.exclude(status__in=["5", "6"]).filter(
					(Q(status__in=["1", "2"]) | Q(tag=True)) & Q(product_scene__in=product_scenes)
				).order_by("-tag", "status", "-updated_at")
			else:
				scene_titles = models.SceneTitle.objects.filter(
					product_scene__in=product_scenes, status="3"
				).order_by("-updated_at")
			# Extract and order updated scene titles
			updated_scene_titles = []
			if scene_titles:
				scene_titles_ids = list(scene_titles.values_list("title_id", flat=True))
				preserved = Case(
					*[When(pk=pk, then=pos) for pos, pk in enumerate(scene_titles_ids)]
				)

				valid_scene_title_ids = models.Scene.objects.filter(
					product_scene__isnull=False,
					version__isnull=True,
					title__in=scene_titles
				).order_by().values_list("title_id", flat=True).annotate(num_of_scene=Count("scene_id")).filter(num_of_scene__gt=0)

				if request.user.role == "master_client":
					valid_scene_title_ids = valid_scene_title_ids.filter(
						Q(schedule_date__lt=datetime.datetime.now()) & ~Q(movie="")
					)

				updated_scene_titles_ids = list(valid_scene_title_ids.values_list("title_id", flat=True))
				updated_scene_titles_queryset = scene_titles.filter(
					title_id__in=updated_scene_titles_ids
				).order_by(preserved).select_related("last_version", "product_scene").prefetch_related(
					Prefetch(
						"scene_title",
						queryset=models.Scene.objects.filter(
							product_scene__isnull=False, version=None
						).exclude(Q(movie="") | Q(movie__isnull=True)).order_by("-variation__order").select_related("product_scene").prefetch_related(
							Prefetch(
								"other_versions",
								queryset=models.Scene.objects.order_by("-created")
							)
						)
					)
				)

				comments = (
					models.SceneComment.objects.filter(
						~Q(user__role=request.user.role)
						& (
							Q(scene__title_id__in=updated_scene_titles_ids)
							| Q(scene_title_id__in=updated_scene_titles_ids)
						)
					)
					.select_related("user")
					.order_by("-created")
				)

				#TODO: Still need to apply folder and file logic here
				updated_scene_titles = [
					{
						"id": scene_title.pk,
						"name": scene_title.title,
						"status": scene_title.status,
						"rating": get_rating(scene_title,request.user.role),
						"thumbnail": get_thumbnail(scene_title.get_last_version()),
						"updated_at": scene_title.updated_at,
						"scene_id": scene_title.get_last_version().pk,
						"movie": scene_title.get_last_version().movie.url if scene_title.get_last_version().movie else None,
						"scene_real_name": scene_title.get_last_version().real_name,
						"last_comment": [{
							'avatar': get_avatar(get_last_message_title_refactor(scene_title,comments).user,'small'),
							'comment': get_last_message_title_refactor(scene_title,comments).comment
						}] if get_last_message_title_refactor(scene_title,comments) else None
					}
					for scene_title in updated_scene_titles_queryset
				]

			# Get finished scenes
			finished_scenes_queryset = models.SceneTitle.objects.filter(
				product_scene__in=product_scenes, status__in=["5", "6"]
			).order_by("-updated_at")

			finished_scenes = [
				{
					"id": scene.pk,
					"name": scene.title,
					"status": scene.status,
					"rating": get_rating(scene, request.user.role),
					"thumbnail": get_thumbnail(scene.get_last_version()),
					"updated_at": scene.updated_at,
					"scene_id": scene.get_last_version().pk,
					"product_scene_id": scene.product_scene.pk,
					"is_audio_file": scene.get_last_version().is_audio_file(),
					"schedule_date": scene.get_last_version().schedule_date,
					"is_out_of_date": get_schedule_out_of_date(scene.get_last_version()),
					"is_out_of_date_st": get_schedule_out_of_date(scene,'last-update'),
					"movie_name": scene.get_last_version().movie.name if scene.get_last_version().movie else None,
					"movie": scene.get_last_version().movie.url if scene.get_last_version().movie else None,
					"scene_real_name": scene.get_last_version().real_name,
				}
				for scene in finished_scenes_queryset
			]

			serializer_data['owners'] = owners_info
			serializer_data['admins'] = admins_info
			serializer_data['members'] = member_invited
			serializer_data['product_scene_list'] = product_scene_list
			serializer_data['updated_scene_titles'] = updated_scene_titles
			serializer_data['finished_scene'] = finished_scenes

			first_offer = project.offer_product.first().offer
			serializer_data['first_offer_id'] = first_offer.pk
			return Response(serializer_data, status=status.HTTP_200_OK)
		except Product.DoesNotExist:
			return Response({"detail": "Project not found."}, status=status.HTTP_404_NOT_FOUND)


class ProductSceneApiView(APIView):
	authentication_classes = [TokenAuthentication, SessionAuthentication]
	permission_classes = [IsAuthenticated]

	def post(self, request, *args, **kwargs):
		# Step 1: Extract POST data
		try:
			count_list_file = int(request.data.get('count_list_file', 0))
			title = request.data.get('title', None)
			version = request.data.get('version', None)
			product_scene_id = request.data.get('product_scene_id', None)
			product_scene_text = request.data.get('product_scene_val', None)
			product_id = request.data.get('product', None)
			schedule_date_str = request.data.get('schedule_date', None)

			# Parse the schedule_date
			if schedule_date_str:
				schedule_date = datetime.datetime.strptime(schedule_date_str, '%Y/%m/%d - %H:%M')
			else:
				return JsonResponse({'error': 'Missing schedule_date'}, status=400)

			# Step 2: Process the video files
			movies = []
			for idx in range(count_list_file):
				# Assuming each "movies" file field is an array of files
				movies.append(request.FILES.getlist(f'movies_{idx}'))

			# Extract variation values
			variation_values = request.data.get('variation_values', "").split("!||!")
			variation_values = [val.split('|') for val in variation_values]

			# Step 3: Check if the product and product scene exist
			try:
				product = models.Product.objects.get(pk=product_id)
				product_scene = product.scene_list.filter(pk=product_scene_id).first()
				if not product_scene and product_scene_text:
					# Create new ProductScene if not exists
					order = 0
					if product.scene_list.exists():
						order = product.scene_list.order_by('order').last().order + 1
										
					product_scene = models.ProductScene.objects.create(
						name=product_scene_text.strip(),
						order=order
					)
					product.scene_list.add(product_scene)
			except models.Product.DoesNotExist:
				return JsonResponse({'error': 'Product not found'}, status=404)

			# Step 4: Handle Scene Title
			is_new = False
			cscene_html = ''
			if title:
				title = title.strip()
				if title == '':
					return JsonResponse({'error': 'Title cannot be empty'}, status=400)

			# Create or fetch SceneTitle
			title_obj, created = models.SceneTitle.objects.get_or_create(
				title=title,
				product_scene=product_scene
			)
			if created:
				is_new = True

			title_obj.updated_at = datetime.datetime.now()
			title_obj.admin_updated_at = datetime.datetime.now()
			title_obj.save()

			# Step 5: Create Scenes for the uploaded videos
			first_scene_ids = []
			try:
				scene_order = 0
				if version:
					scene_version = models.Scene.objects.get(pk=version)
				else:
					scene_version = None

				for idx, movie_group in enumerate(movies):
					list_height = request.data.get('list_height', '').split('!||!')
					list_width = request.data.get('list_width', '').split('!||!')
					scene_thumbnail = None

					# Handle movie processing
					for index1, movie in enumerate(movie_group):
						movie_height = list_height[idx].split('|')[index1]
						movie_width = list_width[idx].split('|')[index1]
												
					# Handle thumbnail (Base64 decoding if needed)
					if not scene_thumbnail:
						scene_thumbnail_base64 = request.data.get('thumbnail_base_64', None)
						if scene_thumbnail_base64:
							scene_thumbnail = self.img_base64_to_file(scene_thumbnail_base64)

					# Create the scene object
					scene = models.Scene.objects.create(
						title=title_obj,
						movie=movie,
						product=product,
						video_height=movie_height,
						video_width=movie_width,
						owner_id=request.user.pk,
						product_scene=product_scene,
						thumbnail=scene_thumbnail,
						order=scene_order,
						version=scene_version,
						schedule_date=schedule_date
					)

					first_scene_ids.append(str(scene.pk))
					scene_order += 1

				# Step 6: Return response with scene details
				cscene_html = render_to_string(
					'top/_cscene.html',
					{'st': title_obj, 'view_only': False, 'scene': title_obj.last_version, 'role': 'admin', 'is_done': False}
				)

				return JsonResponse({
					'project_id': product_id,
					'cscene_html': cscene_html,
					'product_scene_id': str(product_scene.pk),
					'is_new': is_new,
					'schedule_date': schedule_date.strftime('%Y/%m/%d - %H:%M')
				})

			except Exception as e:
				# Log exception here
				return JsonResponse({'error': f'Failed to process scene: {str(e)}'}, status=500)

		except Exception as e:
			return JsonResponse({'error': f'Invalid data: {str(e)}'}, status=400)

	def img_base64_to_file(self, file_base64):
		"""Convert a base64-encoded string to an in-memory image file."""
		
		# Find the start of the base64 string (after the data:image/jpeg;base64, part)
		idx = file_base64.find(',')

		if idx == -1 or not file_base64.startswith('data:image/'):
			raise Exception("Invalid base64 string or missing data:image prefix")

		# Extract the base64 string after the comma (this is the actual base64 content)
		base64file = file_base64[idx + 1:]

		# Extract the file's content type (e.g., image/png, image/jpeg)
		attributes = file_base64[:idx]
		content_type = attributes[len('data:'):attributes.find(';')]

		# Decode the base64 string into bytes and create a BytesIO stream for the image
		try:
			file_data = base64.b64decode(base64file)
		except Exception as e:
			raise Exception(f"Error decoding base64 string: {str(e)}")

		# Create an in-memory file object from the decoded data
		file_thumbnail = io.BytesIO(file_data)
		
		# Create an InMemoryUploadedFile object to return
		image = InMemoryUploadedFile(
			file_thumbnail,	# The actual image file
			'FileField',	# The field type (could be any file field)
			name='picture_' + get_random_string(15) + '.png',	# Default filename (you could modify this as needed)
			content_type=content_type,	# MIME type of the image
			size=sys.getsizeof(file_thumbnail),	# Size of the image data
			charset=None	# No charset for binary data
		)
		
		return image

class ProjectCommentView(APIView):
	renderer_classes = [JSONRenderer]

	def get(self, request):
		comment_type = request.GET.get("type", None)
		scene_id = request.GET.get("scene_id", None)
		project_id = request.GET.get("project_id", None)

		try:
			if comment_type == "scene":
				is_deleted = False
				try:
					scene = models.Scene.objects.get(pk=scene_id)
				except:
					scene = models.Scene.original_objects.get(pk=scene_id)
					is_deleted = True
				scene_title = scene.title
				if scene_title:
					scenes = models.Scene.objects.filter(
						title_id=scene.title_id,
						product_scene__isnull=False,
						product__isnull=False,
					).order_by("-modified")
					parent_comments = (
						models.SceneComment.objects.filter(
							Q(scene_id__in=scenes.values_list("scene_id", flat=True))
							| Q(scene_title_id=scene_title.pk),
							parent__isnull=True,
						)
						.order_by("created")
						.select_related("user")
						.prefetch_related(
							Prefetch(
								"child_comment",
								queryset=models.SceneComment.objects.order_by(
									"created"
								).prefetch_related(
									Prefetch(
										"preview_comment",
										queryset=models.PreviewScene.objects.exclude(
											owner_id=request.user.id
										).order_by("-created"),
									)
								),
							)
						)
						.prefetch_related(
							Prefetch(
								"preview_comment",
								queryset=models.PreviewScene.objects.exclude(
									owner_id=request.user.id
								).order_by("-created"),
							)
						)
					)

					if scene_title.status in ["5", "6"] or is_deleted:
						view_only = True
					else:
						view_only = (
							request.user.role == "master_client"
							and ProductUser.objects.filter(
								user=request.user, product=scene.product, view_only=True
							).exists()
						)

					html = render_to_string(
						"top/_comment_container_new_refactor.html",
						{
							"obj": scene,
							"user": request.user,
							"parent_comments": parent_comments,
							"view_only": view_only,
							"type": "scene",
						},
					)
					return JsonResponse({"html": html})
			elif comment_type == "project":
				product = models.Product.objects.get(pk=project_id)
				product_user = ProductUser.objects.filter(
					user=request.user, product=product
				).first()
				if product_user.position not in [
					ProductUser.DIRECTOR,
					ProductUser.OWNER,
					ProductUser.REVIEWER,
					ProductUser.PRODUCER,
				]:
					return JsonResponse({"status": "404"})
				view_only = (
					request.user.role == AuthUser.MASTERCLIENT
					and product_user.view_only == True
				)

				list_count = models.ProductComment.objects.filter(project=product).count()
				total_page = (
					list_count / MESSAGE_PER_PAGE
					if list_count % MESSAGE_PER_PAGE == 0
					else int(list_count / MESSAGE_PER_PAGE) + 1
				)
				comments = getAllComments(product)
				commentFiles = models.ProductCommentFile.objects.filter(
					message__in=comments
				).order_by("-created")
				comment_files = list(commentFiles.values())
				# Add MediaConvert data to files
				comment_files = add_mediaconvert_data_to_files(comment_files)
				for file in comment_files:
					if get_type_file(file["real_name"]):
						file["type_file_name"] = get_type_file(file["real_name"])
				comment_folders = list(models.ProductCommentFolder.objects.filter(
					message__in=comments
				).order_by("-created").values())

				listExtraFileInfo = getUrlFile(comment_type, commentFiles)
				filterComment = list(comments.values())[-MESSAGE_PER_PAGE:]
				listSameRole = list_check_is_same_role(
					request.user.role, get_comments(product)
				)
				rawArray = list(map(lambda x: x["parent_id"], filterComment))
				listParentComment = [i for i in rawArray if i is not None]

				newParentComemnts = models.ProductComment.objects.filter(pk__in=listParentComment)
				parentComment = list(newParentComemnts.values())
				dictFolder = {str(item["message_id"]): {"folder_id": str(item["folder_id"]),"name": str(item["name"])} for item in comment_folders}
				dictFile = {str(item["message_id"]): {"file_id": str(item["file_id"]),"name": str(item["real_name"])} for item in comment_files}
				for item in parentComment:
					messageId = str(item["comment_id"])
					if str(item["comment_id"]) in dictFolder:
						fileExist = dictFolder.get(messageId)
						item["folder_id"] = fileExist["folder_id"]
						item["name"] = fileExist["name"]
					elif str(item["comment_id"]) in dictFile:
						fileExist = dictFile.get(messageId)
						item["file_id"] = fileExist["file_id"]
						item["name"] = fileExist["name"]
				is_seen = models.SceneCommentReceiver.objects.filter(
					product_comment__project=product,
					seen_date__isnull=True,
					user=request.user,
				).exists()
				is_pc_device = True if request.user_agent.is_pc else False
				return JsonResponse(
					{
						"total_page": total_page,
						"list_extra_file_info": listExtraFileInfo,
						"list_same_role": listSameRole,
						"comments": filterComment,
						"is_seen": is_seen,
						"view_only": view_only,
						"type": "product",
						"is_pc_device": is_pc_device,
						"folder": comment_folders,
						"comment_files": comment_files,
						"current_user_id": request.user.id,
						"current_user_role": request.user.role,
						"parent_comments": parentComment,
						"dictFile": dictFile
					}
				)
			return JsonResponse({"html": html})
		except:
			pass
		return JsonResponse({"status": "404"})

class ProjectUpdatedCountApiView(APIView):
	authentication_classes = [TokenAuthentication, SessionAuthentication]
	permission_classes = [IsAuthenticated]

	def get(self, request):
		project_id = request.GET.get("project_id", None)
		try:
			project = Product.objects.get(pk=project_id)
			user = request.user

			return Response({
				'updated_sence_title': count_new_update_scene_title(project, user),
				'undownload_product_comment': count_undownload_product_comment(project, user),
				'unread_message': count_unread_message(project, user)
			}, status=status.HTTP_200_OK)
		except:
			return Response({
				'updated_sence_title': 0,
				'undownload_product_comment': 0,
				'unread_message': 0
			}, status=status.HTTP_200_OK)

class ProjectMembersApiView(APIView):
	authentication_classes = [TokenAuthentication, SessionAuthentication]
	permission_classes = [IsAuthenticated]

	def get(self, request):
		try:
			current_user = request.user

			if request.method == "GET":
				product_id = request.GET.get('project_id')
			else:
				product_id = request.POST.get('project_id')
			product = Product.objects.get(pk=product_id)

			# ログインしている権限を判定するフラグ
			login_producer = product.productuser_set.filter(user=current_user, position=ProductUser.PRODUCER).exists()
			login_masteradmin = product.productuser_set.filter(user=current_user, position=ProductUser.MASTERADMIN).exists()
			login_director = product.productuser_set.filter(user=current_user, position=ProductUser.DIRECTOR).exists()
			login_masterclient = product.productuser_set.filter(user=current_user, position=ProductUser.OWNER).exists()
			login_creator = product.productuser_set.filter(user=current_user, position=ProductUser.STAFF).exists()
			login_client = product.productuser_set.filter(user=current_user, position=ProductUser.REVIEWER).exists()

			master_admin_users = product.productuser_set.filter(position=ProductUser.MASTERADMIN).exclude(user=current_user).select_related('user')
			master_admin_users_list = [pu.user for pu in master_admin_users]
			owners = None
			pu_owners = product.productuser_set.filter(position=ProductUser.OWNER,
													user__is_active=True)
			if pu_owners:
				owner_ids = list(pu_owners.order_by('order_user').values_list('user__pk', flat=True))
				preserved_owner = Case(*[When(pk=pk, then=pos) for pos, pk in enumerate(owner_ids)])
				owners = product.authuser_set.filter(pk__in=owner_ids).order_by(preserved_owner)
			admins = None
			producers = None
			check_master_client = current_user.role
			pu_producers = None
			editable = False
			if current_user in product.get_member_manage_project_detail():
				editable = True
			can_drag = request.user in product.get_member_offer_project()
			members_invited = None
			members_inviting = None
			project_members = product.authuser_set.filter(role='master_client').exists()
			product_current_user = product.productuser_set.filter(user=current_user).first()

			if project_members:
				pu_invited = product.productuser_set.filter(position=ProductUser.REVIEWER,
															is_invited=False, user__is_active=True)
				user_invited_ids = list(pu_invited.order_by('order_user').values_list('user__pk', flat=True))
				preserved_invited = Case(*[When(pk=pk, then=pos) for pos, pk in enumerate(user_invited_ids)])
				members_invited = product.authuser_set.filter(pk__in=user_invited_ids).order_by(preserved_invited)

				pus_inviting = product.productuser_set.filter((Q(position=ProductUser.REVIEWER) &
															Q(is_invited=True)) | \
															(Q(user__role=AuthUser.MASTERCLIENT) &
															Q(position=ProductUser.OWNER) &
															Q(user__is_active=False) &
															Q(user__is_verify=False) &
															Q(user__last_login__isnull=True)))
				members_inviting = []
				
				if pus_inviting.exists():
					offer = product.offer_product.first()
					for pu in pus_inviting:
						member = pu.user
						if pu.position == ProductUser.OWNER:
							encoded_token = jwt.encode(
								{'member_pk': member.pk, 'product_pk': product_id, 'offer_pk': str(offer.pk)},
								SECRET,
								algorithm='HS256'
							)
						else:
							encoded_token = jwt.encode(
								{'member_pk': member.pk, 'product_pk': product_id},
								SECRET,
								algorithm='HS256'
							)
						member.jwt_token = encoded_token
						member.signature = encoded_token.split('.')[-1]
						members_inviting.append(member)

				if members_invited.exists():
					for member in members_invited:
						encoded_token = jwt.encode(
							{'member_pk': member.pk, 'product_pk': product_id},
							SECRET,
							algorithm='HS256'
						)
						member.jwt_token = encoded_token
						member.signature = encoded_token.split('.')[-1]
			if check_master_client and check_master_client == AuthUser.MASTERCLIENT:
				pu_producers = product.productuser_set.filterpu_producers = product.productuser_set.filter(Q(user__is_active=True) & Q(position=ProductUser.PRODUCER))

			else:
				pu_producers = product.productuser_set.filter(position__in=[ProductUser.PRODUCER],
															user__is_active=True)
			if pu_producers:
				producer_ids = list(pu_producers.order_by('order_user').values_list('user__pk', flat=True))
				preserved_producer = Case(*[When(pk=pk, then=pos) for pos, pk in enumerate(producer_ids)])
				producers = product.authuser_set.filter(pk__in=producer_ids).order_by(preserved_producer)

			pu_directors = product.productuser_set.filter(position=ProductUser.DIRECTOR, user__is_active=True)
			if pu_directors:
				director_ids = list(pu_directors.order_by('order_user').values_list('user__pk', flat=True))
				preserved_director = Case(*[When(pk=pk, then=pos) for pos, pk in enumerate(director_ids)])
				admins = product.authuser_set.filter(pk__in=director_ids).order_by(preserved_director)

			editable = True if current_user in product.get_manage_director_in_project() else False
			editable_all = True if current_user in product.get_master_admin_master_producer_in_project() else False
			project = Product.objects.filter(pk=product_id).first()
			project_user = ProductUser.objects.filter(user=current_user, product=project).first()

			online_users_set = set()
			get_online_user(members_invited, online_users_set)
			get_online_user(owners, online_users_set)
			get_online_user(admins, online_users_set)
			get_online_user(producers, online_users_set)

			online_users = list(online_users_set)
			return JsonResponse({
				'owners': list(owners.values()) if owners else None,
				'admins': list(admins.values()) if admins else None,
				'producers': list(producers.values()) if producers else None,
				'user': AuthUserSerializer(current_user).data,
				'editable_all': editable_all,
				'product': ProductSerializer(product).data,
				'editable': editable,
				'can_drag': can_drag,
				'members_invited': list(members_invited.values()) if members_invited else None,
				'members_inviting': AuthUserSerializer(members_inviting, many=True).data,
				'product_id': product_id,
				'project_user': ProductUserSerializer(project_user).data,
				'online_users': AuthUserSerializer(online_users, many=True).data,
				'login_producer': login_producer,
				'login_masteradmin': login_masteradmin,
				'login_director': login_director,
				'login_masterclient': login_masterclient,
				'login_creator': login_creator,
				'login_client': login_client,
				'masters': AuthUserSerializer(master_admin_users_list, many=True).data,
				'product_current_user': ProductUserSerializer(product_current_user).data
			})
		except:
			return Response(status = "404")

class ProjectMessengerArtistApiView(APIView):
	authentication_classes = [TokenAuthentication, SessionAuthentication]
	permission_classes = [IsAuthenticated]

	def get(self, request):
		try:
			product_id = request.GET.get("project_id")
			offer_id = request.GET.get("offer_active")
			filter_offer = request.GET.get("filter_offer")
			user_id = request.GET.get("user_id")
			context = {}
			user = request.user
			project = Product.objects.get(pk=product_id)
			project_user = ProductUser.objects.get(user=user, product=project)
			type_model = ""
			if project_user.position in [
				ProductUser.DIRECTOR,
				ProductUser.STAFF,
				ProductUser.MASTERADMIN,
			]:
				type_model = "messenger"
			elif project_user.position in [ProductUser.OWNER]:
				type_model = "messenger_owner"
			if project_user:
				if not filter_offer or filter_offer == "search":
					try:
						waiting_offer = get_offers_in_project_refactor(
							project, user, project_user, "waiting", type_model
						).exists()
						processing_offer = get_offers_in_project_refactor(
							project, user, project_user, "processing", type_model
						).exists()
						if filter_offer == "search":
							waiting_offer = False
							processing_offer = False
						if waiting_offer:
							filter_offer = "waiting"
						elif processing_offer:
							filter_offer = "processing"
						elif user.role != AuthUser.MASTERCLIENT:
							context.update({"page": "search"})
							context = {}
							form = SearchCreatorForm()
							offer_form = OfferCreatorForm()
							role_creators = Creator.ROLE_CREATOR
							is_done_project = False
							list_value = get_value_exists(project, request.user)

							list_scenes = list_value[0]
							list_quantity = list_value[1]
							list_data_format = list_value[2]

							context.update(
								{
									"project": project,
									"form": form,
									"offer_form": offer_form,
									"role_creators": role_creators,
									"is_done_project": is_done_project,
									"skills": get_all_skill(),
									"list_scenes": list_scenes,
									"list_quantity": list_quantity,
									"list_data_format": list_data_format,
									"page": "search",
								}
							)

					except:
						pass

				offers = get_offers_in_project_refactor(
					project, user, project_user, filter_offer, type_model
				)
				offers = sort_offers(offers, user)
				offers = offers.distinct().select_related(
					"project", "offer_creator", "offer_product"
				)
				offer_ids = offers.values_list("offer_id", flat=True)
				offer_users = OfferUser.objects.filter(user=user, offer__in=offer_ids).order_by(
					"pk"
				)
				prefetch_offer_users = Prefetch(
					"offeruser_set", queryset=offer_users, to_attr="prefetched_offer_users"
				)
				same_offer_users = (
					OfferUser.objects.filter(offer__in=offer_ids, user__is_active=True)
					.select_related("user")
					.order_by("pk")
				)
				prefetch_same_offer_users = Prefetch(
					"offeruser_set",
					queryset=same_offer_users,
					to_attr="prefetched_same_offer_users",
				)
				offers = offers.prefetch_related(
					prefetch_offer_users, prefetch_same_offer_users
				)

				unreadMap = dict(OfferMessageReceiver.count_unread_message(user_id, product_id))
				when_list = [When(offer_id=offer_id, then=Value(unread_count)) for offer_id, unread_count in unreadMap.items()]
				offers = offers.annotate(
					unread_message_count=Case(
						*when_list,
						default=Value(0), 
						output_field=IntegerField(),
					)
				)

				form_contract_and_plan, current_product_message_file = (
					get_current_form_contract_and_plan_service(project)
				)
				context.update(
					{
						"project": project,
						"offers": offers,
						"user": request.user,
						"offer_status": filter_offer,
						"project_user": project_user,
						"owner": project.get_owner(),
						"form_contract_and_plan": form_contract_and_plan,
						"product_message_file": current_product_message_file,
					}
				)
				isMasterAdmin = False
				if project_user.position in [
					ProductUser.MASTERADMIN,
				]:
					isMasterAdmin = True
				response = {
					"project": ProductSerializer(project).data,
					# "form": context["form"] if "form" in context else None,
					# "offer_form": context["offer_form"] if "offer_form" in context else None,
					"role_creators": context["role_creators"] if "role_creators" in context else None,
					"is_done_project": context["is_done_project"] if "is_done_project" in context else None,
					"skills": list(get_all_skill()),
					"list_scenes": context["list_scenes"] if "list_scenes" in context else None,
					"list_quantity": context["list_quantity"] if "list_quantity" in context else None,
					"list_data_format": context["list_data_format"] if "list_data_format" in context else None,
					"page": context["page"] if "page" in context else None,
					"offers": OfferProjectSerializer(context["offers"], many=True).data if "offers" in context else None,
					"user": AuthUserSerializer(request.user).data,
					"project_user": ProductUserSerializer(context["project_user"]).data if "project_user" in context else None,
					"owner": AuthUserSerializer(project.get_owner()).data,
					# "form_contract_and_plan": context["form_contract_and_plan"] if "form_contract_and_plan" in context else None,
					"product_message_file": context["product_message_file"] if "product_message_file" in context else None,
					"offer_status": filter_offer,
					"offer_active": offer_id,
					'isMasterAdmin': isMasterAdmin,
					"count_offer": len(offers)
				}

				list_scenes = get_list_name_to_search(project, project_user, user)
				response.update({"list_scenes": list_scenes})

				return JsonResponse(response, status=200)
			return JsonResponse({}, status=200)
		except:
			return Response(status = "404")

class ProjectUpdateVideo(APIView):
	authentication_classes = [TokenAuthentication, SessionAuthentication]
	permission_classes = [IsAuthenticated]

	def get(self, request):
		project_id = request.GET.get("project_id")
		updated_scene_titles = None
		scene_titles = None
		comments = None
		product = Product.objects.get(pk=project_id)
		if request.user not in product.get_member_project_detail():
			return JsonResponse({}, status=500)
		product_scenes = product.scene_list.all()
		if request.user.role == "master_client":
			scene_titles = (
				models.SceneTitle.objects.exclude(status__in=["5", "6"])
				.filter(
					(Q(status__in=["1", "2"]) | Q(tag=True))
					& Q(product_scene__in=product_scenes)
				)
				.order_by("-tag", "status", "-updated_at")
			)
		else:
			scene_titles = models.SceneTitle.objects.filter(
				product_scene__in=product_scenes, status="3"
			).order_by("-updated_at")

		if scene_titles:
			scene_titles_ids = list(scene_titles.values_list("title_id", flat=True))
			preserved = Case(
				*[When(pk=pk, then=pos) for pos, pk in enumerate(scene_titles_ids)]
			)

			valid_scene_title_ids = (
				models.Scene.objects.filter(
					product_scene__isnull=False,
					version__isnull=True,
					title__in=scene_titles,
				)
				.order_by()
				.values_list("title_id", flat=True)
				.annotate(num_of_scene=Count("scene_id"))
				.filter(num_of_scene__gt=0)
			)
			if request.user.role == "master_client":
				valid_scene_title_ids = valid_scene_title_ids.filter(
					Q(schedule_date__lt=datetime.datetime.now()) & ~Q(movie="")
				)
			updated_scene_titles_ids = list(
				valid_scene_title_ids.values_list("title_id", flat=True)
			)
			updated_scene_titles = (
				scene_titles.filter(title_id__in=updated_scene_titles_ids)
				.order_by(preserved)
				.select_related("last_version", "product_scene")
				.prefetch_related(
					Prefetch(
						"scene_title",
						queryset=models.Scene.objects.filter(
							product_scene__isnull=False, version=None
						)
						.exclude(Q(movie="") | Q(movie__isnull=True))
						.order_by("-variation__order")
						.select_related("product_scene")
						.prefetch_related(
							Prefetch(
								"other_versions",
								queryset=models.Scene.objects.order_by("-created"),
							)
						),
					),
					Prefetch(
						"owner_rating_title",
						queryset=models.RatingSceneTitle.objects.filter(
							user_id=request.user.id
						),
					),
					Prefetch("title_taken_scene__taken_scenes"),
				)
			)

			comments = (
				models.SceneComment.objects.filter(
					~Q(user__role=request.user.role)
					& (
						Q(scene__title_id__in=updated_scene_titles_ids)
						| Q(scene_title_id__in=updated_scene_titles_ids)
					)
				)
				.select_related("user")
				.order_by("-created")
			)

		view_only = models.ProductUser.objects.get(
			user=request.user, product=product
		).view_only

		variation_id = request.GET.get("variation_id", "-1")
		scene_title_id = None
		if variation_id != "-1":
			scene = models.Scene.original_objects.filter(pk=variation_id).first()
			scene_title = scene.title
			scene_title_id = scene_title.pk

		return JsonResponse({
			"project": ProductSerializer(product).data,
			"is_done": False,
			"view_only": view_only,
			"role": request.user.role,
			"updated_scene_titles": SceneTitleSerializer(updated_scene_titles, many=True).data,
			"comments": SceneCommentSerializer(comments, many=True).data,
			"scene_title_id": scene_title_id
		}, status=200)
	
class ProjectProcessVideo(APIView):
	authentication_classes = [TokenAuthentication, SessionAuthentication]
	permission_classes = [IsAuthenticated]

	def get(self, request):
		project_id = request.GET.get("project_id", None)
		variation_id = request.GET.get("variation_id", None)
		context = {}
		scene_titles = None
		if project_id:
			project = models.Product.objects.filter(pk=project_id)
			if request.user not in project[0].get_member_project_detail():
				return JsonResponse({}, status=500)
			if project.exists():
				project = project[0]
				product_scenes = project.scene_list
				count_product_scene = product_scenes.all().count()
				list_product_scene_ids = list(product_scenes.all().values_list("pk", flat=True))
				scene_titles = models.SceneTitle.objects.filter(
					product_scene__in=product_scenes.all()
				)
				list_scene_title_ids = list(scene_titles.all().values_list("pk", flat=True))
				count_processing = 0
				if request.user.role == "master_client":
					scene_titles = scene_titles.filter(status="3")
					count_processing = (
						models.Scene.objects.filter(title__in=scene_titles)
						.values("title_id")
						.annotate(num_of_scene=Count("scene_id"))
						.filter(num_of_scene__gt=0)
						.count()
					)
					if count_processing > 0:
						scene_titles = (
							scene_titles.select_related("last_version", "product_scene")
							.order_by("-updated_at")
							.prefetch_related(
								Prefetch(
									"scene_title",
									queryset=models.Scene.objects.filter(version=None)
									.order_by("-variation__order")
									.prefetch_related(
										Prefetch(
											"other_versions",
											queryset=models.Scene.objects.order_by(
												"-created"
											),
										)
									),
								),
								Prefetch(
									"owner_rating_title",
									queryset=models.RatingSceneTitle.objects.filter(
										user_id=request.user.id
									),
								),
								Prefetch("title_taken_scene__taken_scenes"),
							)
						)

				product_scenes = product_scenes.filter(pk__in=list_product_scene_ids).prefetch_related(
					Prefetch(
						"title_product_scene",
						queryset=SceneTitle.objects.filter(
							title_id__in=list_scene_title_ids
						)
						.select_related("last_version")
						.prefetch_related(
							Prefetch(
								"owner_rating_title",
								queryset=models.RatingSceneTitle.objects.filter(
									user_id=request.user.id
								),
							),
							Prefetch(
								"scene_title",
								queryset=models.Scene.objects.filter(version=None)
								.order_by("-variation__order")
								.prefetch_related(
									Prefetch(
										"other_versions",
										queryset=models.Scene.objects.order_by("-created"),
									)
								),
							),
							Prefetch("title_taken_scene__taken_scenes"),
						),
					)
				)

				can_share_link = project.share_link
				product_user = models.ProductUser.objects.get(
					user=request.user, product=project
				)
				view_only = product_user.view_only
				role = request.user.role
				if role != AuthUser.MASTERCLIENT:
					role = product_user.position
				if product_user.position == ProductUser.PRODUCER:
					role = AuthUser.CREATOR

				context.update(
					{
						"scene_titles": SceneTitleSerializer(scene_titles, many=True).data,
						"count_processing": count_processing,
						"product_scenes": ProductSceneSerializer(product_scenes, many=True).data,
						"is_done": False,
						"project": ProductSerializer(project).data,
						"role": role,
						"can_share_link": can_share_link,
						"view_only": view_only,
						"role": request.user.role,
						"current_load": 1,
						"list_product_scene_ids": list_product_scene_ids,
						"total_load": 0,
						"count_product_scene": count_product_scene,
					}
				)
				if variation_id != "-1":
					try:
						scene = models.Scene.original_objects.filter(
							pk=variation_id
						).first()
						scene_title = scene.title
						scene_title_id = scene_title.pk
						context.update({"scene_title_id": scene_title_id})
					except:
						pass
					return JsonResponse(context)

			return JsonResponse(context)

class ProjectFinishedVideo(APIView):
	authentication_classes = [TokenAuthentication, SessionAuthentication]
	permission_classes = [IsAuthenticated]

	def get(self, request):
		project_id = request.GET.get("project_id")
		product = Product.objects.get(pk=project_id)
		user = request.user
		product_user = models.ProductUser.objects.filter(
			product=product,
			user=request.user,
			position__in=[
				ProductUser.DIRECTOR,
				ProductUser.OWNER,
				ProductUser.REVIEWER,
				ProductUser.PRODUCER,
			],
		).first()

		if not product_user:
			return JsonResponse({}, status=500)

		scene_titles = GetSceneWattingsFeedbackService(user, product).process_refactor()

		view_only = models.ProductUser.objects.get(
			user=request.user, product=product
		).view_only

		return JsonResponse({
			"project": ProductSerializer(product).data,
			"is_done": False,
			"view_only": view_only,
			"role": request.user.role,
			"checking_scene_titles": SceneTitleSerializer(scene_titles, many=True).data,
		}, status=200)

class ProjectStaffCredit(APIView):
	authentication_classes = [TokenAuthentication, SessionAuthentication]
	permission_classes = [IsAuthenticated]

	def get(self, request):
		project_id = request.GET.get('project_id')
		if not project_id:
			return JsonResponse({}, status=500)
		project = Product.objects.filter(pk=project_id).first()
		if not project:
			return JsonResponse({}, status=500)
		current_user = request.user
		editable = False
		if current_user.role == AuthUser.MASTERADMIN or ProductUser.objects.filter(user=current_user, product=project,
																				position=ProductUser.PRODUCER).exists():
			editable = True

		language = request.GET.get('language', 'jp')

		sections = project.sections.order_by('order')
		items = project.items_staff_credit.order_by('order')

		from itertools import chain
		linked_list = chain(sections, items)
		sorted_list = sorted(linked_list, key=lambda item: item.order)
		
		return JsonResponse({
			'project': ProductSerializer(project).data, 
			'user': AuthUserSerializer(current_user).data, 
			'editable': editable,
			'language': language, 
			'sorted_list': SectionCreditSerializer(sorted_list, many=True).data
		}, status=200)

class MessageFolderItemsApiView(APIView):
	authentication_classes = [TokenAuthentication, SessionAuthentication]
	permission_classes = [IsAuthenticated]

	def get(self, request):
		type_message = request.GET.get('type_message', None)
		folder_id = request.GET.get('folder_id', None)
		offer_id = request.GET.get('offer_id', None)
		self.is_pc_device = True if request.user_agent.is_pc else False

		if folder_id:
			if type_message == 'project':
				folder = models.ProductCommentFolder.objects.get(pk=folder_id)
			elif type_message == 'scene':
				folder = models.SceneCommentFolder.objects.get(pk=folder_id)
			elif type_message in ['message_artist', 'message_owner'] and offer_id:
				real_offer = OfferProject.objects.filter(pk=offer_id).first()
				folder = models.MessageFolder.objects.get(
					pk=folder_id) if real_offer.offer_creator else models.ProductMessageFolder.objects.get(pk=folder_id)
			if folder:
				while folder:
					if folder.parent:
						folder = folder.parent
					else:
						break
			
				return JsonResponse({
					'type': 'folder',
					'id': folder.pk,
					'name': folder.name,
					'child': self.list_files_in_folder(folder, type_message)
				})
		return JsonResponse(status=400)
	
	def list_files_in_folder(self, folder, type_message):
		res = []
		for sub_folder in folder.child_folders.all():
			res.append({
				'type': 'folder',
				'id': sub_folder.pk,
				'name': sub_folder.name,
				'child': self.list_files_in_folder(sub_folder, type_message)
			})

		files = list(folder.children.all())
		# Add MediaConvert data to files
		files = add_mediaconvert_data_to_files(files)
		
		for file in files:
			user_dowloadeds = get_list_user_download(file, type_message)
			res_user_downloadeds = []
			if user_dowloadeds:
				for user_downloaded in user_dowloadeds:
					res_user_downloadeds.append({
						'name': user_downloaded.get_display_name(),
						'avatar': get_avatar(user_downloaded, 'small')
					})

			all_list_user_download = count_list_user_download(file, type_message)
			max_loop = show_user_seen_max(self.is_pc_device)
			user_count = minus(function_count(all_list_user_download), max_loop)
			
			file_data = {
				'type': 'file',
				'id': file.pk,
				'name': file.real_name,
				'is_audio_file': file.is_audio_file(),
				'acr_status': file.acr_status,
				'user_downloadeds': res_user_downloadeds,
				'user_count': user_count,
			}
			
			# Add converted file info if available
			if hasattr(file, 'converted_file'):
				file_data['converted_file'] = file.converted_file
				
			res.append(file_data)

		return res

class MessengerArtistView(APIView):
	renderer_classes = [JSONRenderer]	# This ensures the response is rendered as JSON
	
	def get(self, request):
		# Extract parameters from the GET request
		product_id = request.GET.get("project_id")
		offer_id = request.GET.get("offer_active")
		filter_offer = request.GET.get("filter_offer")
		user_id = request.GET.get("user_id")
		
		if not product_id or not user_id:
			return Response({"error": "Missing project_id or user_id"}, status=status.HTTP_400_BAD_REQUEST)
		
		try:
			user = request.user
			project = Product.objects.get(pk=product_id)
			project_user = ProductUser.objects.get(user=user, product=project)
		except Product.DoesNotExist:
			return Response({"error": "Product not found"}, status=status.HTTP_404_NOT_FOUND)
		except ProductUser.DoesNotExist:
			return Response({"error": "User not associated with product"}, status=status.HTTP_404_NOT_FOUND)
		
		context = {}
		
		type_model = ""
		offer_class = ""

		# Determine the type of model and offer class based on user's position
		if project_user.position in [ProductUser.DIRECTOR, ProductUser.STAFF, ProductUser.MASTERADMIN]:
			type_model = "messenger"
			offer_class = OfferCreator
		elif project_user.position == ProductUser.OWNER:
			type_model = "messenger_owner"
			offer_class = OfferProduct

		if project_user:
			if not filter_offer or filter_offer == "search":
				try:
					# Check if there are any waiting or processing offers
					waiting_offer = get_offers_in_project_refactor(
						project, user, project_user, "waiting", type_model
					).exists()
					processing_offer = get_offers_in_project_refactor(
						project, user, project_user, "processing", type_model
					).exists()
					
					if filter_offer == "search":
						waiting_offer = False
						processing_offer = False
										
					if waiting_offer:
						filter_offer = "waiting"
					elif processing_offer:
						filter_offer = "processing"
				except Exception as e:
					return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

			# Fetch the offers based on the filter
			offers = get_offers_in_project_refactor(
				project, user, project_user, filter_offer, type_model
			)
			offers = sort_offers(offers, user)
			offers = offers.distinct().select_related("project", "offer_creator", "offer_product")
			offer_ids = offers.values_list("offer_id", flat=True)
			
			offer_users = OfferUser.objects.filter(user=user, offer__in=offer_ids).order_by("pk")
			prefetch_offer_users = Prefetch("offeruser_set", queryset=offer_users, to_attr="prefetched_offer_users")

			same_offer_users = (
				OfferUser.objects.filter(offer__in=offer_ids, user__is_active=True)
				.select_related("user")
				.order_by("pk")
			)
			prefetch_same_offer_users = Prefetch(
				"offeruser_set",
				queryset=same_offer_users,
				to_attr="prefetched_same_offer_users",
			)

			offers = offers.prefetch_related(
				prefetch_offer_users, prefetch_same_offer_users
			)

			# Get unread message count for each offer
			unreadMap = dict(OfferMessageReceiver.count_unread_message(user_id, product_id))
			when_list = [When(offer_id=offer_id, then=Value(unread_count)) for offer_id, unread_count in unreadMap.items()]
			offers = offers.annotate(
				unread_message_count=Case(
					*when_list,
					default=Value(0), 
					output_field=IntegerField(),
				)
			)

			# Additional information
			form_contract_and_plan, current_product_message_file = get_current_form_contract_and_plan_service(project)
			
			# context.update({
			# 	"project": project,
			# 	"offers": offers,
			# 	"user": request.user,
			# 	"offer_status": filter_offer,
			# 	"project_user": project_user,
			# 	"owner": project.get_owner(),
			# 	"form_contract_and_plan": form_contract_and_plan,
			# 	"product_message_file": current_product_message_file,
			# })

			# Add scene list for searching
			list_scenes = get_list_name_to_search(project, project_user, user)
			# context["list_scenes"] = list_scenes

			# Determine if the user is a Master Admin
			isMasterAdmin = project_user.position == ProductUser.MASTERADMIN
			# response_data = {
			# 	"offer_status": filter_offer,
			# 	"offer_active": offer_id,
			# 	"isMasterAdmin": isMasterAdmin,
			# 	"count_offer": len(offers),
			# 	"list_scenes": list_scenes,
			# }
			# response_data.update(context)
			response_data = {
				"list_offer": OfferProjectScheduleSerializer(offers, many=True).data,
			}

			return Response(response_data, status=status.HTTP_200_OK)

		return Response({"error": "User not associated with the project"}, status=status.HTTP_403_FORBIDDEN)

class OfferMessageApiView(APIView):
	authentication_classes = [TokenAuthentication, SessionAuthentication]  # Allow both authentication types
	permission_classes = [IsAuthenticated]  # Require authentication

	def get(self, request):
		offer_id = request.GET.get("offer_id")
		user = request.user
		if not offer_id:
			return JsonResponse({"html": "", "status": "failed"})

		real_offer = OfferProject.objects.filter(pk=offer_id).first()
		if not real_offer or real_offer.offer_creator and real_offer.offer_product:
			return JsonResponse({"html": "", "status": "failed"})

		offer_user = OfferUser.objects.filter(user=request.user, offer=real_offer).first()
		if not offer_user:
			return JsonResponse({"html": "", "status": "failed"})

		if real_offer.offer_creator:
			offer = real_offer.offer_creator
			target = models.OfferProject.objects.filter(pk=offer.pk).first()

			if not target:
				return JsonResponse({"html": "", "status": "failed"})

			receiver = target.admin if request.user != target.admin else target.creator
			thread_name = receiver.get_display_name()
			is_seen = not target.check_offer_is_unread(request.user)

			message_files = target.message_offer.filter(has_file=True)
			file_messages = (
				models.MessageFile.objects.filter(
					message__in=message_files, folder__isnull=True
				)
				.select_related("message", "message__offer", "message__user")
				.prefetch_related(
					Prefetch(
						"user_downloaded",
						queryset=models.DownloadedMessageFile.objects.select_related("user"),
					)
				)
				.order_by("-created")
			)
			folder_messages = (
				models.MessageFolder.objects.filter(
					message__in=message_files, parent__isnull=True
				)
				.select_related("message", "message__offer", "message__user")
				.prefetch_related(
					Prefetch(
						"child_folders",
						queryset=models.MessageFolder.objects.prefetch_related(
							Prefetch(
								"child_folders", queryset=models.MessageFolder.objects.all()
							)
						),
					),
					Prefetch(
						"children",
						queryset=models.MessageFile.objects.prefetch_related(
							Prefetch(
								"user_downloaded",
								queryset=models.DownloadedMessageFile.objects.select_related("user"),
							)
						),
					),
				)
				.order_by("-created")
			)

			dict_files = {}
			for file in file_messages:
				dict_files[file.pk] = {"object": file, "modified": file.modified}

			for folder in folder_messages:
				dict_files[folder.pk] = {"object": folder, "modified": folder.modified}

			dict_files = {
				k: v
				for k, v in sorted(
					dict_files.items(), key=lambda item: item[1]["modified"], reverse=True
				)
			}

			list_count = target.message_offer.count()
			total_page = (list_count // MESSAGE_PER_PAGE) + (1 if list_count % MESSAGE_PER_PAGE != 0 else 0)
			id_review_offer = [offer.admin.pk, offer.creator.pk]
			messages = get_messages(target.offer)
			prev_messages = messages
			next_messages = None
			list_messages_afterreview = []
			firstSystem = None
			list_review = ReviewOffer.objects.filter(offer_id=offer_id.replace("-", ""))
			user_ids = [int(review.user_id) for review in list_review]
			if offer.accept_time:
				firstSystem = messages.filter(Q(type_message='2')).first()
				prev_messages = messages.filter(
					Q(created__lt=offer.accept_time)
				).union(messages.filter(pk=firstSystem.pk) if firstSystem else None)
				next_messages = messages.filter(
					Q(created__gt=offer.accept_time)
				).exclude(pk=firstSystem.pk if firstSystem else None)
				if len(list_review) > 0:
					for review in list_review:
						filter_message = next_messages.filter(created__lt=review.created)
						next_messages = next_messages.exclude(created__lt=review.created)
						list_messages_afterreview.append(filter_message)
					list_messages_afterreview.append(next_messages.filter(created__gt=list_review[len(list_review) - 1].created))
				else:
					list_messages_afterreview.append(next_messages)

			# context = {
			# 	"offer": target,
			# 	"real_offer": real_offer,
			# 	"user": request.user,
			# 	"is_seen": is_seen,
			# 	"request": request,
			# 	"thread_name": thread_name,
			# 	"receiver": receiver,
			# 	"message_file": message_files,
			# 	"dict_files": dict_files,
			# 	"type_comment": "messenger",
			# 	"prev_messages": prev_messages,
			# 	"next_messages": next_messages,
			# 	"offer_user": offer_user,
			# 	"messages": messages,
			# 	"offer_creator": offer,
			# 	"user_ids": user_ids,
			# 	"id_review_offer": id_review_offer,
			# 	"list_messages_afterreview": list_messages_afterreview,
			# 	"list_review": list_review,
			# }
   
			messages_data = [
				{
					"id": message.pk,
					"content": message.content,
					"comment": message.comment,
					"type_message": message.type_message,
					# "offer_user": get_offer_user_by_message(message) if message.user and message.offer else None,
					"created": message.created,
					"is_seen": True if message.seen_date else False
				}
				for message in messages
			]
   
			commentFiles = models.ProductMessageFile.objects.filter(
				message__in=messages
			).order_by("-created")
			comment_files = list(commentFiles.values())
			# Add MediaConvert data to files
			comment_files = add_mediaconvert_data_to_files(comment_files)
			for file in comment_files:
				if get_type_file(file["real_name"]):
					file["type_file_name"] = get_type_file(file["real_name"])
			comment_folders = list(models.ProductMessageFolder.objects.filter(
				message__in=messages
			).order_by("-created").values())

			listExtraFileInfo = getUrlFile("messenger_owner", commentFiles)
			rawArray = list(map(lambda x: x.parent_id, messages))
			listParentComment = [i for i in rawArray if i is not None]

			newParentComemnts = models.ProductMessage.objects.filter(pk__in=listParentComment)
			parentComment = list(newParentComemnts.values())
			dictFolder = {str(item["message_id"]): {"folder_id": str(item["folder_id"]),"name": str(item["name"])} for item in comment_folders}
			dictFile = {str(item["message_id"]): {"file_id": str(item["file_id"]),"name": str(item["real_name"])} for item in comment_files}
			for item in parentComment:
				messageId = str(item["comment_id"])
				if str(item["comment_id"]) in dictFolder:
					fileExist = dictFolder.get(messageId)
					item["folder_id"] = fileExist["folder_id"]
					item["name"] = fileExist["name"]
				elif str(item["comment_id"]) in dictFile:
					fileExist = dictFile.get(messageId)
					item["file_id"] = fileExist["file_id"]
					item["name"] = fileExist["name"]

			output_data = {
				"total_page": total_page,
				"list_extra_file_info": listExtraFileInfo,
				"is_seen": is_seen,
				"view_only": False,
				"comments": messages_data,
    		"type_comment": "messenger_owner",
				"offer_user": offer_user.position,
    		"folder": comment_folders,
				"comment_files": comment_files,
				"current_user_id": request.user.id,
				"current_user_role": request.user.role,
				"parent_comments": parentComment,
				"dictFile": dictFile
			}
   
			return Response(output_data, status=status.HTTP_200_OK)

		else:
			offer = real_offer.offer_product
			target = models.OfferProduct.objects.filter(pk=offer.pk).first()

			if not target:
				return JsonResponse({"html": "", "status": "failed"})

			thread_name = target.project.get_name_by_contract()
			is_seen = not target.check_offer_is_unread(request.user)
			message_files = target.message_product.filter()
			file_messages = (
				models.ProductMessageFile.objects.filter(
					(
						Q(message__in=message_files)
						| Q(offer=target, type_file=models.ProductMessageFile.OFFER)
					)
					& Q(folder__isnull=True)
				)
				.select_related("message", "message__offer", "message__user")
				.prefetch_related(
					Prefetch(
						"user_downloaded",
						queryset=models.DownloadedProductMessageFile.objects.select_related("user"),
					)
				)
				.order_by("-created")
			)
			folder_messages = (
				models.ProductMessageFolder.objects.filter(
					(
						Q(message__in=message_files)
						| Q(offer=target, type_file=models.ProductMessageFolder.OFFER)
					)
					& Q(parent__isnull=True)
				)
				.select_related("message", "message__offer", "message__user")
				.prefetch_related(
					Prefetch(
						"child_folders",
						queryset=models.ProductMessageFolder.objects.prefetch_related(
							Prefetch(
								"child_folders",
								queryset=models.ProductMessageFolder.objects.all(),
							)
						),
					),
					Prefetch(
						"children",
						queryset=models.ProductMessageFile.objects.prefetch_related(
							Prefetch(
								"user_downloaded",
								queryset=models.DownloadedProductMessageFile.objects.select_related("user"),
							)
						),
					),
				)
				.order_by("-created")
			)

			dict_files = {}
			for file in file_messages:
				dict_files[file.pk] = {"object": file, "modified": file.modified}

			for folder in folder_messages:
				dict_files[folder.pk] = {"object": folder, "modified": folder.modified}

			dict_files = {
				k: v
				for k, v in sorted(
					dict_files.items(), key=lambda item: item[1]["modified"], reverse=True
				)
			}

			list_count = target.message_product.count()
			total_page = (list_count // MESSAGE_PER_PAGE) + (1 if list_count % MESSAGE_PER_PAGE != 0 else 0)
			messages = get_messages(target.offer)
			# is_pc_device = request.user_agent.is_pc

			# context = {
			# 	"offer": target,
			# 	"real_offer": real_offer,
			# 	"user": request.user,
			# 	"is_seen": is_seen,
			# 	"request": request,
			# 	"thread_name": thread_name,
			# 	"dict_files": dict_files,
			# 	"type_comment": "messenger_owner",
			# 	"offer_user": offer_user,
			# 	"messages": messages,
			# 	"is_pc_device": is_pc_device,
			# }

			# context_data = {}
			# if target.condition in OfferProduct.STATUS_SHOW_MENU:
			# 	file_contract = target.get_contract_in_offer()
			# 	if file_contract:
			# 		context_data["file_contract"] = str(file_contract.pk)

			# if target.condition == OfferProduct.STATUS_PAYMENTED or target.receipt_id:
			# 	file_bill = target.get_bill_in_offer()
			# 	if file_bill:
			# 		context_data["file_bill"] = str(file_bill.pk)

			# context_data.update(
			# 	{
			# 		"html": html,
			# 		"infor_html": infor_html,
			# 		"status": "success",
			# 		"total_page": total_page,
			# 	}
			# )

			messages_data = [
				{
					"id": message.pk,
					"content": message.content,
					"comment": message.comment,
					"type_message": message.type_message,
					"offer_user": get_offer_user_by_message(message) if message.user and message.offer else None,
					"created": message.created,
					"is_seen": True if message.seen_date else False
				}
				for message in messages
			]
   
			commentFiles = models.ProductMessageFile.objects.filter(
				message__in=messages
			).order_by("-created")
			comment_files = list(commentFiles.values())
			# Add MediaConvert data to files
			comment_files = add_mediaconvert_data_to_files(comment_files)
			for file in comment_files:
				if get_type_file(file["real_name"]):
					file["type_file_name"] = get_type_file(file["real_name"])
			comment_folders = list(models.ProductMessageFolder.objects.filter(
				message__in=messages
			).order_by("-created").values())

			listExtraFileInfo = getUrlFile("messenger_owner", commentFiles)
			rawArray = list(map(lambda x: x.parent_id, messages))
			listParentComment = [i for i in rawArray if i is not None]

			newParentComemnts = models.ProductMessage.objects.filter(pk__in=listParentComment)
			parentComment = list(newParentComemnts.values())
			dictFolder = {str(item["message_id"]): {"folder_id": str(item["folder_id"]),"name": str(item["name"])} for item in comment_folders}
			dictFile = {str(item["message_id"]): {"file_id": str(item["file_id"]),"name": str(item["real_name"])} for item in comment_files}
			for item in parentComment:
				messageId = str(item["comment_id"])
				if str(item["comment_id"]) in dictFolder:
					fileExist = dictFolder.get(messageId)
					item["folder_id"] = fileExist["folder_id"]
					item["name"] = fileExist["name"]
				elif str(item["comment_id"]) in dictFile:
					fileExist = dictFile.get(messageId)
					item["file_id"] = fileExist["file_id"]
					item["name"] = fileExist["name"]

			output_data = {
				"total_page": total_page,
				"list_extra_file_info": listExtraFileInfo,
				"is_seen": is_seen,
				"view_only": False,
				"comments": messages_data,
    		"type_comment": "messenger_owner",
				"offer_user": offer_user.position,
    		"folder": comment_folders,
				"comment_files": comment_files,
				"current_user_id": request.user.id,
				"current_user_role": request.user.role,
				"parent_comments": parentComment,
				"dictFile": dictFile
			}
   
			return Response(output_data, status=status.HTTP_200_OK)


class ActiveProjectsApiView(APIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [IsAuthenticated]
    renderer_classes = [JSONRenderer]

    def get(self, request):
        # ソート順のパラメータを取得（デフォルトは最終更新日の降順）
        sort_by = request.GET.get('sort_by', 'last_update')
        order = request.GET.get('order', 'desc')
        
        # 許可されたソートフィールド
        allowed_sort_fields = ['last_update', 'created', 'name', 'rating']
        if sort_by not in allowed_sort_fields:
            sort_by = 'last_update'
        
        # ソート順の構築
        order_prefix = '-' if order == 'desc' else ''
        order_by = f'{order_prefix}{sort_by}'
        
        # 認証ユーザーに関連するプロジェクトを直接取得
        projects = Product.objects.filter(
            productuser__user=request.user,
            productuser__is_active=True,
            is_active=True
        ).exclude(
            max_scene=F('current_heart')  # max_sceneとcurrent_heartが等しくない
        ).distinct().order_by(order_by)

        # シリアライザーを使用してデータを変換
        serializer = ProjectsSerializer(projects, many=True)

        return Response({
            'status': 'success',
            'data': serializer.data
        })

class CompletedProjectsApiView(APIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [IsAuthenticated]
    renderer_classes = [JSONRenderer]

    def get(self, request):
        # ソート順のパラメータを取得（デフォルトは最終更新日の降順）
        sort_by = request.GET.get('sort_by', 'last_update')
        order = request.GET.get('order', 'desc')
        
        # 許可されたソートフィールド
        allowed_sort_fields = ['last_update', 'created', 'name', 'rating']
        if sort_by not in allowed_sort_fields:
            sort_by = 'last_update'
        
        # ソート順の構築
        order_prefix = '-' if order == 'desc' else ''
        order_by = f'{order_prefix}{sort_by}'
        
        # 認証ユーザーに関連するプロジェクトを直接取得
        # max_sceneとcurrent_heartが等しいプロジェクトのみ取得（完了したプロジェクト）
        projects = Product.objects.filter(
            productuser__user=request.user,
            productuser__is_active=True,
            is_active=True,
            max_scene=F('current_heart')  # max_sceneとcurrent_heartが等しい
        ).distinct().order_by(order_by)

        # シリアライザーを使用してデータを変換
        serializer = ProjectsSerializer(projects, many=True)

        return Response({
            'status': 'success',
            'data': serializer.data
        })

def add_mediaconvert_data_to_files(files):
	"""
	Helper function to add MediaConvert data to file lists
	"""
	if not files:
		return files

	# Get file paths for MediaConvert lookup
	file_paths = []
	for file in files:
		if isinstance(file, dict):
			file_path = file.get('file', '')
		else:
			file_path = getattr(file, 'file', '')
		if file_path:
			file_paths.append(str(file_path))

	if not file_paths:
		return files

	# Query MediaConvert jobs for these files
	mediaconvert_jobs = models.MediaConvertJob.objects.filter(
		original_object_key__in=file_paths
	).values('original_object_key', 'converted_media_key', 'status')

	# Create a lookup dictionary for better performance
	mediaconvert_lookup = {job['original_object_key']: job for job in mediaconvert_jobs}

	# Add converted file info to each file
	for file in files:
		file_path = file.get('file', '') if isinstance(file, dict) else str(getattr(file, 'file', ''))
		if file_path in mediaconvert_lookup:
			job_data = mediaconvert_lookup[file_path]
			if isinstance(file, dict):
				file['converted_file'] = job_data['converted_media_key']
				file['conversion_status'] = job_data['status']
				file['has_hls'] = job_data['status'] == 'completed' and job_data['converted_media_key']
				if file['has_hls']:
					from django.conf import settings
					file['hls_url'] = f"{settings.AWS_S3_CUSTOM_DOMAIN}/{job_data['converted_media_key']}"
			else:
				# For object instances, add as attributes
				setattr(file, 'converted_file', job_data['converted_media_key'])
				setattr(file, 'conversion_status', job_data['status'])
				setattr(file, 'has_hls', job_data['status'] == 'completed' and job_data['converted_media_key'])
				if getattr(file, 'has_hls', False):
					from django.conf import settings
					setattr(file, 'hls_url', f"{settings.AWS_S3_CUSTOM_DOMAIN}/{job_data['converted_media_key']}")

	return files



